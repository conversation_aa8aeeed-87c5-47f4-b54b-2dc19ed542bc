function(check_string_option_value option)
  get_property(expected_values CACHE ${option} PROPERTY STRINGS)
  if(expected_values)
    if(${option} IN_LIST expected_values)
      return()
    endif()
    message(FATAL_ERROR "${option} value is \"${${option}}\", but must be one of ${expected_values}.")
  endif()
  message(AUTHOR_WARNING "The STRINGS property must be set before invoking `check_string_option_value' function.")
endfunction()
