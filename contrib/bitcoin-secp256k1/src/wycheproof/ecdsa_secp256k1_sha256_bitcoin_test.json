{"algorithm": "ECDSA", "schema": "ecdsa_bitcoin_verify_schema.json", "generatorVersion": "0.9rc5", "numberOfTests": 463, "header": ["Test vectors of type EcdsaBitcoinVerify are meant for the verification", "of a ECDSA variant used for bitcoin, that add signature non-malleability."], "notes": {"ArithmeticError": {"bugType": "EDGE_CASE", "description": "Some implementations of ECDSA have arithmetic errors that occur when intermediate results have extreme values. This test vector has been constructed to test such occurences.", "cves": ["CVE-2017-18146"]}, "BerEncodedSignature": {"bugType": "BER_ENCODING", "description": "ECDSA signatures are usually DER encoded. This signature contains valid values for r and s, but it uses alternative BER encoding.", "effect": "Accepting alternative BER encodings may be benign in some cases, or be an issue if protocol requires signature malleability.", "cves": ["CVE-2020-14966", "CVE-2020-13822", "CVE-2019-14859", "CVE-2016-1000342"]}, "EdgeCasePublicKey": {"bugType": "EDGE_CASE", "description": "The test vector uses a special case public key. "}, "EdgeCaseShamirMultiplication": {"bugType": "EDGE_CASE", "description": "<PERSON><PERSON><PERSON> proposed a fast method for computing the sum of two scalar multiplications efficiently. This test vector has been constructed so that an intermediate result is the point at infinity if <PERSON><PERSON><PERSON>'s method is used."}, "IntegerOverflow": {"bugType": "CAN_OF_WORMS", "description": "The test vector contains an r and s that has been modified, so that the original value is restored if the implementation ignores the most significant bits.", "effect": "Without further analysis it is unclear if the modification can be used to forge signatures."}, "InvalidEncoding": {"bugType": "CAN_OF_WORMS", "description": "ECDSA signatures are encoded using ASN.1. This test vector contains an incorrectly encoded signature. The test vector itself was generated from a valid signature by modifying its encoding.", "effect": "Without further analysis it is unclear if the modification can be used to forge signatures."}, "InvalidSignature": {"bugType": "AUTH_BYPASS", "description": "The signature contains special case values such as r=0 and s=0. Buggy implementations may accept such values, if the implementation does not check boundaries and computes s^(-1) == 0.", "effect": "Accepting such signatures can have the effect that an adversary can forge signatures without even knowning the message to sign.", "cves": ["CVE-2022-21449", "CVE-2021-43572", "CVE-2022-24884"]}, "InvalidTypesInSignature": {"bugType": "AUTH_BYPASS", "description": "The signature contains invalid types. Dynamic typed languages sometime coerce such values of different types into integers. If an implementation is careless and has additional bugs, such as not checking integer boundaries then it may be possible that such signatures are accepted.", "effect": "Accepting such signatures can have the effect that an adversary can forge signatures without even knowning the message to sign.", "cves": ["CVE-2022-21449"]}, "ModifiedInteger": {"bugType": "CAN_OF_WORMS", "description": "The test vector contains an r and s that has been modified. The goal is to check for arithmetic errors.", "effect": "Without further analysis it is unclear if the modification can be used to forge signatures."}, "ModifiedSignature": {"bugType": "CAN_OF_WORMS", "description": "The test vector contains an invalid signature that was generated from a valid signature by modifying it.", "effect": "Without further analysis it is unclear if the modification can be used to forge signatures."}, "ModularInverse": {"bugType": "EDGE_CASE", "description": "The test vectors contains a signature where computing the modular inverse of s hits an edge case.", "effect": "While the signature in this test vector is constructed and similar cases are unlikely to occur, it is important to determine if the underlying arithmetic error can be used to forge signatures.", "cves": ["CVE-2019-0865"]}, "PointDuplication": {"bugType": "EDGE_CASE", "description": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission.", "cves": ["2020-12607", "CVE-2015-2730"]}, "RangeCheck": {"bugType": "CAN_OF_WORMS", "description": "The test vector contains an r and s that has been modified. By adding or subtracting the order of the group (or other values) the test vector checks whether signature verification verifies the range of r and s.", "effect": "Without further analysis it is unclear if the modification can be used to forge signatures."}, "SignatureMalleabilityBitcoin": {"bugType": "SIGNATURE_MALLEABILITY", "description": "\"BitCoins\"-curves are curves where signature malleability can be a serious issue. An implementation should only accept a signature s where s < n/2. If an implementation is not meant for uses cases that require signature malleability then this implemenation should be tested with another set of test vectors.", "effect": "In bitcoin exchanges, it may be used to make a double deposits or double withdrawals", "links": ["https://en.bitcoin.it/wiki/Transaction_malleability", "https://en.bitcoinwiki.org/wiki/Transaction_Malleability"]}, "SmallRandS": {"bugType": "EDGE_CASE", "description": "The test vectors contains a signature where both r and s are small integers. Some libraries cannot verify such signatures.", "effect": "While the signature in this test vector is constructed and similar cases are unlikely to occur, it is important to determine if the underlying arithmetic error can be used to forge signatures.", "cves": ["2020-13895"]}, "SpecialCaseHash": {"bugType": "EDGE_CASE", "description": "The test vector contains a signature where the hash of the message is a special case, e.g., contains a long run of 0 or 1 bits."}, "ValidSignature": {"bugType": "BASIC", "description": "The test vector contains a valid signature that was generated pseudorandomly. Such signatures should not fail to verify unless some of the parameters (e.g. curve or hash function) are not supported."}}, "testGroups": [{"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "wx": "00b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6f", "wy": "00f0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEuDj/ROW8F3vyEYnQdmCC/J2EMiaIf8l2\nA3EQC37iCm/wyddb+6ezGmvKGXRJbutW3jVwcZVdg8Sxutqgshgy6Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 1, "comment": "Signature malleability", "flags": ["SignatureMalleabilityBitcoin"], "msg": "313233343030", "sig": "3046022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365022100900e75ad233fcc908509dbff5922647db37c21f4afd3203ae8dc4ae7794b0f87", "result": "invalid"}, {"tcId": 2, "comment": "valid", "flags": ["ValidSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "valid"}, {"tcId": 3, "comment": "length of sequence [r, s] uses long form encoding", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "308145022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 4, "comment": "length of sequence [r, s] contains a leading 0", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "30820045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 5, "comment": "length of sequence [r, s] uses 70 instead of 69", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3046022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 6, "comment": "length of sequence [r, s] uses 68 instead of 69", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3044022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 7, "comment": "uint32 overflow in length of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30850100000045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 8, "comment": "uint64 overflow in length of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3089010000000000000045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 9, "comment": "length of sequence [r, s] = 2**31 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30847fffffff022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 10, "comment": "length of sequence [r, s] = 2**31", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "308480000000022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 11, "comment": "length of sequence [r, s] = 2**32 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3084ffffffff022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 12, "comment": "length of sequence [r, s] = 2**40 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3085ffffffffff022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 13, "comment": "length of sequence [r, s] = 2**64 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3088ffffffffffffffff022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 14, "comment": "incorrect length of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30ff022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 15, "comment": "replaced sequence [r, s] by an indefinite length tag without termination", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 16, "comment": "removing sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "", "result": "invalid"}, {"tcId": 17, "comment": "lonely sequence tag", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30", "result": "invalid"}, {"tcId": 18, "comment": "appending 0's to sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 19, "comment": "prepending 0's to sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30470000022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 20, "comment": "appending unused 0's to sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 21, "comment": "appending null value to sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0500", "result": "invalid"}, {"tcId": 22, "comment": "prepending garbage to sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a4981773045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 23, "comment": "prepending garbage to sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304925003045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 24, "comment": "appending garbage to sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30473045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0004deadbeef", "result": "invalid"}, {"tcId": 25, "comment": "including undefined tags", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "304daa00bb00cd003045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 26, "comment": "including undefined tags", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d2229aa00bb00cd00022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 27, "comment": "including undefined tags", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323652228aa00bb00cd0002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 28, "comment": "truncated length of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3081", "result": "invalid"}, {"tcId": 29, "comment": "including undefined tags to sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "304baa02aabb3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 30, "comment": "using composition with indefinite length for sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30803045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 31, "comment": "using composition with wrong tag for sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30803145022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 32, "comment": "Replacing sequence [r, s] with NULL", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "0500", "result": "invalid"}, {"tcId": 33, "comment": "changing tag value of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "2e45022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 34, "comment": "changing tag value of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "2f45022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 35, "comment": "changing tag value of sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3145022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 36, "comment": "changing tag value of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3245022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 37, "comment": "changing tag value of sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "ff45022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 38, "comment": "dropping value of sequence [r, s]", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3000", "result": "invalid"}, {"tcId": 39, "comment": "using composition for sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304930010230442100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 40, "comment": "truncated sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3044022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31", "result": "invalid"}, {"tcId": 41, "comment": "truncated sequence [r, s]", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30442100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 42, "comment": "sequence [r, s] of size 4166 to check for overflows", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "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", "result": "invalid"}, {"tcId": 43, "comment": "indefinite length", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 44, "comment": "indefinite length with truncated delimiter", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba00", "result": "invalid"}, {"tcId": 45, "comment": "indefinite length with additional element", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba05000000", "result": "invalid"}, {"tcId": 46, "comment": "indefinite length with truncated element", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba060811220000", "result": "invalid"}, {"tcId": 47, "comment": "indefinite length with garbage", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000fe02beef", "result": "invalid"}, {"tcId": 48, "comment": "indefinite length with nonempty EOC", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3080022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0002beef", "result": "invalid"}, {"tcId": 49, "comment": "prepend empty sequence", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30473000022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 50, "comment": "append empty sequence", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba3000", "result": "invalid"}, {"tcId": 51, "comment": "append zero", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3048022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba020100", "result": "invalid"}, {"tcId": 52, "comment": "append garbage with high tag number", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3048022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31babf7f00", "result": "invalid"}, {"tcId": 53, "comment": "append null with explicit tag", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31baa0020500", "result": "invalid"}, {"tcId": 54, "comment": "append null with implicit tag", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31baa000", "result": "invalid"}, {"tcId": 55, "comment": "sequence of sequence", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30473045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 56, "comment": "truncated sequence: removed last 1 elements", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3023022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365", "result": "invalid"}, {"tcId": 57, "comment": "repeating element in sequence", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3067022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 58, "comment": "flipped bit 0 in r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236402206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 59, "comment": "flipped bit 32 in r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccac983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 60, "comment": "flipped bit 48 in r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5133ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 61, "comment": "flipped bit 64 in r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc08b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 62, "comment": "length of r uses long form encoding", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "304602812100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 63, "comment": "length of r contains a leading 0", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "30470282002100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 64, "comment": "length of r uses 34 instead of 33", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022200813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 65, "comment": "length of r uses 32 instead of 33", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 66, "comment": "uint32 overflow in length of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a0285010000002100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 67, "comment": "uint64 overflow in length of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304e028901000000000000002100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 68, "comment": "length of r = 2**31 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304902847fffffff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 69, "comment": "length of r = 2**31", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304902848000000000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 70, "comment": "length of r = 2**32 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30490284ffffffff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 71, "comment": "length of r = 2**40 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a0285ffffffffff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 72, "comment": "length of r = 2**64 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d0288ffffffffffffffff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 73, "comment": "incorrect length of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304502ff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 74, "comment": "replaced r by an indefinite length tag without termination", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045028000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 75, "comment": "removing r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "302202206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 76, "comment": "lonely integer tag", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30230202206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 77, "comment": "lonely integer tag", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3024022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502", "result": "invalid"}, {"tcId": 78, "comment": "appending 0's to r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365000002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 79, "comment": "prepending 0's to r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30470223000000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 80, "comment": "appending unused 0's to r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365000002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 81, "comment": "appending null value to r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022300813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365050002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 82, "comment": "prepending garbage to r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a2226498177022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 83, "comment": "prepending garbage to r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304922252500022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 84, "comment": "appending garbage to r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d2223022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650004deadbeef02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 85, "comment": "truncated length of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3024028102206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 86, "comment": "including undefined tags to r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304b2227aa02aabb022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 87, "comment": "using composition with indefinite length for r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30492280022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365000002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 88, "comment": "using composition with wrong tag for r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "30492280032100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365000002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 89, "comment": "Replacing r with NULL", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3024050002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 90, "comment": "changing tag value of r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045002100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 91, "comment": "changing tag value of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045012100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 92, "comment": "changing tag value of r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045032100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 93, "comment": "changing tag value of r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045042100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 94, "comment": "changing tag value of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045ff2100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 95, "comment": "dropping value of r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3024020002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 96, "comment": "using composition for r", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304922250201000220813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 97, "comment": "modifying first byte of r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022102813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 98, "comment": "modifying last byte of r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323e502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 99, "comment": "truncated r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3044022000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832302206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 100, "comment": "truncated r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30440220813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 101, "comment": "r of size 4130 to check for overflows", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "308210480282102200813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 102, "comment": "leading ff in r", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30460222ff00813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 103, "comment": "replaced r by infinity", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "302509018002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 104, "comment": "replacing r with zero", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "302502010002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 105, "comment": "flipped bit 0 in s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3043022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323656ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31bb", "result": "invalid"}, {"tcId": 106, "comment": "flipped bit 32 in s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3043022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323656ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a456eb31ba", "result": "invalid"}, {"tcId": 107, "comment": "flipped bit 48 in s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3043022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323656ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f713a556eb31ba", "result": "invalid"}, {"tcId": 108, "comment": "flipped bit 64 in s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3043022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323656ff18a52dcc0336f7af62400a6dd9b810732baf1ff758001d6f613a556eb31ba", "result": "invalid"}, {"tcId": 109, "comment": "length of s uses long form encoding", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "3046022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650281206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 110, "comment": "length of s contains a leading 0", "flags": ["BerEncodedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365028200206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 111, "comment": "length of s uses 33 instead of 32", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502216ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 112, "comment": "length of s uses 31 instead of 32", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365021f6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 113, "comment": "uint32 overflow in length of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365028501000000206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 114, "comment": "uint64 overflow in length of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304e022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502890100000000000000206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 115, "comment": "length of s = 2**31 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502847fffffff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 116, "comment": "length of s = 2**31", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650284800000006ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 117, "comment": "length of s = 2**32 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650284ffffffff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 118, "comment": "length of s = 2**40 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650285ffffffffff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 119, "comment": "length of s = 2**64 - 1", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650288ffffffffffffffff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 120, "comment": "incorrect length of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502ff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 121, "comment": "replaced s by an indefinite length tag without termination", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502806ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 122, "comment": "appending 0's to s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502226ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 123, "comment": "prepending 0's to s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365022200006ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 124, "comment": "appending null value to s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3047022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502226ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0500", "result": "invalid"}, {"tcId": 125, "comment": "prepending garbage to s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304a022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365222549817702206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 126, "comment": "prepending garbage to s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323652224250002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 127, "comment": "appending garbage to s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304d022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365222202206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0004deadbeef", "result": "invalid"}, {"tcId": 128, "comment": "truncated length of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3025022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650281", "result": "invalid"}, {"tcId": 129, "comment": "including undefined tags to s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "304b022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323652226aa02aabb02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 130, "comment": "using composition with indefinite length for s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365228002206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 131, "comment": "using composition with wrong tag for s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365228003206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000", "result": "invalid"}, {"tcId": 132, "comment": "Replacing s with NULL", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3025022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650500", "result": "invalid"}, {"tcId": 133, "comment": "changing tag value of s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236500206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 134, "comment": "changing tag value of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236501206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 135, "comment": "changing tag value of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236503206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 136, "comment": "changing tag value of s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236504206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 137, "comment": "changing tag value of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365ff206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 138, "comment": "dropping value of s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3025022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650200", "result": "invalid"}, {"tcId": 139, "comment": "using composition for s", "flags": ["InvalidEncoding"], "msg": "313233343030", "sig": "3049022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365222402016f021ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 140, "comment": "modifying first byte of s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206df18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 141, "comment": "modifying last byte of s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3045022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb313a", "result": "invalid"}, {"tcId": 142, "comment": "truncated s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3044022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365021f6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31", "result": "invalid"}, {"tcId": 143, "comment": "truncated s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3044022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365021ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 144, "comment": "s of size 4129 to check for overflows", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "30821048022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365028210216ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid"}, {"tcId": 145, "comment": "leading ff in s", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3046022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc98323650221ff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 146, "comment": "replaced s by infinity", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3026022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365090180", "result": "invalid"}, {"tcId": 147, "comment": "replacing s with zero", "flags": ["ModifiedSignature"], "msg": "313233343030", "sig": "3026022100813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc9832365020100", "result": "invalid"}, {"tcId": 148, "comment": "replaced r by r + n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "3045022101813ef79ccefa9a56f7ba805f0e478583b90deabca4b05c4574e49b5899b964a602206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 149, "comment": "replaced r by r - n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "30440220813ef79ccefa9a56f7ba805f0e47858643b030ef461f1bcdf53fde3ef94ce22402206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 150, "comment": "replaced r by r + 256 * n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "304602220100813ef79ccefa9a56f7ba805f0e47843fad3bf4853e07f7c98770c99bffc4646502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 151, "comment": "replaced r by -r", "flags": ["ModifiedInteger"], "msg": "313233343030", "sig": "30450221ff7ec10863310565a908457fa0f1b87a7b01a0f22a0a9843f64aedc334367cdc9b02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 152, "comment": "replaced r by n - r", "flags": ["ModifiedInteger"], "msg": "313233343030", "sig": "304402207ec10863310565a908457fa0f1b87a79bc4fcf10b9e0e4320ac021c106b31ddc02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 153, "comment": "replaced r by -n - r", "flags": ["ModifiedInteger"], "msg": "313233343030", "sig": "30450221fe7ec10863310565a908457fa0f1b87a7c46f215435b4fa3ba8b1b64a766469b5a02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 154, "comment": "replaced r by r + 2**256", "flags": ["IntegerOverflow"], "msg": "313233343030", "sig": "3045022101813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 155, "comment": "replaced r by r + 2**320", "flags": ["IntegerOverflow"], "msg": "313233343030", "sig": "304d0229010000000000000000813ef79ccefa9a56f7ba805f0e478584fe5f0dd5f567bc09b5123ccbc983236502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 156, "comment": "replaced s by s + n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "30450221016ff18a52dcc0336f7af62400a6dd9b7fc1e197d8aebe203c96c87232272172fb02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 157, "comment": "replaced s by s - n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "30450221ff6ff18a52dcc0336f7af62400a6dd9b824c83de0b502cdfc51723b51886b4f07902206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 158, "comment": "replaced s by s + 256 * n", "flags": ["RangeCheck"], "msg": "313233343030", "sig": "3046022201006ff18a52dcc0336f7af62400a6dd9a3bb60fa1a14815bbc0a954a0758d2c72ba02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 159, "comment": "replaced s by -s", "flags": ["ModifiedInteger"], "msg": "313233343030", "sig": "30440220900e75ad233fcc908509dbff5922647ef8cd450e008a7fff2909ec5aa914ce4602206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 160, "comment": "replaced s by -n - s", "flags": ["ModifiedInteger"], "msg": "313233343030", "sig": "30450221fe900e75ad233fcc908509dbff592264803e1e68275141dfc369378dcdd8de8d0502206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 161, "comment": "replaced s by s + 2**256", "flags": ["IntegerOverflow"], "msg": "313233343030", "sig": "30450221016ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 162, "comment": "replaced s by s - 2**256", "flags": ["IntegerOverflow"], "msg": "313233343030", "sig": "30450221ff6ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 163, "comment": "replaced s by s + 2**320", "flags": ["IntegerOverflow"], "msg": "313233343030", "sig": "304d02290100000000000000006ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba02206ff18a52dcc0336f7af62400a6dd9b810732baf1ff758000d6f613a556eb31ba", "result": "invalid"}, {"tcId": 164, "comment": "Signature with special case values r=0 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3006020100020100", "result": "invalid"}, {"tcId": 165, "comment": "Signature with special case values r=0 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3006020100020101", "result": "invalid"}, {"tcId": 166, "comment": "Signature with special case values r=0 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid"}, {"tcId": 167, "comment": "Signature with special case values r=0 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 168, "comment": "Signature with special case values r=0 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 169, "comment": "Signature with special case values r=0 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 170, "comment": "Signature with special case values r=0 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 171, "comment": "Signature with special case values r=0 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 172, "comment": "Signature with special case values r=1 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3006020101020100", "result": "invalid"}, {"tcId": 173, "comment": "Signature with special case values r=1 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3006020101020101", "result": "invalid"}, {"tcId": 174, "comment": "Signature with special case values r=1 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid"}, {"tcId": 175, "comment": "Signature with special case values r=1 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 176, "comment": "Signature with special case values r=1 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 177, "comment": "Signature with special case values r=1 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 178, "comment": "Signature with special case values r=1 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 179, "comment": "Signature with special case values r=1 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 180, "comment": "Signature with special case values r=-1 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid"}, {"tcId": 181, "comment": "Signature with special case values r=-1 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid"}, {"tcId": 182, "comment": "Signature with special case values r=-1 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid"}, {"tcId": 183, "comment": "Signature with special case values r=-1 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 184, "comment": "Signature with special case values r=-1 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 185, "comment": "Signature with special case values r=-1 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 186, "comment": "Signature with special case values r=-1 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 187, "comment": "Signature with special case values r=-1 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 188, "comment": "Signature with special case values r=n and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020100", "result": "invalid"}, {"tcId": 189, "comment": "Signature with special case values r=n and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020101", "result": "invalid"}, {"tcId": 190, "comment": "Signature with special case values r=n and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410201ff", "result": "invalid"}, {"tcId": 191, "comment": "Signature with special case values r=n and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 192, "comment": "Signature with special case values r=n and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 193, "comment": "Signature with special case values r=n and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 194, "comment": "Signature with special case values r=n and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 195, "comment": "Signature with special case values r=n and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 196, "comment": "Signature with special case values r=n - 1 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020100", "result": "invalid"}, {"tcId": 197, "comment": "Signature with special case values r=n - 1 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020101", "result": "invalid"}, {"tcId": 198, "comment": "Signature with special case values r=n - 1 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641400201ff", "result": "invalid"}, {"tcId": 199, "comment": "Signature with special case values r=n - 1 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 200, "comment": "Signature with special case values r=n - 1 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 201, "comment": "Signature with special case values r=n - 1 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 202, "comment": "Signature with special case values r=n - 1 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 203, "comment": "Signature with special case values r=n - 1 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 204, "comment": "Signature with special case values r=n + 1 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020100", "result": "invalid"}, {"tcId": 205, "comment": "Signature with special case values r=n + 1 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020101", "result": "invalid"}, {"tcId": 206, "comment": "Signature with special case values r=n + 1 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420201ff", "result": "invalid"}, {"tcId": 207, "comment": "Signature with special case values r=n + 1 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 208, "comment": "Signature with special case values r=n + 1 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 209, "comment": "Signature with special case values r=n + 1 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 210, "comment": "Signature with special case values r=n + 1 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 211, "comment": "Signature with special case values r=n + 1 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 212, "comment": "Signature with special case values r=p and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020100", "result": "invalid"}, {"tcId": 213, "comment": "Signature with special case values r=p and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020101", "result": "invalid"}, {"tcId": 214, "comment": "Signature with special case values r=p and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0201ff", "result": "invalid"}, {"tcId": 215, "comment": "Signature with special case values r=p and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 216, "comment": "Signature with special case values r=p and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 217, "comment": "Signature with special case values r=p and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 218, "comment": "Signature with special case values r=p and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 219, "comment": "Signature with special case values r=p and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 220, "comment": "Signature with special case values r=p + 1 and s=0", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020100", "result": "invalid"}, {"tcId": 221, "comment": "Signature with special case values r=p + 1 and s=1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020101", "result": "invalid"}, {"tcId": 222, "comment": "Signature with special case values r=p + 1 and s=-1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc300201ff", "result": "invalid"}, {"tcId": 223, "comment": "Signature with special case values r=p + 1 and s=n", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid"}, {"tcId": 224, "comment": "Signature with special case values r=p + 1 and s=n - 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid"}, {"tcId": 225, "comment": "Signature with special case values r=p + 1 and s=n + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid"}, {"tcId": 226, "comment": "Signature with special case values r=p + 1 and s=p", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid"}, {"tcId": 227, "comment": "Signature with special case values r=p + 1 and s=p + 1", "flags": ["InvalidSignature"], "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid"}, {"tcId": 228, "comment": "Signature encoding contains incorrect types: r=0, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid"}, {"tcId": 229, "comment": "Signature encoding contains incorrect types: r=0, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020100090142", "result": "invalid"}, {"tcId": 230, "comment": "Signature encoding contains incorrect types: r=0, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020100010101", "result": "invalid"}, {"tcId": 231, "comment": "Signature encoding contains incorrect types: r=0, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020100010100", "result": "invalid"}, {"tcId": 232, "comment": "Signature encoding contains incorrect types: r=0, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201000500", "result": "invalid"}, {"tcId": 233, "comment": "Signature encoding contains incorrect types: r=0, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201000c00", "result": "invalid"}, {"tcId": 234, "comment": "Signature encoding contains incorrect types: r=0, s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201000c0130", "result": "invalid"}, {"tcId": 235, "comment": "Signature encoding contains incorrect types: r=0, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201003000", "result": "invalid"}, {"tcId": 236, "comment": "Signature encoding contains incorrect types: r=0, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30080201003003020100", "result": "invalid"}, {"tcId": 237, "comment": "Signature encoding contains incorrect types: r=1, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid"}, {"tcId": 238, "comment": "Signature encoding contains incorrect types: r=1, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020101090142", "result": "invalid"}, {"tcId": 239, "comment": "Signature encoding contains incorrect types: r=1, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020101010101", "result": "invalid"}, {"tcId": 240, "comment": "Signature encoding contains incorrect types: r=1, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006020101010100", "result": "invalid"}, {"tcId": 241, "comment": "Signature encoding contains incorrect types: r=1, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201010500", "result": "invalid"}, {"tcId": 242, "comment": "Signature encoding contains incorrect types: r=1, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201010c00", "result": "invalid"}, {"tcId": 243, "comment": "Signature encoding contains incorrect types: r=1, s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid"}, {"tcId": 244, "comment": "Signature encoding contains incorrect types: r=1, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201013000", "result": "invalid"}, {"tcId": 245, "comment": "Signature encoding contains incorrect types: r=1, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid"}, {"tcId": 246, "comment": "Signature encoding contains incorrect types: r=-1, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid"}, {"tcId": 247, "comment": "Signature encoding contains incorrect types: r=-1, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid"}, {"tcId": 248, "comment": "Signature encoding contains incorrect types: r=-1, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201ff010101", "result": "invalid"}, {"tcId": 249, "comment": "Signature encoding contains incorrect types: r=-1, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201ff010100", "result": "invalid"}, {"tcId": 250, "comment": "Signature encoding contains incorrect types: r=-1, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201ff0500", "result": "invalid"}, {"tcId": 251, "comment": "Signature encoding contains incorrect types: r=-1, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201ff0c00", "result": "invalid"}, {"tcId": 252, "comment": "Signature encoding contains incorrect types: r=-1, s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060201ff0c0130", "result": "invalid"}, {"tcId": 253, "comment": "Signature encoding contains incorrect types: r=-1, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050201ff3000", "result": "invalid"}, {"tcId": 254, "comment": "Signature encoding contains incorrect types: r=-1, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30080201ff3003020100", "result": "invalid"}, {"tcId": 255, "comment": "Signature encoding contains incorrect types: r=n, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090380fe01", "result": "invalid"}, {"tcId": 256, "comment": "Signature encoding contains incorrect types: r=n, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090142", "result": "invalid"}, {"tcId": 257, "comment": "Signature encoding contains incorrect types: r=n, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141010101", "result": "invalid"}, {"tcId": 258, "comment": "Signature encoding contains incorrect types: r=n, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141010100", "result": "invalid"}, {"tcId": 259, "comment": "Signature encoding contains incorrect types: r=n, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410500", "result": "invalid"}, {"tcId": 260, "comment": "Signature encoding contains incorrect types: r=n, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410c00", "result": "invalid"}, {"tcId": 261, "comment": "Signature encoding contains incorrect types: r=n, s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410c0130", "result": "invalid"}, {"tcId": 262, "comment": "Signature encoding contains incorrect types: r=n, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641413000", "result": "invalid"}, {"tcId": 263, "comment": "Signature encoding contains incorrect types: r=n, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641413003020100", "result": "invalid"}, {"tcId": 264, "comment": "Signature encoding contains incorrect types: r=p, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090380fe01", "result": "invalid"}, {"tcId": 265, "comment": "Signature encoding contains incorrect types: r=p, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090142", "result": "invalid"}, {"tcId": 266, "comment": "Signature encoding contains incorrect types: r=p, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f010101", "result": "invalid"}, {"tcId": 267, "comment": "Signature encoding contains incorrect types: r=p, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f010100", "result": "invalid"}, {"tcId": 268, "comment": "Signature encoding contains incorrect types: r=p, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0500", "result": "invalid"}, {"tcId": 269, "comment": "Signature encoding contains incorrect types: r=p, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0c00", "result": "invalid"}, {"tcId": 270, "comment": "Signature encoding contains incorrect types: r=p, s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0c0130", "result": "invalid"}, {"tcId": 271, "comment": "Signature encoding contains incorrect types: r=p, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3025022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f3000", "result": "invalid"}, {"tcId": 272, "comment": "Signature encoding contains incorrect types: r=p, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f3003020100", "result": "invalid"}, {"tcId": 273, "comment": "Signature encoding contains incorrect types: r=0.25, s=0.25", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "300a090380fe01090380fe01", "result": "invalid"}, {"tcId": 274, "comment": "Signature encoding contains incorrect types: r=nan, s=nan", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006090142090142", "result": "invalid"}, {"tcId": 275, "comment": "Signature encoding contains incorrect types: r=True, s=True", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006010101010101", "result": "invalid"}, {"tcId": 276, "comment": "Signature encoding contains incorrect types: r=False, s=False", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006010100010100", "result": "invalid"}, {"tcId": 277, "comment": "Signature encoding contains incorrect types: r=Null, s=Null", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "300405000500", "result": "invalid"}, {"tcId": 278, "comment": "Signature encoding contains incorrect types: r=empyt UTF-8 string, s=empyt UTF-8 string", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30040c000c00", "result": "invalid"}, {"tcId": 279, "comment": "Signature encoding contains incorrect types: r=\"0\", s=\"0\"", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060c01300c0130", "result": "invalid"}, {"tcId": 280, "comment": "Signature encoding contains incorrect types: r=empty list, s=empty list", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "300430003000", "result": "invalid"}, {"tcId": 281, "comment": "Signature encoding contains incorrect types: r=list containing 0, s=list containing 0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "300a30030201003003020100", "result": "invalid"}, {"tcId": 282, "comment": "Signature encoding contains incorrect types: r=0.25, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3008090380fe01020100", "result": "invalid"}, {"tcId": 283, "comment": "Signature encoding contains incorrect types: r=nan, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006090142020100", "result": "invalid"}, {"tcId": 284, "comment": "Signature encoding contains incorrect types: r=True, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006010101020100", "result": "invalid"}, {"tcId": 285, "comment": "Signature encoding contains incorrect types: r=False, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "3006010100020100", "result": "invalid"}, {"tcId": 286, "comment": "Signature encoding contains incorrect types: r=Null, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050500020100", "result": "invalid"}, {"tcId": 287, "comment": "Signature encoding contains incorrect types: r=empyt UTF-8 string, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30050c00020100", "result": "invalid"}, {"tcId": 288, "comment": "Signature encoding contains incorrect types: r=\"0\", s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30060c0130020100", "result": "invalid"}, {"tcId": 289, "comment": "Signature encoding contains incorrect types: r=empty list, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30053000020100", "result": "invalid"}, {"tcId": 290, "comment": "Signature encoding contains incorrect types: r=list containing 0, s=0", "flags": ["InvalidTypesInSignature"], "msg": "313233343030", "sig": "30083003020100020100", "result": "invalid"}, {"tcId": 291, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "flags": ["EdgeCaseShamirMultiplication"], "msg": "3235353835", "sig": "3045022100dd1b7d09a7bd8218961034a39a87fecf5314f00c4d25eb58a07ac85e85eab516022035138c401ef8d3493d65c9002fe62b43aee568731b744548358996d9cc427e06", "result": "valid"}, {"tcId": 292, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "343236343739373234", "sig": "304502210095c29267d972a043d955224546222bba343fc1d4db0fec262a33ac61305696ae02206edfe96713aed56f8a28a6653f57e0b829712e5eddc67f34682b24f0676b2640", "result": "valid"}, {"tcId": 293, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "37313338363834383931", "sig": "3044022028f94a894e92024699e345fe66971e3edcd050023386135ab3939d550898fb25022032963e5bd41fa5911ed8f37deb86dae0a762bb6121c894615083c5d95ea01db3", "result": "valid"}, {"tcId": 294, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3130333539333331363638", "sig": "3045022100be26b18f9549f89f411a9b52536b15aa270b84548d0e859a1952a27af1a77ac6022070c1d4fa9cd03cc8eaa8d506edb97eed7b8358b453c88aefbb880a3f0e8d472f", "result": "valid"}, {"tcId": 295, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33393439343031323135", "sig": "3045022100b1a4b1478e65cc3eafdf225d1298b43f2da19e4bcff7eacc0a2e98cd4b74b1140220179aa31e304cc142cf5073171751b28f3f5e0fa88c994e7c55f1bc07b8d56c16", "result": "valid"}, {"tcId": 296, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31333434323933303739", "sig": "30440220325332021261f1bd18f2712aa1e2252da23796da8a4b1ff6ea18cafec7e171f2022040b4f5e287ee61fc3c804186982360891eaa35c75f05a43ecd48b35d984a6648", "result": "valid"}, {"tcId": 297, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33373036323131373132", "sig": "3045022100a23ad18d8fc66d81af0903890cbd453a554cb04cdc1a8ca7f7f78e5367ed88a0022023e3eb2ce1c04ea748c389bd97374aa9413b9268851c04dcd9f88e78813fee56", "result": "valid"}, {"tcId": 298, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "333433363838373132", "sig": "304402202bdea41cda63a2d14bf47353bd20880a690901de7cd6e3cc6d8ed5ba0cdb109102203cea66bccfc9f9bf8c7ca4e1c1457cc9145e13e936d90b3d9c7786b8b26cf4c7", "result": "valid"}, {"tcId": 299, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31333531353330333730", "sig": "3045022100d7cd76ec01c1b1079eba9e2aa2a397243c4758c98a1ba0b7404a340b9b00ced602203575001e19d922e6de8b3d6c84ea43b5c3338106cf29990134e7669a826f78e6", "result": "valid"}, {"tcId": 300, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "36353533323033313236", "sig": "3045022100a872c744d936db21a10c361dd5c9063355f84902219652f6fc56dc95a7139d960220400df7575d9756210e9ccc77162c6b593c7746cfb48ac263c42750b421ef4bb9", "result": "valid"}, {"tcId": 301, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31353634333436363033", "sig": "30450221009fa9afe07752da10b36d3afcd0fe44bfc40244d75203599cf8f5047fa3453854022050e0a7c013bfbf51819736972d44b4b56bc2a2b2c180df6ec672df171410d77a", "result": "valid"}, {"tcId": 302, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "34343239353339313137", "sig": "3045022100885640384d0d910efb177b46be6c3dc5cac81f0b88c3190bb6b5f99c2641f2050220738ed9bff116306d9caa0f8fc608be243e0b567779d8dab03e8e19d553f1dc8e", "result": "valid"}, {"tcId": 303, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3130393533323631333531", "sig": "304402202d051f91c5a9d440c5676985710483bc4f1a6c611b10c95a2ff0363d90c2a45802206ddf94e6fba5be586833d0c53cf216ad3948f37953c26c1cf4968e9a9e8243dc", "result": "valid"}, {"tcId": 304, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "35393837333530303431", "sig": "3045022100f3ac2523967482f53d508522712d583f4379cd824101ff635ea0935117baa54f022027f10812227397e02cea96fb0e680761636dab2b080d1fc5d11685cbe8500cfe", "result": "valid"}, {"tcId": 305, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33343633303036383738", "sig": "304502210096447cf68c3ab7266ed7447de3ac52fed7cc08cbdfea391c18a9b8ab370bc91302200f5e7874d3ac0e918f01c885a1639177c923f8660d1ceba1ca1f301bc675cdbc", "result": "valid"}, {"tcId": 306, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "39383137333230323837", "sig": "30440220530a0832b691da0b5619a0b11de6877f3c0971baaa68ed122758c29caaf46b7202206c89e44f5eb33060ea4b46318c39138eaedec72de42ba576579a6a4690e339f3", "result": "valid"}, {"tcId": 307, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33323232303431303436", "sig": "30450221009c54c25500bde0b92d72d6ec483dc2482f3654294ca74de796b681255ed58a770220677453c6b56f527631c9f67b3f3eb621fd88582b4aff156d2f1567d6211a2a33", "result": "valid"}, {"tcId": 308, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "36363636333037313034", "sig": "3045022100e7909d41439e2f6af29136c7348ca2641a2b070d5b64f91ea9da7070c7a2618b022042d782f132fa1d36c2c88ba27c3d678d80184a5d1eccac7501f0b47e3d205008", "result": "valid"}, {"tcId": 309, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31303335393531383938", "sig": "304402205924873209593135a4c3da7bb381227f8a4b6aa9f34fe5bb7f8fbc131a039ffe02201f1bb11b441c8feaa40f44213d9a405ed792d59fb49d5bcdd9a4285ae5693022", "result": "valid"}, {"tcId": 310, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31383436353937313935", "sig": "3045022100eeb692c9b262969b231c38b5a7f60649e0c875cd64df88f33aa571fa3d29ab0e0220218b3a1eb06379c2c18cf51b06430786d1c64cd2d24c9b232b23e5bac7989acd", "result": "valid"}, {"tcId": 311, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33313336303436313839", "sig": "3045022100a40034177f36091c2b653684a0e3eb5d4bff18e4d09f664c2800e7cafda1daf802203a3ec29853704e52031c58927a800a968353adc3d973beba9172cbbeab4dd149", "result": "valid"}, {"tcId": 312, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "32363633373834323534", "sig": "3045022100b5d795cc75cea5c434fa4185180cd6bd21223f3d5a86da6670d71d95680dadbf022054e4d8810a001ecbb9f7ca1c2ebfdb9d009e9031a431aca3c20ab4e0d1374ec1", "result": "valid"}, {"tcId": 313, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31363532313030353234", "sig": "3044022007dc2478d43c1232a4595608c64426c35510051a631ae6a5a6eb1161e57e42e102204a59ea0fdb72d12165cea3bf1ca86ba97517bd188db3dbd21a5a157850021984", "result": "valid"}, {"tcId": 314, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "35373438303831363936", "sig": "3045022100ddd20c4a05596ca868b558839fce9f6511ddd83d1ccb53f82e5269d559a0155202205b91734729d93093ff22123c4a25819d7feb66a250663fc780cb66fc7b6e6d17", "result": "valid"}, {"tcId": 315, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "36333433393133343638", "sig": "30450221009cde6e0ede0a003f02fda0a01b59facfe5dec063318f279ce2de7a9b1062f7b702202886a5b8c679bdf8224c66f908fd6205492cb70b0068d46ae4f33a4149b12a52", "result": "valid"}, {"tcId": 316, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31353431313033353938", "sig": "3045022100c5771016d0dd6357143c89f684cd740423502554c0c59aa8c99584f1ff38f609022054b405f4477546686e464c5463b4fd4190572e58d0f7e7357f6e61947d20715c", "result": "valid"}, {"tcId": 317, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3130343738353830313238", "sig": "3045022100a24ebc0ec224bd67ae397cbe6fa37b3125adbd34891abe2d7c7356921916dfe6022034f6eb6374731bbbafc4924fb8b0bdcdda49456d724cdae6178d87014cb53d8c", "result": "valid"}, {"tcId": 318, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3130353336323835353638", "sig": "304402202557d64a7aee2e0931c012e4fea1cd3a2c334edae68cdeb7158caf21b68e5a2402207f06cdbb6a90023a973882ed97b080fe6b05af3ec93db6f1a4399a69edf7670d", "result": "valid"}, {"tcId": 319, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "393533393034313035", "sig": "3045022100c4f2eccbb6a24350c8466450b9d61b207ee359e037b3dcedb42a3f2e6dd6aeb502203263c6b59a2f55cdd1c6e14894d5e5963b28bc3e2469ac9ba1197991ca7ff9c7", "result": "valid"}, {"tcId": 320, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "393738383438303339", "sig": "3045022100eff04781c9cbcd162d0a25a6e2ebcca43506c523385cb515d49ea38a1b12fcad022015acd73194c91a95478534f23015b672ebed213e45424dd2c8e26ac8b3eb34a5", "result": "valid"}, {"tcId": 321, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33363130363732343432", "sig": "3045022100f58b4e3110a64bf1b5db97639ee0e5a9c8dfa49dc59b679891f520fdf0584c8702202cd8fe51888aee9db3e075440fd4db73b5c732fb87b510e97093d66415f62af7", "result": "valid"}, {"tcId": 322, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31303534323430373035", "sig": "3045022100f8abecaa4f0c502de4bf5903d48417f786bf92e8ad72fec0bd7fcb7800c0bbe302204c7f9e231076a30b7ae36b0cebe69ccef1cd194f7cce93a5588fd6814f437c0e", "result": "valid"}, {"tcId": 323, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "35313734343438313937", "sig": "304402205d5b38bd37ad498b2227a633268a8cca879a5c7c94a4e416bd0a614d09e606d2022012b8d664ea9991062ecbb834e58400e25c46007af84f6007d7f1685443269afe", "result": "valid"}, {"tcId": 324, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31393637353631323531", "sig": "304402200c1cd9fe4034f086a2b52d65b9d3834d72aebe7f33dfe8f976da82648177d8e3022013105782e3d0cfe85c2778dec1a848b27ac0ae071aa6da341a9553a946b41e59", "result": "valid"}, {"tcId": 325, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33343437323533333433", "sig": "3045022100ae7935fb96ff246b7b5d5662870d1ba587b03d6e1360baf47988b5c02ccc1a5b02205f00c323272083782d4a59f2dfd65e49de0693627016900ef7e61428056664b3", "result": "valid"}, {"tcId": 326, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "333638323634333138", "sig": "3044022000a134b5c6ccbcefd4c882b945baeb4933444172795fa6796aae1490675470980220566e46105d24d890151e3eea3ebf88f5b92b3f5ec93a217765a6dcbd94f2c55b", "result": "valid"}, {"tcId": 327, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33323631313938363038", "sig": "304402202e4721363ad3992c139e5a1c26395d2c2d777824aa24fde075e0d7381171309d0220740f7c494418e1300dd4512f782a58800bff6a7abdfdd20fbbd4f05515ca1a4f", "result": "valid"}, {"tcId": 328, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "39363738373831303934", "sig": "304402206852e9d3cd9fe373c2d504877967d365ab1456707b6817a042864694e1960ccf0220064b27ea142b30887b84c86adccb2fa39a6911ad21fc7e819f593be52bc4f3bd", "result": "valid"}, {"tcId": 329, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "34393538383233383233", "sig": "30440220188a8c5648dc79eace158cf886c62b5468f05fd95f03a7635c5b4c31f09af4c5022036361a0b571a00c6cd5e686ccbfcfa703c4f97e48938346d0c103fdc76dc5867", "result": "valid"}, {"tcId": 330, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "383234363337383337", "sig": "3045022100a74f1fb9a8263f62fc4416a5b7d584f4206f3996bb91f6fc8e73b9e92bad0e1302206815032e8c7d76c3ab06a86f33249ce9940148cb36d1f417c2e992e801afa3fa", "result": "valid"}, {"tcId": 331, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3131303230383333373736", "sig": "3044022007244865b72ff37e62e3146f0dc14682badd7197799135f0b00ade7671742bfe02200d80c2238edb4e4a7a86a8c57ca9af1711f406f7f5da0299aa04e2932d960754", "result": "valid"}, {"tcId": 332, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "313333383731363438", "sig": "3045022100da7fdd05b5badabd619d805c4ee7d9a84f84ddd5cf9c5bf4d4338140d689ef08022028f1cf4fa1c3c5862cfa149c0013cf5fe6cf5076cae000511063e7de25bb38e5", "result": "valid"}, {"tcId": 333, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "333232313434313632", "sig": "3045022100d3027c656f6d4fdfd8ede22093e3c303b0133c340d615e7756f6253aea927238022009aef060c8e4cef972974011558df144fed25ca69ae8d0b2eaf1a8feefbec417", "result": "valid"}, {"tcId": 334, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3130363836363535353436", "sig": "304402200bf6c0188dc9571cd0e21eecac5fbb19d2434988e9cc10244593ef3a98099f6902204864a562661f9221ec88e3dd0bc2f6e27ac128c30cc1a80f79ec670a22b042ee", "result": "valid"}, {"tcId": 335, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "3632313535323436", "sig": "3045022100ae459640d5d1179be47a47fa538e16d94ddea5585e7a244804a51742c686443a02206c8e30e530a634fae80b3ceb062978b39edbe19777e0a24553b68886181fd897", "result": "valid"}, {"tcId": 336, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "37303330383138373734", "sig": "304402201cf3517ba3bf2ab8b9ead4ebb6e866cb88a1deacb6a785d3b63b483ca02ac4950220249a798b73606f55f5f1c70de67cb1a0cff95d7dc50b3a617df861bad3c6b1c9", "result": "valid"}, {"tcId": 337, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "35393234353233373434", "sig": "3045022100e69b5238265ea35d77e4dd172288d8cea19810a10292617d5976519dc5757cb802204b03c5bc47e826bdb27328abd38d3056d77476b2130f3df6ec4891af08ba1e29", "result": "valid"}, {"tcId": 338, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31343935353836363231", "sig": "304402205f9d7d7c870d085fc1d49fff69e4a275812800d2cf8973e7325866cb40fa2b6f02206d1f5491d9f717a597a15fd540406486d76a44697b3f0d9d6dcef6669f8a0a56", "result": "valid"}, {"tcId": 339, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "34303035333134343036", "sig": "304402200a7d5b1959f71df9f817146ee49bd5c89b431e7993e2fdecab6858957da685ae02200f8aad2d254690bdc13f34a4fec44a02fd745a422df05ccbb54635a8b86b9609", "result": "valid"}, {"tcId": 340, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "33303936343537353132", "sig": "3044022079e88bf576b74bc07ca142395fda28f03d3d5e640b0b4ff0752c6d94cd553408022032cea05bd2d706c8f6036a507e2ab7766004f0904e2e5c5862749c0073245d6a", "result": "valid"}, {"tcId": 341, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "32373834303235363230", "sig": "30450221009d54e037a00212b377bc8874798b8da080564bbdf7e07591b861285809d01488022018b4e557667a82bd95965f0706f81a29243fbdd86968a7ebeb43069db3b18c7f", "result": "valid"}, {"tcId": 342, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "32363138373837343138", "sig": "304402202664f1ffa982fedbcc7cab1b8bc6e2cb420218d2a6077ad08e591ba9feab33bd022049f5c7cb515e83872a3d41b4cdb85f242ad9d61a5bfc01debfbb52c6c84ba728", "result": "valid"}, {"tcId": 343, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "31363432363235323632", "sig": "304402205827518344844fd6a7de73cbb0a6befdea7b13d2dee4475317f0f18ffc81524b02204f5ccb4e0b488b5a5d760aacddb2d791970fe43da61eb30e2e90208a817e46db", "result": "valid"}, {"tcId": 344, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "36383234313839343336", "sig": "304502210097ab19bd139cac319325869218b1bce111875d63fb12098a04b0cd59b6fdd3a30220431d9cea3a243847303cebda56476431d034339f31d785ee8852db4f040d4921", "result": "valid"}, {"tcId": 345, "comment": "special case hash", "flags": ["SpecialCaseHash"], "msg": "343834323435343235", "sig": "3044022052c683144e44119ae2013749d4964ef67509278f6d38ba869adcfa69970e123d02203479910167408f45bda420a626ec9c4ec711c1274be092198b4187c018b562ca", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0407310f90a9eae149a08402f54194a0f7b4ac427bf8d9bd6c7681071dc47dc36226a6d37ac46d61fd600c0bf1bff87689ed117dda6b0e59318ae010a197a26ca0", "wx": "07310f90a9eae149a08402f54194a0f7b4ac427bf8d9bd6c7681071dc47dc362", "wy": "26a6d37ac46d61fd600c0bf1bff87689ed117dda6b0e59318ae010a197a26ca0"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000407310f90a9eae149a08402f54194a0f7b4ac427bf8d9bd6c7681071dc47dc36226a6d37ac46d61fd600c0bf1bff87689ed117dda6b0e59318ae010a197a26ca0", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBzEPkKnq4UmghAL1QZSg97SsQnv42b1s\ndoEHHcR9w2ImptN6xG1h/WAMC/G/+HaJ7RF92msOWTGK4BChl6JsoA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 346, "comment": "k*G has a large x-coordinate", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "30160211014551231950b75fc4402da1722fc9baeb020103", "result": "valid"}, {"tcId": 347, "comment": "r too large", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2c020103", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04bc97e7585eecad48e16683bc4091708e1a930c683fc47001d4b383594f2c4e22705989cf69daeadd4e4e4b8151ed888dfec20fb01728d89d56b3f38f2ae9c8c5", "wx": "00bc97e7585eecad48e16683bc4091708e1a930c683fc47001d4b383594f2c4e22", "wy": "705989cf69daeadd4e4e4b8151ed888dfec20fb01728d89d56b3f38f2ae9c8c5"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004bc97e7585eecad48e16683bc4091708e1a930c683fc47001d4b383594f2c4e22705989cf69daeadd4e4e4b8151ed888dfec20fb01728d89d56b3f38f2ae9c8c5", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEvJfnWF7srUjhZoO8QJFwjhqTDGg/xHAB\n1LODWU8sTiJwWYnPadrq3U5OS4FR7YiN/sIPsBco2J1Ws/OPKunIxQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 348, "comment": "r,s are large", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413f020103", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0444ad339afbc21e9abf7b602a5ca535ea378135b6d10d81310bdd8293d1df3252b63ff7d0774770f8fe1d1722fa83acd02f434e4fc110a0cc8f6dddd37d56c463", "wx": "44ad339afbc21e9abf7b602a5ca535ea378135b6d10d81310bdd8293d1df3252", "wy": "00b63ff7d0774770f8fe1d1722fa83acd02f434e4fc110a0cc8f6dddd37d56c463"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000444ad339afbc21e9abf7b602a5ca535ea378135b6d10d81310bdd8293d1df3252b63ff7d0774770f8fe1d1722fa83acd02f434e4fc110a0cc8f6dddd37d56c463", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERK0zmvvCHpq/e2AqXKU16jeBNbbRDYEx\nC92Ck9HfMlK2P/fQd0dw+P4dFyL6g6zQL0NOT8EQoMyPbd3TfVbEYw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 349, "comment": "r and s^-1 have a large Hamming weight", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203e9a7582886089c62fb840cf3b83061cd1cff3ae4341808bb5bdee6191174177", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "041260c2122c9e244e1af5151bede0c3ae23b54d7c596881d3eebad21f37dd878c5c9a0c1a9ade76737a8811bd6a7f9287c978ee396aa89c11e47229d2ccb552f0", "wx": "1260c2122c9e244e1af5151bede0c3ae23b54d7c596881d3eebad21f37dd878c", "wy": "5c9a0c1a9ade76737a8811bd6a7f9287c978ee396aa89c11e47229d2ccb552f0"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200041260c2122c9e244e1af5151bede0c3ae23b54d7c596881d3eebad21f37dd878c5c9a0c1a9ade76737a8811bd6a7f9287c978ee396aa89c11e47229d2ccb552f0", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEEmDCEiyeJE4a9RUb7eDDriO1TXxZaIHT\n7rrSHzfdh4xcmgwamt52c3qIEb1qf5KHyXjuOWqonBHkcinSzLVS8A==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 350, "comment": "r and s^-1 have a large Hamming weight", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022024238e70b431b1a64efdf9032669939d4b77f249503fc6905feb7540dea3e6d2", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "041877045be25d34a1d0600f9d5c00d0645a2a54379b6ceefad2e6bf5c2a3352ce821a532cc1751ee1d36d41c3d6ab4e9b143e44ec46d73478ea6a79a5c0e54159", "wx": "1877045be25d34a1d0600f9d5c00d0645a2a54379b6ceefad2e6bf5c2a3352ce", "wy": "00821a532cc1751ee1d36d41c3d6ab4e9b143e44ec46d73478ea6a79a5c0e54159"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200041877045be25d34a1d0600f9d5c00d0645a2a54379b6ceefad2e6bf5c2a3352ce821a532cc1751ee1d36d41c3d6ab4e9b143e44ec46d73478ea6a79a5c0e54159", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGHcEW+JdNKHQYA+dXADQZFoqVDebbO76\n0ua/XCozUs6CGlMswXUe4dNtQcPWq06bFD5E7EbXNHjqanmlwOVBWQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 351, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020101020101", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04455439fcc3d2deeceddeaece60e7bd17304f36ebb602adf5a22e0b8f1db46a50aec38fb2baf221e9a8d1887c7bf6222dd1834634e77263315af6d23609d04f77", "wx": "455439fcc3d2deeceddeaece60e7bd17304f36ebb602adf5a22e0b8f1db46a50", "wy": "00aec38fb2baf221e9a8d1887c7bf6222dd1834634e77263315af6d23609d04f77"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004455439fcc3d2deeceddeaece60e7bd17304f36ebb602adf5a22e0b8f1db46a50aec38fb2baf221e9a8d1887c7bf6222dd1834634e77263315af6d23609d04f77", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERVQ5/MPS3uzt3q7OYOe9FzBPNuu2Aq31\noi4Ljx20alCuw4+yuvIh6ajRiHx79iIt0YNGNOdyYzFa9tI2CdBPdw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 352, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020101020102", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "042e1f466b024c0c3ace2437de09127fed04b706f94b19a21bb1c2acf35cece7180449ae3523d72534e964972cfd3b38af0bddd9619e5af223e4d1a40f34cf9f1d", "wx": "2e1f466b024c0c3ace2437de09127fed04b706f94b19a21bb1c2acf35cece718", "wy": "0449ae3523d72534e964972cfd3b38af0bddd9619e5af223e4d1a40f34cf9f1d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200042e1f466b024c0c3ace2437de09127fed04b706f94b19a21bb1c2acf35cece7180449ae3523d72534e964972cfd3b38af0bddd9619e5af223e4d1a40f34cf9f1d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAELh9GawJMDDrOJDfeCRJ/7QS3BvlLGaIb\nscKs81zs5xgESa41I9clNOlklyz9OzivC93ZYZ5a8iPk0aQPNM+fHQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 353, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020101020103", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048e7abdbbd18de7452374c1879a1c3b01d13261e7d4571c3b47a1c76c55a2337326ed897cd517a4f5349db809780f6d2f2b9f6299d8b5a89077f1119a718fd7b3", "wx": "008e7abdbbd18de7452374c1879a1c3b01d13261e7d4571c3b47a1c76c55a23373", "wy": "26ed897cd517a4f5349db809780f6d2f2b9f6299d8b5a89077f1119a718fd7b3"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048e7abdbbd18de7452374c1879a1c3b01d13261e7d4571c3b47a1c76c55a2337326ed897cd517a4f5349db809780f6d2f2b9f6299d8b5a89077f1119a718fd7b3", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjnq9u9GN50UjdMGHmhw7AdEyYefUVxw7\nR6HHbFWiM3Mm7Yl81Rek9TSduAl4D20vK59imdi1qJB38RGacY/Xsw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 354, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020102020101", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "047b333d4340d3d718dd3e6aff7de7bbf8b72bfd616c8420056052842376b9af1942117c5afeac755d6f376fc6329a7d76051b87123a4a5d0bc4a539380f03de7b", "wx": "7b333d4340d3d718dd3e6aff7de7bbf8b72bfd616c8420056052842376b9af19", "wy": "42117c5afeac755d6f376fc6329a7d76051b87123a4a5d0bc4a539380f03de7b"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200047b333d4340d3d718dd3e6aff7de7bbf8b72bfd616c8420056052842376b9af1942117c5afeac755d6f376fc6329a7d76051b87123a4a5d0bc4a539380f03de7b", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFY<PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEezM9Q0DT1xjdPmr/fee7+Lcr/WFshCAF\nYFKEI3a5rxlCEXxa/qx1XW83b8Yymn12BRuHEjpKXQvEpTk4DwPeew==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 355, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020102020102", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d30ca4a0ddb6616c851d30ced682c40f83c62758a1f2759988d6763a88f1c0e503a80d5415650d41239784e8e2fb1235e9fe991d112ebb81186cbf0da2de3aff", "wx": "00d30ca4a0ddb6616c851d30ced682c40f83c62758a1f2759988d6763a88f1c0e5", "wy": "03a80d5415650d41239784e8e2fb1235e9fe991d112ebb81186cbf0da2de3aff"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d30ca4a0ddb6616c851d30ced682c40f83c62758a1f2759988d6763a88f1c0e503a80d5415650d41239784e8e2fb1235e9fe991d112ebb81186cbf0da2de3aff", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE0wykoN22YWyFHTDO1oLED4PGJ1ih8nWZ\niNZ2OojxwOUDqA1UFWUNQSOXhOji+xI16f6ZHREuu4EYbL8Not46/w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 356, "comment": "small r and s", "flags": ["SmallRandS", "ArithmeticError"], "msg": "313233343030", "sig": "3006020102020103", "result": "valid"}, {"tcId": 357, "comment": "r is larger than n", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364143020103", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0448969b39991297b332a652d3ee6e01e909b39904e71fa2354a7830c7750baf24b4012d1b830d199ccb1fc972b32bfded55f09cd62d257e5e844e27e57a1594ec", "wx": "48969b39991297b332a652d3ee6e01e909b39904e71fa2354a7830c7750baf24", "wy": "00b4012d1b830d199ccb1fc972b32bfded55f09cd62d257e5e844e27e57a1594ec"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000448969b39991297b332a652d3ee6e01e909b39904e71fa2354a7830c7750baf24b4012d1b830d199ccb1fc972b32bfded55f09cd62d257e5e844e27e57a1594ec", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESJabOZkSl7MyplLT7m4B6QmzmQTnH6I1\nSngwx3ULryS0AS0bgw0ZnMsfyXKzK/3tVfCc1i0lfl6ETiflehWU7A==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 358, "comment": "s is larger than n", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "30080201020203ed2979", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0402ef4d6d6cfd5a94f1d7784226e3e2a6c0a436c55839619f38fb4472b5f9ee777eb4acd4eebda5cd72875ffd2a2f26229c2dc6b46500919a432c86739f3ae866", "wx": "02ef4d6d6cfd5a94f1d7784226e3e2a6c0a436c55839619f38fb4472b5f9ee77", "wy": "7eb4acd4eebda5cd72875ffd2a2f26229c2dc6b46500919a432c86739f3ae866"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000402ef4d6d6cfd5a94f1d7784226e3e2a6c0a436c55839619f38fb4472b5f9ee777eb4acd4eebda5cd72875ffd2a2f26229c2dc6b46500919a432c86739f3ae866", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAu9NbWz9WpTx13hCJuPipsCkNsVYOWGf\nOPtEcrX57nd+tKzU7r2lzXKHX/0qLyYinC3GtGUAkZpDLIZznzroZg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 359, "comment": "small r and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "30260202010102203a74e9d3a74e9d3a74e9d3a74e9d3a749f8ab3732a0a89604a09bce5b2916da4", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04464f4ff715729cae5072ca3bd801d3195b67aec65e9b01aad20a2943dcbcb584b1afd29d31a39a11d570aa1597439b3b2d1971bf2f1abf15432d0207b10d1d08", "wx": "464f4ff715729cae5072ca3bd801d3195b67aec65e9b01aad20a2943dcbcb584", "wy": "00b1afd29d31a39a11d570aa1597439b3b2d1971bf2f1abf15432d0207b10d1d08"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004464f4ff715729cae5072ca3bd801d3195b67aec65e9b01aad20a2943dcbcb584b1afd29d31a39a11d570aa1597439b3b2d1971bf2f1abf15432d0207b10d1d08", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERk9P9xVynK5Qcso72AHTGVtnrsZemwGq\n0gopQ9y8tYSxr9KdMaOaEdVwqhWXQ5s7LRlxvy8avxVDLQIHsQ0dCA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 360, "comment": "smallish r and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "302b02072d9b4d347952cc02200343aefc2f25d98b882e86eb9e30d55a6eb508b516510b34024ae4b6362330b3", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04157f8fddf373eb5f49cfcf10d8b853cf91cbcd7d665c3522ba7dd738ddb79a4cdeadf1a5c448ea3c9f4191a8999abfcc757ac6d64567ef072c47fec613443b8f", "wx": "157f8fddf373eb5f49cfcf10d8b853cf91cbcd7d665c3522ba7dd738ddb79a4c", "wy": "00deadf1a5c448ea3c9f4191a8999abfcc757ac6d64567ef072c47fec613443b8f"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004157f8fddf373eb5f49cfcf10d8b853cf91cbcd7d665c3522ba7dd738ddb79a4cdeadf1a5c448ea3c9f4191a8999abfcc757ac6d64567ef072c47fec613443b8f", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEFX+P3fNz619Jz88Q2LhTz5HLzX1mXDUi\nun3XON23mkzerfGlxEjqPJ9BkaiZmr/MdXrG1kVn7wcsR/7GE0Q7jw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 361, "comment": "100-bit r and small s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3031020d1033e67e37b32b445580bf4efc02206f906f906f906f906f906f906f906f8fe1cab5eefdb214061dce3b22789f1d6f", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "040934a537466c07430e2c48feb990bb19fb78cecc9cee424ea4d130291aa237f0d4f92d23b462804b5b68c52558c01c9996dbf727fccabbeedb9621a400535afa", "wx": "0934a537466c07430e2c48feb990bb19fb78cecc9cee424ea4d130291aa237f0", "wy": "00d4f92d23b462804b5b68c52558c01c9996dbf727fccabbeedb9621a400535afa"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200040934a537466c07430e2c48feb990bb19fb78cecc9cee424ea4d130291aa237f0d4f92d23b462804b5b68c52558c01c9996dbf727fccabbeedb9621a400535afa", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAECTSlN0ZsB0MOLEj+uZC7Gft4zsyc7kJO\npNEwKRqiN/DU+S0jtGKAS1toxSVYwByZltv3J/zKu+7bliGkAFNa+g==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 362, "comment": "small r and 100 bit s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3026020201010220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d6ef20be66c893f741a9bf90d9b74675d1c2a31296397acb3ef174fd0b300c654a0c95478ca00399162d7f0f2dc89efdc2b28a30fbabe285857295a4b0c4e265", "wx": "00d6ef20be66c893f741a9bf90d9b74675d1c2a31296397acb3ef174fd0b300c65", "wy": "4a0c95478ca00399162d7f0f2dc89efdc2b28a30fbabe285857295a4b0c4e265"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d6ef20be66c893f741a9bf90d9b74675d1c2a31296397acb3ef174fd0b300c654a0c95478ca00399162d7f0f2dc89efdc2b28a30fbabe285857295a4b0c4e265", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE1u8gvmbIk/dBqb+Q2bdGddHCoxKWOXrL\nPvF0/QswDGVKDJVHjKADmRYtfw8tyJ79wrKKMPur4oWFcpWksMTiZQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 363, "comment": "100-bit r and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3031020d062522bbd3ecbe7c39e93e7c260220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04b7291d1404e0c0c07dab9372189f4bd58d2ceaa8d15ede544d9514545ba9ee0629c9a63d5e308769cc30ec276a410e6464a27eeafd9e599db10f053a4fe4a829", "wx": "00b7291d1404e0c0c07dab9372189f4bd58d2ceaa8d15ede544d9514545ba9ee06", "wy": "29c9a63d5e308769cc30ec276a410e6464a27eeafd9e599db10f053a4fe4a829"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004b7291d1404e0c0c07dab9372189f4bd58d2ceaa8d15ede544d9514545ba9ee0629c9a63d5e308769cc30ec276a410e6464a27eeafd9e599db10f053a4fe4a829", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEtykdFATgwMB9q5NyGJ9L1Y0s6qjRXt5U\nTZUUVFup7gYpyaY9XjCHacww7CdqQQ5kZKJ+6v2eWZ2xDwU6T+SoKQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 364, "comment": "r and s^-1 are close to n", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03640c1022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046e28303305d642ccb923b722ea86b2a0bc8e3735ecb26e849b19c9f76b2fdbb8186e80d64d8cab164f5238f5318461bf89d4d96ee6544c816c7566947774e0f6", "wx": "6e28303305d642ccb923b722ea86b2a0bc8e3735ecb26e849b19c9f76b2fdbb8", "wy": "186e80d64d8cab164f5238f5318461bf89d4d96ee6544c816c7566947774e0f6"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046e28303305d642ccb923b722ea86b2a0bc8e3735ecb26e849b19c9f76b2fdbb8186e80d64d8cab164f5238f5318461bf89d4d96ee6544c816c7566947774e0f6", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbigwMwXWQsy5I7ci6oayoLyONzXssm6E\nmxnJ92sv27gYboDWTYyrFk9SOPUxhGG/idTZbuZUTIFsdWaUd3Tg9g==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 365, "comment": "r and s are 64-bit integer", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "30160209009c44febf31c3594d020900839ed28247c2b06b", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04375bda93f6af92fb5f8f4b1b5f0534e3bafab34cb7ad9fb9d0b722e4a5c302a9a00b9f387a5a396097aa2162fc5bbcf4a5263372f681c94da51e9799120990fd", "wx": "375bda93f6af92fb5f8f4b1b5f0534e3bafab34cb7ad9fb9d0b722e4a5c302a9", "wy": "00a00b9f387a5a396097aa2162fc5bbcf4a5263372f681c94da51e9799120990fd"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004375bda93f6af92fb5f8f4b1b5f0534e3bafab34cb7ad9fb9d0b722e4a5c302a9a00b9f387a5a396097aa2162fc5bbcf4a5263372f681c94da51e9799120990fd", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEN1vak/avkvtfj0sbXwU047r6s0y3rZ+5\n0Lci5KXDAqmgC584elo5YJeqIWL8W7z0pSYzcvaByU2lHpeZEgmQ/Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 366, "comment": "r and s are 100-bit integer", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "301e020d09df8b682430beef6f5fd7c7cf020d0fd0a62e13778f4222a0d61c8a", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d75b68216babe03ae257e94b4e3bf1c52f44e3df266d1524ff8c5ea69da73197da4bff9ed1c53f44917a67d7b978598e89df359e3d5913eaea24f3ae259abc44", "wx": "00d75b68216babe03ae257e94b4e3bf1c52f44e3df266d1524ff8c5ea69da73197", "wy": "00da4bff9ed1c53f44917a67d7b978598e89df359e3d5913eaea24f3ae259abc44"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d75b68216babe03ae257e94b4e3bf1c52f44e3df266d1524ff8c5ea69da73197da4bff9ed1c53f44917a67d7b978598e89df359e3d5913eaea24f3ae259abc44", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE11toIWur4DriV+lLTjvxxS9E498mbRUk\n/4xepp2nMZfaS/+e0cU/RJF6Z9e5eFmOid81nj1ZE+rqJPOuJZq8RA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 367, "comment": "r and s are 128-bit integer", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "30260211008a598e563a89f526c32ebec8de26367a02110084f633e2042630e99dd0f1e16f7a04bf", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0478bcda140aed23d430cb23c3dc0d01f423db134ee94a3a8cb483f2deac2ac653118114f6f33045d4e9ed9107085007bfbddf8f58fe7a1a2445d66a990045476e", "wx": "78bcda140aed23d430cb23c3dc0d01f423db134ee94a3a8cb483f2deac2ac653", "wy": "118114f6f33045d4e9ed9107085007bfbddf8f58fe7a1a2445d66a990045476e"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000478bcda140aed23d430cb23c3dc0d01f423db134ee94a3a8cb483f2deac2ac653118114f6f33045d4e9ed9107085007bfbddf8f58fe7a1a2445d66a990045476e", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeLzaFArtI9QwyyPD3A0B9CPbE07pSjqM\ntIPy3qwqxlMRgRT28zBF1OntkQcIUAe/vd+PWP56GiRF1mqZAEVHbg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 368, "comment": "r and s are 160-bit integer", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "302e021500aa6eeb5823f7fa31b466bb473797f0d0314c0bdf021500e2977c479e6d25703cebbc6bd561938cc9d1bfb9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04bb79f61857f743bfa1b6e7111ce4094377256969e4e15159123d9548acc3be6c1f9d9f8860dcffd3eb36dd6c31ff2e7226c2009c4c94d8d7d2b5686bf7abd677", "wx": "00bb79f61857f743bfa1b6e7111ce4094377256969e4e15159123d9548acc3be6c", "wy": "1f9d9f8860dcffd3eb36dd6c31ff2e7226c2009c4c94d8d7d2b5686bf7abd677"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004bb79f61857f743bfa1b6e7111ce4094377256969e4e15159123d9548acc3be6c1f9d9f8860dcffd3eb36dd6c31ff2e7226c2009c4c94d8d7d2b5686bf7abd677", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEu3n2GFf3Q7+htucRHOQJQ3claWnk4VFZ\nEj2VSKzDvmwfnZ+IYNz/0+s23Wwx/y5yJsIAnEyU2NfStWhr96vWdw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 369, "comment": "s == 1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020101", "result": "valid"}, {"tcId": 370, "comment": "s == 0", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020100", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0493591827d9e6713b4e9faea62c72b28dfefa68e0c05160b5d6aae88fd2e36c36073f5545ad5af410af26afff68654cf72d45e493489311203247347a890f4518", "wx": "0093591827d9e6713b4e9faea62c72b28dfefa68e0c05160b5d6aae88fd2e36c36", "wy": "073f5545ad5af410af26afff68654cf72d45e493489311203247347a890f4518"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000493591827d9e6713b4e9faea62c72b28dfefa68e0c05160b5d6aae88fd2e36c36073f5545ad5af410af26afff68654cf72d45e493489311203247347a890f4518", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEk1kYJ9nmcTtOn66mLHKyjf76aODAUWC1\n1qroj9LjbDYHP1VFrVr0EK8mr/9oZUz3LUXkk0iTESAyRzR6iQ9FGA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 371, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c10220419d981c515af8cc82545aac0c85e9e308fbb2eab6acd7ed497e0b4145a18fd9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0431ed3081aefe001eb6402069ee2ccc1862937b85995144dba9503943587bf0dada01b8cc4df34f5ab3b1a359615208946e5ee35f98ee775b8ccecd86ccc1650f", "wx": "31ed3081aefe001eb6402069ee2ccc1862937b85995144dba9503943587bf0da", "wy": "00da01b8cc4df34f5ab3b1a359615208946e5ee35f98ee775b8ccecd86ccc1650f"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000431ed3081aefe001eb6402069ee2ccc1862937b85995144dba9503943587bf0dada01b8cc4df34f5ab3b1a359615208946e5ee35f98ee775b8ccecd86ccc1650f", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEMe0wga7+AB62QCBp7izMGGKTe4WZUUTb\nqVA5Q1h78NraAbjMTfNPWrOxo1lhUgiUbl7jX5jud1uMzs2GzMFlDw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 372, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102201b21717ad71d23bbac60a9ad0baf75b063c9fdf52a00ebf99d022172910993c9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "047dff66fa98509ff3e2e51045f4390523dccda43a3bc2885e58c248090990eea854c76c2b9adeb6bb571823e07fd7c65c8639cf9d905260064c8e7675ce6d98b4", "wx": "7dff66fa98509ff3e2e51045f4390523dccda43a3bc2885e58c248090990eea8", "wy": "54c76c2b9adeb6bb571823e07fd7c65c8639cf9d905260064c8e7675ce6d98b4"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200047dff66fa98509ff3e2e51045f4390523dccda43a3bc2885e58c248090990eea854c76c2b9adeb6bb571823e07fd7c65c8639cf9d905260064c8e7675ce6d98b4", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEff9m+phQn/Pi5RBF9DkFI9zNpDo7wohe\nWMJICQmQ7qhUx2wrmt62u1cYI+B/**************************==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 373, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102202f588f66018f3dd14db3e28e77996487e32486b521ed8e5a20f06591951777e9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "044280509aab64edfc0b4a2967e4cbce849cb544e4a77313c8e6ece579fbd7420a2e89fe5cc1927d554e6a3bb14033ea7c922cd75cba2c7415fdab52f20b1860f1", "wx": "4280509aab64edfc0b4a2967e4cbce849cb544e4a77313c8e6ece579fbd7420a", "wy": "2e89fe5cc1927d554e6a3bb14033ea7c922cd75cba2c7415fdab52f20b1860f1"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200044280509aab64edfc0b4a2967e4cbce849cb544e4a77313c8e6ece579fbd7420a2e89fe5cc1927d554e6a3bb14033ea7c922cd75cba2c7415fdab52f20b1860f1", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEQoBQmqtk7fwLSiln5MvOhJy1ROSncxPI\n5uzlefvXQgouif5cwZJ9VU5qO7FAM+p8kizXXLosdBX9q1LyCxhg8Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 374, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c10220091a08870ff4daf9123b30c20e8c4fc8505758dcf4074fcaff2170c9bfcf74f4", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "044f8df145194e3c4fc3eea26d43ce75b402d6b17472ddcbb254b8a79b0bf3d9cb2aa20d82844cb266344e71ca78f2ad27a75a09e5bc0fa57e4efd9d465a0888db", "wx": "4f8df145194e3c4fc3eea26d43ce75b402d6b17472ddcbb254b8a79b0bf3d9cb", "wy": "2aa20d82844cb266344e71ca78f2ad27a75a09e5bc0fa57e4efd9d465a0888db"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200044f8df145194e3c4fc3eea26d43ce75b402d6b17472ddcbb254b8a79b0bf3d9cb2aa20d82844cb266344e71ca78f2ad27a75a09e5bc0fa57e4efd9d465a0888db", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAET43xRRlOPE/D7qJtQ851tALWsXRy3cuy\nVLinmwvz2csqog2ChEyyZjROccp48q0np1oJ5bwPpX5O/Z1GWgiI2w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 375, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102207c370dc0ce8c59a8b273cba44a7c1191fc3186dc03cab96b0567312df0d0b250", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "049598a57dd67ec3e16b587a338aa3a10a3a3913b41a3af32e3ed3ff01358c6b14122819edf8074bbc521f7d4cdce82fef7a516706affba1d93d9dea9ccae1a207", "wx": "009598a57dd67ec3e16b587a338aa3a10a3a3913b41a3af32e3ed3ff01358c6b14", "wy": "122819edf8074bbc521f7d4cdce82fef7a516706affba1d93d9dea9ccae1a207"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200049598a57dd67ec3e16b587a338aa3a10a3a3913b41a3af32e3ed3ff01358c6b14122819edf8074bbc521f7d4cdce82fef7a516706affba1d93d9dea9ccae1a207", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAElZilfdZ+w+FrWHoziqOhCjo5E7QaOvMu\nPtP/ATWMaxQSKBnt+AdLvFIffUzc6C/velFnBq/7odk9neqcyuGiBw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 376, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1022070b59a7d1ee77a2f9e0491c2a7cfcd0ed04df4a35192f6132dcc668c79a6160e", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "049171fec3ca20806bc084f12f0760911b60990bd80e5b2a71ca03a048b20f837e634fd17863761b2958d2be4e149f8d3d7abbdc18be03f451ab6c17fa0a1f8330", "wx": "009171fec3ca20806bc084f12f0760911b60990bd80e5b2a71ca03a048b20f837e", "wy": "634fd17863761b2958d2be4e149f8d3d7abbdc18be03f451ab6c17fa0a1f8330"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200049171fec3ca20806bc084f12f0760911b60990bd80e5b2a71ca03a048b20f837e634fd17863761b2958d2be4e149f8d3d7abbdc18be03f451ab6c17fa0a1f8330", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEkXH+w8oggGvAhPEvB2CRG2CZC9gOWypx\nygOgSLIPg35jT9F4Y3YbKVjSvk4Un409ervcGL4D9FGrbBf6Ch+DMA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 377, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102202736d76e412246e097148e2bf62915614eb7c428913a58eb5e9cd4674a9423de", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04777c8930b6e1d271100fe68ce93f163fa37612c5fff67f4a62fc3bafaf3d17a9ed73d86f60a51b5ed91353a3b054edc0aa92c9ebcbd0b75d188fdc882791d68d", "wx": "777c8930b6e1d271100fe68ce93f163fa37612c5fff67f4a62fc3bafaf3d17a9", "wy": "00ed73d86f60a51b5ed91353a3b054edc0aa92c9ebcbd0b75d188fdc882791d68d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004777c8930b6e1d271100fe68ce93f163fa37612c5fff67f4a62fc3bafaf3d17a9ed73d86f60a51b5ed91353a3b054edc0aa92c9ebcbd0b75d188fdc882791d68d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEd3yJMLbh0nEQD+aM6T8WP6N2EsX/9n9K\nYvw7r689F6ntc9hvYKUbXtkTU6OwVO3AqpLJ68vQt10Yj9yIJ5HWjQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 378, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102204a1e12831fbe93627b02d6e7f24bccdd6ef4b2d0f46739eaf3b1eaf0ca117770", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04eabc248f626e0a63e1eb81c43d461a39a1dba881eb6ee2152b07c32d71bcf4700603caa8b9d33db13af44c6efbec8a198ed6124ac9eb17eaafd2824a545ec000", "wx": "00eabc248f626e0a63e1eb81c43d461a39a1dba881eb6ee2152b07c32d71bcf470", "wy": "0603caa8b9d33db13af44c6efbec8a198ed6124ac9eb17eaafd2824a545ec000"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004eabc248f626e0a63e1eb81c43d461a39a1dba881eb6ee2152b07c32d71bcf4700603caa8b9d33db13af44c6efbec8a198ed6124ac9eb17eaafd2824a545ec000", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE6rwkj2JuCmPh64HEPUYaOaHbqIHrbuIV\nKwfDLXG89HAGA8qoudM9sTr0TG777IoZjtYSSsnrF+qv0oJKVF7AAA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 379, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1022006c778d4dfff7dee06ed88bc4e0ed34fc553aad67caf796f2a1c6487c1b2e877", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "049f7a13ada158a55f9ddf1a45f044f073d9b80030efdcfc9f9f58418fbceaf001f8ada0175090f80d47227d6713b6740f9a0091d88a837d0a1cd77b58a8f28d73", "wx": "009f7a13ada158a55f9ddf1a45f044f073d9b80030efdcfc9f9f58418fbceaf001", "wy": "00f8ada0175090f80d47227d6713b6740f9a0091d88a837d0a1cd77b58a8f28d73"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200049f7a13ada158a55f9ddf1a45f044f073d9b80030efdcfc9f9f58418fbceaf001f8ada0175090f80d47227d6713b6740f9a0091d88a837d0a1cd77b58a8f28d73", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEn3oTraFYpV+d3xpF8ETwc9m4ADDv3Pyf\nn1hBj7zq8AH4raAXUJD4DUcifWcTtnQPmgCR2IqDfQoc13tYqPKNcw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 380, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102204de459ef9159afa057feb3ec40fef01c45b809f4ab296ea48c206d4249a2b451", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0411c4f3e461cd019b5c06ea0cea4c4090c3cc3e3c5d9f3c6d65b436826da9b4dbbbeb7a77e4cbfda207097c43423705f72c80476da3dac40a483b0ab0f2ead1cb", "wx": "11c4f3e461cd019b5c06ea0cea4c4090c3cc3e3c5d9f3c6d65b436826da9b4db", "wy": "00bbeb7a77e4cbfda207097c43423705f72c80476da3dac40a483b0ab0f2ead1cb"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000411c4f3e461cd019b5c06ea0cea4c4090c3cc3e3c5d9f3c6d65b436826da9b4dbbbeb7a77e4cbfda207097c43423705f72c80476da3dac40a483b0ab0f2ead1cb", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEEcTz5GHNAZtcBuoM6kxAkMPMPjxdnzxt\nZbQ2gm2ptNu763p35Mv9ogcJfENCNwX3LIBHbaPaxApIOwqw8urRyw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 381, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c10220745d294978007302033502e1acc48b63ae6500be43adbea1b258d6b423dbb416", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04e2e18682d53123aa01a6c5d00b0c623d671b462ea80bddd65227fd5105988aa4161907b3fd25044a949ea41c8e2ea8459dc6f1654856b8b61b31543bb1b45bdb", "wx": "00e2e18682d53123aa01a6c5d00b0c623d671b462ea80bddd65227fd5105988aa4", "wy": "161907b3fd25044a949ea41c8e2ea8459dc6f1654856b8b61b31543bb1b45bdb"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004e2e18682d53123aa01a6c5d00b0c623d671b462ea80bddd65227fd5105988aa4161907b3fd25044a949ea41c8e2ea8459dc6f1654856b8b61b31543bb1b45bdb", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE4uGGgtUxI6oBpsXQCwxiPWcbRi6oC93W\nUif9UQWYiqQWGQez/SUESpSepByOLqhFncbxZUhWuLYbMVQ7sbRb2w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 382, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102207b2a785e3896f59b2d69da57648e80ad3c133a750a2847fd2098ccd902042b6c", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0490f8d4ca73de08a6564aaf005247b6f0ffe978504dce52605f46b7c3e56197dafadbe528eb70d9ee7ea0e70702db54f721514c7b8604ac2cb214f1decb7e383d", "wx": "0090f8d4ca73de08a6564aaf005247b6f0ffe978504dce52605f46b7c3e56197da", "wy": "00fadbe528eb70d9ee7ea0e70702db54f721514c7b8604ac2cb214f1decb7e383d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000490f8d4ca73de08a6564aaf005247b6f0ffe978504dce52605f46b7c3e56197dafadbe528eb70d9ee7ea0e70702db54f721514c7b8604ac2cb214f1decb7e383d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEkPjUynPeCKZWSq8AUke28P/peFBNzlJg\nX0a3w+Vhl9r62+Uo63DZ7n6g5wcC21T3IVFMe4YErCyyFPHey344PQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 383, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1022071ae94a72ca896875e7aa4a4c3d29afdb4b35b6996273e63c47ac519256c5eb1", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04824c195c73cffdf038d101bce1687b5c3b6146f395c885976f7753b2376b948e3cdefa6fc347d13e4dcbc63a0b03a165180cd2be1431a0cf74ce1ea25082d2bc", "wx": "00824c195c73cffdf038d101bce1687b5c3b6146f395c885976f7753b2376b948e", "wy": "3cdefa6fc347d13e4dcbc63a0b03a165180cd2be1431a0cf74ce1ea25082d2bc"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004824c195c73cffdf038d101bce1687b5c3b6146f395c885976f7753b2376b948e3cdefa6fc347d13e4dcbc63a0b03a165180cd2be1431a0cf74ce1ea25082d2bc", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEgkwZXHPP/fA40QG84Wh7XDthRvOVyIWX\nb3dTsjdrlI483vpvw0fRPk3LxjoLA6FlGAzSvhQxoM90zh6iUILSvA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 384, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102200fa527fa7343c0bc9ec35a6278bfbff4d83301b154fc4bd14aee7eb93445b5f9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "042788a52f078eb3f202c4fa73e0d3386faf3df6be856003636f599922d4f5268f30b4f207c919bbdf5e67a8be4265a8174754b3aba8f16e575b77ff4d5a7eb64f", "wx": "2788a52f078eb3f202c4fa73e0d3386faf3df6be856003636f599922d4f5268f", "wy": "30b4f207c919bbdf5e67a8be4265a8174754b3aba8f16e575b77ff4d5a7eb64f"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200042788a52f078eb3f202c4fa73e0d3386faf3df6be856003636f599922d4f5268f30b4f207c919bbdf5e67a8be4265a8174754b3aba8f16e575b77ff4d5a7eb64f", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEJ4ilLweOs/ICxPpz4NM4b6899r6FYANj\nb1mZItT1Jo8wtPIHyRm7315nqL5CZagXR1Szq6jxbldbd/9NWn62Tw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 385, "comment": "edge case modular inverse", "flags": ["ModularInverse", "ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c102206539c0adadd0525ff42622164ce9314348bd0863b4c80e936b23ca0414264671", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d533b789a4af890fa7a82a1fae58c404f9a62a50b49adafab349c513b415087401b4171b803e76b34a9861e10f7bc289a066fd01bd29f84c987a10a5fb18c2d4", "wx": "00d533b789a4af890fa7a82a1fae58c404f9a62a50b49adafab349c513b4150874", "wy": "01b4171b803e76b34a9861e10f7bc289a066fd01bd29f84c987a10a5fb18c2d4"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d533b789a4af890fa7a82a1fae58c404f9a62a50b49adafab349c513b415087401b4171b803e76b34a9861e10f7bc289a066fd01bd29f84c987a10a5fb18c2d4", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE1TO3iaSviQ+nqCofrljEBPmmKlC0mtr6\ns0nFE7QVCHQBtBcbgD52s0qYYeEPe8KJoGb9Ab0p+EyYehCl+xjC1A==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 386, "comment": "point at infinity during verify", "flags": ["PointDuplication", "ArithmeticError"], "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "043a3150798c8af69d1e6e981f3a45402ba1d732f4be8330c5164f49e10ec555b4221bd842bc5e4d97eff37165f60e3998a424d72a450cf95ea477c78287d0343a", "wx": "3a3150798c8af69d1e6e981f3a45402ba1d732f4be8330c5164f49e10ec555b4", "wy": "221bd842bc5e4d97eff37165f60e3998a424d72a450cf95ea477c78287d0343a"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200043a3150798c8af69d1e6e981f3a45402ba1d732f4be8330c5164f49e10ec555b4221bd842bc5e4d97eff37165f60e3998a424d72a450cf95ea477c78287d0343a", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEOjFQeYyK9p0ebpgfOkVAK6HXMvS+gzDF\nFk9J4Q7FVbQiG9hCvF5Nl+/zcWX2DjmYpCTXKkUM+V6kd8eCh9A0Og==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 387, "comment": "edge case for signature malleability", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "043b37df5fb347c69a0f17d85c0c7ca83736883a825e13143d0fcfc8101e851e800de3c090b6ca21ba543517330c04b12f948c6badf14a63abffdf4ef8c7537026", "wx": "3b37df5fb347c69a0f17d85c0c7ca83736883a825e13143d0fcfc8101e851e80", "wy": "0de3c090b6ca21ba543517330c04b12f948c6badf14a63abffdf4ef8c7537026"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200043b37df5fb347c69a0f17d85c0c7ca83736883a825e13143d0fcfc8101e851e800de3c090b6ca21ba543517330c04b12f948c6badf14a63abffdf4ef8c7537026", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEOzffX7NHxpoPF9hcDHyoNzaIOoJeExQ9\nD8/IEB6FHoAN48CQtsohulQ1FzMMBLEvlIxrrfFKY6v/3074x1NwJg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 388, "comment": "edge case for signature malleability", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a1", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04feb5163b0ece30ff3e03c7d55c4380fa2fa81ee2c0354942ff6f08c99d0cd82ce87de05ee1bda089d3e4e248fa0f721102acfffdf50e654be281433999df897e", "wx": "00feb5163b0ece30ff3e03c7d55c4380fa2fa81ee2c0354942ff6f08c99d0cd82c", "wy": "00e87de05ee1bda089d3e4e248fa0f721102acfffdf50e654be281433999df897e"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004feb5163b0ece30ff3e03c7d55c4380fa2fa81ee2c0354942ff6f08c99d0cd82ce87de05ee1bda089d3e4e248fa0f721102acfffdf50e654be281433999df897e", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE/rUWOw7OMP8+A8fVXEOA+i+oHuLANUlC\n/28IyZ0M2CzofeBe4b2gidPk4kj6D3IRAqz//fUOZUvigUM5md+Jfg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 389, "comment": "u1 == 1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022044a5ad0bd0636d9e12bc9e0a6bdd5e1bba77f523842193b3b82e448e05d5f11e", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04238ced001cf22b8853e02edc89cbeca5050ba7e042a7a77f9382cd414922897640683d3094643840f295890aa4c18aa39b41d77dd0fb3bb2700e4f9ec284ffc2", "wx": "238ced001cf22b8853e02edc89cbeca5050ba7e042a7a77f9382cd4149228976", "wy": "40683d3094643840f295890aa4c18aa39b41d77dd0fb3bb2700e4f9ec284ffc2"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004238ced001cf22b8853e02edc89cbeca5050ba7e042a7a77f9382cd414922897640683d3094643840f295890aa4c18aa39b41d77dd0fb3bb2700e4f9ec284ffc2", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEI4ztABzyK4hT4C7cicvspQULp+BCp6d/\nk4LNQUkiiXZAaD0wlGQ4QPKViQqkwYqjm0HXfdD7O7JwDk+ewoT/wg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 390, "comment": "u1 == n - 1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022044a5ad0bd0636d9e12bc9e0a6bdd5e1bba77f523842193b3b82e448e05d5f11e", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04961cf64817c06c0e51b3c2736c922fde18bd8c4906fcd7f5ef66c4678508f35ed2c5d18168cfbe70f2f123bd7419232bb92dd69113e2941061889481c5a027bf", "wx": "00961cf64817c06c0e51b3c2736c922fde18bd8c4906fcd7f5ef66c4678508f35e", "wy": "00d2c5d18168cfbe70f2f123bd7419232bb92dd69113e2941061889481c5a027bf"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004961cf64817c06c0e51b3c2736c922fde18bd8c4906fcd7f5ef66c4678508f35ed2c5d18168cfbe70f2f123bd7419232bb92dd69113e2941061889481c5a027bf", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAElhz2SBfAbA5Rs8JzbJIv3hi9jEkG/Nf1\n72bEZ4UI817SxdGBaM++cPLxI710GSMruS3WkRPilBBhiJSBxaAnvw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 391, "comment": "u2 == 1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0413681eae168cd4ea7cf2e2a45d052742d10a9f64e796867dbdcb829fe0b1028816528760d177376c09df79de39557c329cc1753517acffe8fa2ec298026b8384", "wx": "13681eae168cd4ea7cf2e2a45d052742d10a9f64e796867dbdcb829fe0b10288", "wy": "16528760d177376c09df79de39557c329cc1753517acffe8fa2ec298026b8384"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000413681eae168cd4ea7cf2e2a45d052742d10a9f64e796867dbdcb829fe0b1028816528760d177376c09df79de39557c329cc1753517acffe8fa2ec298026b8384", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEE2gerhaM1Op88uKkXQUnQtEKn2TnloZ9\nvcuCn+CxAogWUodg0Xc3bAnfed45VXwynMF1NRes/+j6LsKYAmuDhA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 392, "comment": "u2 == n - 1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "045aa7abfdb6b4086d543325e5d79c6e95ce42f866d2bb84909633a04bb1aa31c291c80088794905e1da33336d874e2f91ccf45cc59185bede5dd6f3f7acaae18b", "wx": "5aa7abfdb6b4086d543325e5d79c6e95ce42f866d2bb84909633a04bb1aa31c2", "wy": "0091c80088794905e1da33336d874e2f91ccf45cc59185bede5dd6f3f7acaae18b"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200045aa7abfdb6b4086d543325e5d79c6e95ce42f866d2bb84909633a04bb1aa31c291c80088794905e1da33336d874e2f91ccf45cc59185bede5dd6f3f7acaae18b", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEWqer/ba0CG1UMyXl15xulc5C+GbSu4SQ\nljOgS7GqMcKRyACIeUkF4dozM22HTi+RzPRcxZGFvt5d1vP3rKrhiw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 393, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022016e1e459457679df5b9434ae23f474b3e8d2a70bd6b5dbe692ba16da01f1fb0a", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0400277791b305a45b2b39590b2f05d3392a6c8182cef4eb540120e0f5c206c3e464108233fb0b8c3ac892d79ef8e0fbf92ed133addb4554270132584dc52eef41", "wx": "277791b305a45b2b39590b2f05d3392a6c8182cef4eb540120e0f5c206c3e4", "wy": "64108233fb0b8c3ac892d79ef8e0fbf92ed133addb4554270132584dc52eef41"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000400277791b305a45b2b39590b2f05d3392a6c8182cef4eb540120e0f5c206c3e464108233fb0b8c3ac892d79ef8e0fbf92ed133addb4554270132584dc52eef41", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEACd3kbMFpFsrOVkLLwXTOSpsgYLO9OtU\nASDg9cIGw+RkEIIz+wuMOsiS15744Pv5LtEzrdtFVCcBMlhNxS7vQQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 394, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02201c940f313f92647be257eccd7ed08b0baef3f0478f25871b53635302c5f6314a", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046efa092b68de9460f0bcc919005a5f6e80e19de98968be3cd2c770a9949bfb1ac75e6e5087d6550d5f9beb1e79e5029307bc255235e2d5dc99241ac3ab886c49", "wx": "6efa092b68de9460f0bcc919005a5f6e80e19de98968be3cd2c770a9949bfb1a", "wy": "00c75e6e5087d6550d5f9beb1e79e5029307bc255235e2d5dc99241ac3ab886c49"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046efa092b68de9460f0bcc919005a5f6e80e19de98968be3cd2c770a9949bfb1ac75e6e5087d6550d5f9beb1e79e5029307bc255235e2d5dc99241ac3ab886c49", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbvoJK2jelGDwvMkZAFpfboDhnemJaL48\n0sdwqZSb+xrHXm5Qh9ZVDV+b6x555QKTB7wlUjXi1dyZJBrDq4hsSQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 395, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022015d94a85077b493f91cb7101ec63e1b01be58b594e855f45050a8c14062d689b", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0472d4a19c4f9d2cf5848ea40445b70d4696b5f02d632c0c654cc7d7eeb0c6d058e8c4cd9943e459174c7ac01fa742198e47e6c19a6bdb0c4f6c237831c1b3f942", "wx": "72d4a19c4f9d2cf5848ea40445b70d4696b5f02d632c0c654cc7d7eeb0c6d058", "wy": "00e8c4cd9943e459174c7ac01fa742198e47e6c19a6bdb0c4f6c237831c1b3f942"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000472d4a19c4f9d2cf5848ea40445b70d4696b5f02d632c0c654cc7d7eeb0c6d058e8c4cd9943e459174c7ac01fa742198e47e6c19a6bdb0c4f6c237831c1b3f942", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEctShnE+dLPWEjqQERbcNRpa18C1jLAxl\nTMfX7rDG0FjoxM2ZQ+RZF0x6wB+nQhmOR+bBmmvbDE9sI3gxwbP5Qg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 396, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02205b1d27a7694c146244a5ad0bd0636d9d9ef3b9fb58385418d9c982105077d1b7", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "042a8ea2f50dcced0c217575bdfa7cd47d1c6f100041ec0e35512794c1be7e740258f8c17122ed303fda7143eb58bede70295b653266013b0b0ebd3f053137f6ec", "wx": "2a8ea2f50dcced0c217575bdfa7cd47d1c6f100041ec0e35512794c1be7e7402", "wy": "58f8c17122ed303fda7143eb58bede70295b653266013b0b0ebd3f053137f6ec"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200042a8ea2f50dcced0c217575bdfa7cd47d1c6f100041ec0e35512794c1be7e740258f8c17122ed303fda7143eb58bede70295b653266013b0b0ebd3f053137f6ec", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEKo6i9Q3M7QwhdXW9+nzUfRxvEABB7A41\nUSeUwb5+dAJY+MFxIu0wP9pxQ+tYvt5wKVtlMmYBOwsOvT8FMTf27A==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 397, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202d85896b3eb9dbb5a52f42f9c9261ed3fc46644ec65f06ade3fd78f257e43432", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0488de689ce9af1e94be6a2089c8a8b1253ffdbb6c8e9c86249ba220001a4ad3b80c4998e54842f413b9edb1825acbb6335e81e4d184b2b01c8bebdc85d1f28946", "wx": "0088de689ce9af1e94be6a2089c8a8b1253ffdbb6c8e9c86249ba220001a4ad3b8", "wy": "0c4998e54842f413b9edb1825acbb6335e81e4d184b2b01c8bebdc85d1f28946"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000488de689ce9af1e94be6a2089c8a8b1253ffdbb6c8e9c86249ba220001a4ad3b80c4998e54842f413b9edb1825acbb6335e81e4d184b2b01c8bebdc85d1f28946", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEiN5onOmvHpS+aiCJyKixJT/9u2yOnIYk\nm6IgABpK07gMSZjlSEL0E7ntsYJay7YzXoHk0YSysByL69yF0fKJRg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 398, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02205b0b12d67d73b76b4a5e85f3924c3da7f88cc89d8cbe0d5bc7faf1e4afc86864", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04fea2d31f70f90d5fb3e00e186ac42ab3c1615cee714e0b4e1131b3d4d8225bf7b037a18df2ac15343f30f74067ddf29e817d5f77f8dce05714da59c094f0cda9", "wx": "00fea2d31f70f90d5fb3e00e186ac42ab3c1615cee714e0b4e1131b3d4d8225bf7", "wy": "00b037a18df2ac15343f30f74067ddf29e817d5f77f8dce05714da59c094f0cda9"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004fea2d31f70f90d5fb3e00e186ac42ab3c1615cee714e0b4e1131b3d4d8225bf7b037a18df2ac15343f30f74067ddf29e817d5f77f8dce05714da59c094f0cda9", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE/qLTH3D5DV+z4A4YasQqs8FhXO5xTgtO\nETGz1NgiW/ewN6GN8qwVND8w90Bn3fKegX1fd/jc4FcU2lnAlPDNqQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 399, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220694c146244a5ad0bd0636d9e12bc9e09e60e68b90d0b5e6c5dddd0cb694d8799", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "047258911e3d423349166479dbe0b8341af7fbd03d0a7e10edccb36b6ceea5a3db17ac2b8992791128fa3b96dc2fbd4ca3bfa782ef2832fc6656943db18e7346b0", "wx": "7258911e3d423349166479dbe0b8341af7fbd03d0a7e10edccb36b6ceea5a3db", "wy": "17ac2b8992791128fa3b96dc2fbd4ca3bfa782ef2832fc6656943db18e7346b0"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200047258911e3d423349166479dbe0b8341af7fbd03d0a7e10edccb36b6ceea5a3db17ac2b8992791128fa3b96dc2fbd4ca3bfa782ef2832fc6656943db18e7346b0", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEcliRHj1CM0kWZHnb4Lg0Gvf70D0KfhDt\nzLNrbO6lo9sXrCuJknkRKPo7ltwvvUyjv6eC7ygy/GZWlD2xjnNGsA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 400, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203d7f487c07bfc5f30846938a3dcef696444707cf9677254a92b06c63ab867d22", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "044f28461dea64474d6bb34d1499c97d37b9e95633df1ceeeaacd45016c98b3914c8818810b8cc06ddb40e8a1261c528faa589455d5a6df93b77bc5e0e493c7470", "wx": "4f28461dea64474d6bb34d1499c97d37b9e95633df1ceeeaacd45016c98b3914", "wy": "00c8818810b8cc06ddb40e8a1261c528faa589455d5a6df93b77bc5e0e493c7470"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200044f28461dea64474d6bb34d1499c97d37b9e95633df1ceeeaacd45016c98b3914c8818810b8cc06ddb40e8a1261c528faa589455d5a6df93b77bc5e0e493c7470", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAETyhGHepkR01rs00Umcl9N7npVjPfHO7q\nrNRQFsmLORTIgYgQuMwG3bQOihJhxSj6pYlFXVpt+Tt3vF4OSTx0cA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 401, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206c7648fc0fbf8a06adb8b839f97b4ff7a800f11b1e37c593b261394599792ba4", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0474f2a814fb5d8eca91a69b5e60712732b3937de32829be974ed7b68c5c2f5d66eff0f07c56f987a657f42196205f588c0f1d96fd8a63a5f238b48f478788fe3b", "wx": "74f2a814fb5d8eca91a69b5e60712732b3937de32829be974ed7b68c5c2f5d66", "wy": "00eff0f07c56f987a657f42196205f588c0f1d96fd8a63a5f238b48f478788fe3b"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000474f2a814fb5d8eca91a69b5e60712732b3937de32829be974ed7b68c5c2f5d66eff0f07c56f987a657f42196205f588c0f1d96fd8a63a5f238b48f478788fe3b", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEdPKoFPtdjsqRppteYHEnMrOTfeMoKb6X\nTte2jFwvXWbv8PB8VvmHplf0IZYgX1iMDx2W/YpjpfI4tI9Hh4j+Ow==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 402, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220641c9c5d790dc09cdd3dfabb62cdf453e69747a7e3d7aa1a714189ef53171a99", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04195b51a7cc4a21b8274a70a90de779814c3c8ca358328208c09a29f336b82d6ab2416b7c92fffdc29c3b1282dd2a77a4d04df7f7452047393d849989c5cee9ad", "wx": "195b51a7cc4a21b8274a70a90de779814c3c8ca358328208c09a29f336b82d6a", "wy": "00b2416b7c92fffdc29c3b1282dd2a77a4d04df7f7452047393d849989c5cee9ad"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004195b51a7cc4a21b8274a70a90de779814c3c8ca358328208c09a29f336b82d6ab2416b7c92fffdc29c3b1282dd2a77a4d04df7f7452047393d849989c5cee9ad", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGVtRp8xKIbgnSnCpDed5gUw8jKNYMoII\nwJop8za4LWqyQWt8kv/9wpw7EoLdKnek0E3390UgRzk9hJmJxc7prQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 403, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022029798c5c45bdf58b4a7b2fdc2c46ab4af1218c7eeb9f0f27a88f1267674de3b0", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04622fc74732034bec2ddf3bc16d34b3d1f7a327dd2a8c19bab4bb4fe3a24b58aa736b2f2fae76f4dfaecc9096333b01328d51eb3fda9c9227e90d0b449983c4f0", "wx": "622fc74732034bec2ddf3bc16d34b3d1f7a327dd2a8c19bab4bb4fe3a24b58aa", "wy": "736b2f2fae76f4dfaecc9096333b01328d51eb3fda9c9227e90d0b449983c4f0"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004622fc74732034bec2ddf3bc16d34b3d1f7a327dd2a8c19bab4bb4fe3a24b58aa736b2f2fae76f4dfaecc9096333b01328d51eb3fda9c9227e90d0b449983c4f0", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEYi/HRzIDS+wt3zvBbTSz0fejJ90qjBm6\ntLtP46JLWKpzay8vrnb0367MkJYzOwEyjVHrP9qckifpDQtEmYPE8A==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 404, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02200b70f22ca2bb3cefadca1a5711fa3a59f4695385eb5aedf3495d0b6d00f8fd85", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "041f7f85caf2d7550e7af9b65023ebb4dce3450311692309db269969b834b611c70827f45b78020ecbbaf484fdd5bfaae6870f1184c21581baf6ef82bd7b530f93", "wx": "1f7f85caf2d7550e7af9b65023ebb4dce3450311692309db269969b834b611c7", "wy": "0827f45b78020ecbbaf484fdd5bfaae6870f1184c21581baf6ef82bd7b530f93"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200041f7f85caf2d7550e7af9b65023ebb4dce3450311692309db269969b834b611c70827f45b78020ecbbaf484fdd5bfaae6870f1184c21581baf6ef82bd7b530f93", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEH3+FyvLXVQ56+bZQI+u03ONFAxFpIwnb\nJplpuDS2EccIJ/RbeAIOy7r0hP3Vv6rmhw8RhMIVgbr274K9e1MPkw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 405, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022016e1e459457679df5b9434ae23f474b3e8d2a70bd6b5dbe692ba16da01f1fb0a", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0449c197dc80ad1da47a4342b93893e8e1fb0bb94fc33a83e783c00b24c781377aefc20da92bac762951f72474becc734d4cc22ba81b895e282fdac4df7af0f37d", "wx": "49c197dc80ad1da47a4342b93893e8e1fb0bb94fc33a83e783c00b24c781377a", "wy": "00efc20da92bac762951f72474becc734d4cc22ba81b895e282fdac4df7af0f37d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000449c197dc80ad1da47a4342b93893e8e1fb0bb94fc33a83e783c00b24c781377aefc20da92bac762951f72474becc734d4cc22ba81b895e282fdac4df7af0f37d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEScGX3ICtHaR6Q0K5OJPo4fsLuU/DOoPn\ng8ALJMeBN3rvwg2pK6x2KVH3JHS+zHNNTMIrqBuJXigv2sTfevDzfQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 406, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202252d685e831b6cf095e4f0535eeaf0ddd3bfa91c210c9d9dc17224702eaf88f", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d8cb68517b616a56400aa3868635e54b6f699598a2f6167757654980baf6acbe7ec8cf449c849aa03461a30efada41453c57c6e6fbc93bbc6fa49ada6dc0555c", "wx": "00d8cb68517b616a56400aa3868635e54b6f699598a2f6167757654980baf6acbe", "wy": "7ec8cf449c849aa03461a30efada41453c57c6e6fbc93bbc6fa49ada6dc0555c"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d8cb68517b616a56400aa3868635e54b6f699598a2f6167757654980baf6acbe7ec8cf449c849aa03461a30efada41453c57c6e6fbc93bbc6fa49ada6dc0555c", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE2MtoUXthalZACqOGhjXlS29plZii9hZ3\nV2VJgLr2rL5+yM9EnISaoDRhow762kFFPFfG5vvJO7xvpJrabcBVXA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 407, "comment": "edge case for u1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022075135abd7c425b60371a477f09ce0f274f64a8c6b061a07b5d63e93c65046c53", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04030713fb63f2aa6fe2cadf1b20efc259c77445dafa87dac398b84065ca347df3b227818de1a39b589cb071d83e5317cccdc2338e51e312fe31d8dc34a4801750", "wx": "030713fb63f2aa6fe2cadf1b20efc259c77445dafa87dac398b84065ca347df3", "wy": "00b227818de1a39b589cb071d83e5317cccdc2338e51e312fe31d8dc34a4801750"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004030713fb63f2aa6fe2cadf1b20efc259c77445dafa87dac398b84065ca347df3b227818de1a39b589cb071d83e5317cccdc2338e51e312fe31d8dc34a4801750", "publicKeyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAwcT+2Pyqm/iyt8bIO/CWcd0Rdr6h9rD\nmLhAZco0ffOyJ4GN4aObWJywcdg+UxfMzcIzjlHjEv4x2Nw0pIAXUA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 408, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b17", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04babb3677b0955802d8e929a41355640eaf1ea1353f8a771331c4946e3480afa7252f196c87ed3d2a59d3b1b559137fed0013fecefc19fb5a92682b9bca51b950", "wx": "00babb3677b0955802d8e929a41355640eaf1ea1353f8a771331c4946e3480afa7", "wy": "252f196c87ed3d2a59d3b1b559137fed0013fecefc19fb5a92682b9bca51b950"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004babb3677b0955802d8e929a41355640eaf1ea1353f8a771331c4946e3480afa7252f196c87ed3d2a59d3b1b559137fed0013fecefc19fb5a92682b9bca51b950", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEurs2d7CVWALY6SmkE1VkDq8eoTU/incT\nMcSUbjSAr6clLxlsh+09KlnTsbVZE3/tABP+zvwZ+1qSaCubylG5UA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 409, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203e888377ac6c71ac9dec3fdb9b56c9feaf0cfaca9f827fc5eb65fc3eac811210", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "041aab2018793471111a8a0e9b143fde02fc95920796d3a63de329b424396fba60bbe4130705174792441b318d3aa31dfe8577821e9b446ec573d272e036c4ebe9", "wx": "1aab2018793471111a8a0e9b143fde02fc95920796d3a63de329b424396fba60", "wy": "00bbe4130705174792441b318d3aa31dfe8577821e9b446ec573d272e036c4ebe9"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200041aab2018793471111a8a0e9b143fde02fc95920796d3a63de329b424396fba60bbe4130705174792441b318d3aa31dfe8577821e9b446ec573d272e036c4ebe9", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGqsgGHk0cREaig6bFD/eAvyVkgeW06Y9\n4ym0JDlvumC75BMHBRdHkkQbMY06ox3+hXeCHptEbsVz0nLgNsTr6Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 410, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022030bbb794db588363b40679f6c182a50d3ce9679acdd3ffbe36d7813dacbdc818", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048cb0b909499c83ea806cd885b1dd467a0119f06a88a0276eb0cfda274535a8ff47b5428833bc3f2c8bf9d9041158cf33718a69961cd01729bc0011d1e586ab75", "wx": "008cb0b909499c83ea806cd885b1dd467a0119f06a88a0276eb0cfda274535a8ff", "wy": "47b5428833bc3f2c8bf9d9041158cf33718a69961cd01729bc0011d1e586ab75"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048cb0b909499c83ea806cd885b1dd467a0119f06a88a0276eb0cfda274535a8ff47b5428833bc3f2c8bf9d9041158cf33718a69961cd01729bc0011d1e586ab75", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjLC5CUmcg+qAbNiFsd1GegEZ8GqIoCdu\nsM/aJ0U1qP9HtUKIM7w/LIv52QQRWM8zcYpplhzQFym8ABHR5YardQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 411, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202c37fd995622c4fb7fffffffffffffffc7cee745110cb45ab558ed7c90c15a2f", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048f03cf1a42272bb1532723093f72e6feeac85e1700e9fbe9a6a2dd642d74bf5d3b89a7189dad8cf75fc22f6f158aa27f9c2ca00daca785be3358f2bda3862ca0", "wx": "008f03cf1a42272bb1532723093f72e6feeac85e1700e9fbe9a6a2dd642d74bf5d", "wy": "3b89a7189dad8cf75fc22f6f158aa27f9c2ca00daca785be3358f2bda3862ca0"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048f03cf1a42272bb1532723093f72e6feeac85e1700e9fbe9a6a2dd642d74bf5d3b89a7189dad8cf75fc22f6f158aa27f9c2ca00daca785be3358f2bda3862ca0", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjwPPGkInK7FTJyMJP3Lm/urIXhcA6fvp\npqLdZC10v107iacYna2M91/CL28ViqJ/nCygDaynhb4zWPK9o4YsoA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 412, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02207fd995622c4fb7ffffffffffffffffff5d883ffab5b32652ccdcaa290fccb97d", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0444de3b9c7a57a8c9e820952753421e7d987bb3d79f71f013805c897e018f8acea2460758c8f98d3fdce121a943659e372c326fff2e5fc2ae7fa3f79daae13c12", "wx": "44de3b9c7a57a8c9e820952753421e7d987bb3d79f71f013805c897e018f8ace", "wy": "00a2460758c8f98d3fdce121a943659e372c326fff2e5fc2ae7fa3f79daae13c12"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000444de3b9c7a57a8c9e820952753421e7d987bb3d79f71f013805c897e018f8acea2460758c8f98d3fdce121a943659e372c326fff2e5fc2ae7fa3f79daae13c12", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERN47nHpXqMnoIJUnU0IefZh7s9efcfAT\ngFyJfgGPis6iRgdYyPmNP9zhIalDZZ43LDJv/y5fwq5/o/edquE8Eg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 413, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304302207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc021f4cd53ba7608fffffffffffffffffffff9e5cf143e2539626190a3ab09cce47", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046fb8b2b48e33031268ad6a517484dc8839ea90f6669ea0c7ac3233e2ac31394a0ac8bbe7f73c2ff4df9978727ac1dfc2fd58647d20f31f99105316b64671f204", "wx": "6fb8b2b48e33031268ad6a517484dc8839ea90f6669ea0c7ac3233e2ac31394a", "wy": "0ac8bbe7f73c2ff4df9978727ac1dfc2fd58647d20f31f99105316b64671f204"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046fb8b2b48e33031268ad6a517484dc8839ea90f6669ea0c7ac3233e2ac31394a0ac8bbe7f73c2ff4df9978727ac1dfc2fd58647d20f31f99105316b64671f204", "publicKeyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEb7iytI4zAxJorWpRdITciDnqkPZmnqDH\nrDIz4qwxOUoKyLvn9zwv9N+ZeHJ6wd/C/VhkfSDzH5kQUxa2RnHyBA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 414, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02205622c4fb7fffffffffffffffffffffff928a8f1c7ac7bec1808b9f61c01ec327", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04bea71122a048693e905ff602b3cf9dd18af69b9fc9d8431d2b1dd26b942c95e6f43c7b8b95eb62082c12db9dbda7fe38e45cbe4a4886907fb81bdb0c5ea9246c", "wx": "00bea71122a048693e905ff602b3cf9dd18af69b9fc9d8431d2b1dd26b942c95e6", "wy": "00f43c7b8b95eb62082c12db9dbda7fe38e45cbe4a4886907fb81bdb0c5ea9246c"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004bea71122a048693e905ff602b3cf9dd18af69b9fc9d8431d2b1dd26b942c95e6f43c7b8b95eb62082c12db9dbda7fe38e45cbe4a4886907fb81bdb0c5ea9246c", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEvqcRIqBIaT6QX/YCs8+d0Yr2m5/J2EMd\nKx3Sa5Qsleb0PHuLletiCCwS2529p/445Fy+SkiGkH+4G9sMXqkkbA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 415, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022044104104104104104104104104104103b87853fd3b7d3f8e175125b4382f25ed", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04da918c731ba06a20cb94ef33b778e981a404a305f1941fe33666b45b03353156e2bb2694f575b45183be78e5c9b5210bf3bf488fd4c8294516d89572ca4f5391", "wx": "00da918c731ba06a20cb94ef33b778e981a404a305f1941fe33666b45b03353156", "wy": "00e2bb2694f575b45183be78e5c9b5210bf3bf488fd4c8294516d89572ca4f5391"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004da918c731ba06a20cb94ef33b778e981a404a305f1941fe33666b45b03353156e2bb2694f575b45183be78e5c9b5210bf3bf488fd4c8294516d89572ca4f5391", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE2pGMcxugaiDLlO8zt3jpgaQEowXxlB/j\nNma0WwM1MVbiuyaU9XW0UYO+eOXJtSEL879Ij9TIKUUW2JVyyk9TkQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 416, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202739ce739ce739ce739ce739ce739ce705560298d1f2f08dc419ac273a5b54d9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "043007e92c3937dade7964dfa35b0eff031f7eb02aed0a0314411106cdeb70fe3d5a7546fc0552997b20e3d6f413e75e2cb66e116322697114b79bac734bfc4dc5", "wx": "3007e92c3937dade7964dfa35b0eff031f7eb02aed0a0314411106cdeb70fe3d", "wy": "5a7546fc0552997b20e3d6f413e75e2cb66e116322697114b79bac734bfc4dc5"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200043007e92c3937dade7964dfa35b0eff031f7eb02aed0a0314411106cdeb70fe3d5a7546fc0552997b20e3d6f413e75e2cb66e116322697114b79bac734bfc4dc5", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEMAfpLDk32t55ZN+jWw7/Ax9+sCrtCgMU\nQREGzetw/j1adUb8BVKZeyDj1vQT514stm4RYyJpcRS3m6xzS/xNxQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 417, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02204888888888888888888888888888888831c83ae82ebe0898776b4c69d11f88de", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0460e734ef5624d3cbf0ddd375011bd663d6d6aebc644eb599fdf98dbdcd18ce9bd2d90b3ac31f139af832cccf6ccbbb2c6ea11fa97370dc9906da474d7d8a7567", "wx": "60e734ef5624d3cbf0ddd375011bd663d6d6aebc644eb599fdf98dbdcd18ce9b", "wy": "00d2d90b3ac31f139af832cccf6ccbbb2c6ea11fa97370dc9906da474d7d8a7567"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000460e734ef5624d3cbf0ddd375011bd663d6d6aebc644eb599fdf98dbdcd18ce9bd2d90b3ac31f139af832cccf6ccbbb2c6ea11fa97370dc9906da474d7d8a7567", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEYOc071Yk08vw3dN1ARvWY9bWrrxkTrWZ\n/fmNvc0YzpvS2Qs6wx8TmvgyzM9sy7ssbqEfqXNw3JkG2kdNfYp1Zw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 418, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206492492492492492492492492492492406dd3a19b8d5fb875235963c593bd2d3", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0485a900e97858f693c0b7dfa261e380dad6ea046d1f65ddeeedd5f7d8af0ba33769744d15add4f6c0bc3b0da2aec93b34cb8c65f9340ddf74e7b0009eeeccce3c", "wx": "0085a900e97858f693c0b7dfa261e380dad6ea046d1f65ddeeedd5f7d8af0ba337", "wy": "69744d15add4f6c0bc3b0da2aec93b34cb8c65f9340ddf74e7b0009eeeccce3c"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000485a900e97858f693c0b7dfa261e380dad6ea046d1f65ddeeedd5f7d8af0ba33769744d15add4f6c0bc3b0da2aec93b34cb8c65f9340ddf74e7b0009eeeccce3c", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEhakA6XhY9pPAt9+iYeOA2tbqBG0fZd3u\n7dX32K8LozdpdE0VrdT2wLw7DaKuyTs0y4xl+TQN33TnsACe7szOPA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 419, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b15", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0438066f75d88efc4c93de36f49e037b234cc18b1de5608750a62cab0345401046a3e84bed8cfcb819ef4d550444f2ce4b651766b69e2e2901f88836ff90034fed", "wx": "38066f75d88efc4c93de36f49e037b234cc18b1de5608750a62cab0345401046", "wy": "00a3e84bed8cfcb819ef4d550444f2ce4b651766b69e2e2901f88836ff90034fed"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000438066f75d88efc4c93de36f49e037b234cc18b1de5608750a62cab0345401046a3e84bed8cfcb819ef4d550444f2ce4b651766b69e2e2901f88836ff90034fed", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEOAZvddiO/EyT3jb0ngN7I0zBix3lYIdQ\npiyrA0VAEEaj6EvtjPy4Ge9NVQRE8s5LZRdmtp4uKQH4iDb/kANP7Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 420, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b17", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0498f68177dc95c1b4cbfa5245488ca523a7d5629470d035d621a443c72f39aabfa33d29546fa1c648f2c7d5ccf70cf1ce4ab79b5db1ac059dbecd068dbdff1b89", "wx": "0098f68177dc95c1b4cbfa5245488ca523a7d5629470d035d621a443c72f39aabf", "wy": "00a33d29546fa1c648f2c7d5ccf70cf1ce4ab79b5db1ac059dbecd068dbdff1b89"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000498f68177dc95c1b4cbfa5245488ca523a7d5629470d035d621a443c72f39aabfa33d29546fa1c648f2c7d5ccf70cf1ce4ab79b5db1ac059dbecd068dbdff1b89", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEmPaBd9yVwbTL+lJFSIylI6fVYpRw0DXW\nIaRDxy85qr+jPSlUb6HGSPLH1cz3DPHOSrebXbGsBZ2+zQaNvf8biQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 421, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "045c2bbfa23c9b9ad07f038aa89b4930bf267d9401e4255de9e8da0a5078ec8277e3e882a31d5e6a379e0793983ccded39b95c4353ab2ff01ea5369ba47b0c3191", "wx": "5c2bbfa23c9b9ad07f038aa89b4930bf267d9401e4255de9e8da0a5078ec8277", "wy": "00e3e882a31d5e6a379e0793983ccded39b95c4353ab2ff01ea5369ba47b0c3191"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200045c2bbfa23c9b9ad07f038aa89b4930bf267d9401e4255de9e8da0a5078ec8277e3e882a31d5e6a379e0793983ccded39b95c4353ab2ff01ea5369ba47b0c3191", "publicKeyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEXCu/ojybmtB/A4qom0kwvyZ9lAHkJV3p\n6NoKUHjsgnfj6IKjHV5qN54Hk5g8ze05uVxDU6sv8B6lNpukewwxkQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 422, "comment": "edge case for u2", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220185ddbca6dac41b1da033cfb60c152869e74b3cd66e9ffdf1b6bc09ed65ee40c", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "042ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a3853547808298448edb5e701ade84cd5fb1ac9567ba5e8fb68a6b933ec4b5cc84cc", "wx": "2ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a385", "wy": "3547808298448edb5e701ade84cd5fb1ac9567ba5e8fb68a6b933ec4b5cc84cc"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200042ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a3853547808298448edb5e701ade84cd5fb1ac9567ba5e8fb68a6b933ec4b5cc84cc", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAELqcTNDIznGnSf5smcoG9Ld1fGdYzjUAK\nBc02R7FXo4U1R4CCmESO215wGt6EzV+xrJVnul6Ptoprkz7EtcyEzA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 423, "comment": "point duplication during verification", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3044022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022029ed3d67b3d505be95580d77d5b792b436881179b2b6b2e04c5fe592d38d82d9", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "042ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a385cab87f7d67bb7124a18fe5217b32a04e536a9845a1704975946cc13a4a337763", "wx": "2ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a385", "wy": "00cab87f7d67bb7124a18fe5217b32a04e536a9845a1704975946cc13a4a337763"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200042ea7133432339c69d27f9b267281bd2ddd5f19d6338d400a05cd3647b157a385cab87f7d67bb7124a18fe5217b32a04e536a9845a1704975946cc13a4a337763", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAELqcTNDIznGnSf5smcoG9Ld1fGdYzjUAK\nBc02R7FXo4XKuH99Z7txJKGP5SF7MqBOU2qYRaFwSXWUbME6SjN3Yw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 424, "comment": "duplication bug", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3044022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022029ed3d67b3d505be95580d77d5b792b436881179b2b6b2e04c5fe592d38d82d9", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048aa2c64fa9c6437563abfbcbd00b2048d48c18c152a2a6f49036de7647ebe82e1ce64387995c68a060fa3bc0399b05cc06eec7d598f75041a4917e692b7f51ff", "wx": "008aa2c64fa9c6437563abfbcbd00b2048d48c18c152a2a6f49036de7647ebe82e", "wy": "1ce64387995c68a060fa3bc0399b05cc06eec7d598f75041a4917e692b7f51ff"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048aa2c64fa9c6437563abfbcbd00b2048d48c18c152a2a6f49036de7647ebe82e1ce64387995c68a060fa3bc0399b05cc06eec7d598f75041a4917e692b7f51ff", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEiqLGT6nGQ3Vjq/vL0AsgSNSMGMFSoqb0\nkDbedkfr6C4c5kOHmVxooGD6O8A5mwXMBu7H1Zj3UEGkkX5pK39R/w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 425, "comment": "comparison with point at infinity ", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0022033333333333333333333333333333332f222f8faefdb533f265d461c29a47373", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04391427ff7ee78013c14aec7d96a8a062209298a783835e94fd6549d502fff71fdd6624ec343ad9fcf4d9872181e59f842f9ba4cccae09a6c0972fb6ac6b4c6bd", "wx": "391427ff7ee78013c14aec7d96a8a062209298a783835e94fd6549d502fff71f", "wy": "00dd6624ec343ad9fcf4d9872181e59f842f9ba4cccae09a6c0972fb6ac6b4c6bd"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004391427ff7ee78013c14aec7d96a8a062209298a783835e94fd6549d502fff71fdd6624ec343ad9fcf4d9872181e59f842f9ba4cccae09a6c0972fb6ac6b4c6bd", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEORQn/37ngBPBSux9lqigYiCSmKeDg16U\n/WVJ1QL/9x/dZiTsNDrZ/PTZhyGB5Z+EL5ukzMrgmmwJcvtqxrTGvQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 426, "comment": "extreme value for k and edgecase s", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04e762b8a219b4f180219cc7a9059245e4961bd191c03899789c7a34b89e8c138ec1533ef0419bb7376e0bfde9319d10a06968791d9ea0eed9c1ce6345aed9759e", "wx": "00e762b8a219b4f180219cc7a9059245e4961bd191c03899789c7a34b89e8c138e", "wy": "00c1533ef0419bb7376e0bfde9319d10a06968791d9ea0eed9c1ce6345aed9759e"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004e762b8a219b4f180219cc7a9059245e4961bd191c03899789c7a34b89e8c138ec1533ef0419bb7376e0bfde9319d10a06968791d9ea0eed9c1ce6345aed9759e", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE52K4ohm08YAhnMepBZJF5JYb0ZHAOJl4\nnHo0uJ6ME47BUz7wQZu3N24L/ekxnRCgaWh5HZ6g7tnBzmNFrtl1ng==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 427, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "049aedb0d281db164e130000c5697fae0f305ef848be6fffb43ac593fbb950e952fa6f633359bdcd82b56b0b9f965b037789d46b9a8141b791b2aefa713f96c175", "wx": "009aedb0d281db164e130000c5697fae0f305ef848be6fffb43ac593fbb950e952", "wy": "00fa6f633359bdcd82b56b0b9f965b037789d46b9a8141b791b2aefa713f96c175"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200049aedb0d281db164e130000c5697fae0f305ef848be6fffb43ac593fbb950e952fa6f633359bdcd82b56b0b9f965b037789d46b9a8141b791b2aefa713f96c175", "publicKeyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEmu2w0oHbFk4TAADFaX+uDzBe+Ei+b/+0\nOsWT+7lQ6VL6b2MzWb3NgrVrC5+WWwN3idRrmoFBt5GyrvpxP5bBdQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 428, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048ad445db62816260e4e687fd1884e48b9fc0636d031547d63315e792e19bfaee1de64f99d5f1cd8b6ec9cb0f787a654ae86993ba3db1008ef43cff0684cb22bd", "wx": "008ad445db62816260e4e687fd1884e48b9fc0636d031547d63315e792e19bfaee", "wy": "1de64f99d5f1cd8b6ec9cb0f787a654ae86993ba3db1008ef43cff0684cb22bd"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048ad445db62816260e4e687fd1884e48b9fc0636d031547d63315e792e19bfaee1de64f99d5f1cd8b6ec9cb0f787a654ae86993ba3db1008ef43cff0684cb22bd", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEitRF22KBYmDk5of9GITki5/AY20DFUfW\nMxXnkuGb+u4d5k+Z1fHNi27Jyw94emVK6GmTuj2xAI70PP8GhMsivQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 429, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "041f5799c95be89063b24f26e40cb928c1a868a76fb0094607e8043db409c91c32e75724e813a4191e3a839007f08e2e897388b06d4a00de6de60e536d91fab566", "wx": "1f5799c95be89063b24f26e40cb928c1a868a76fb0094607e8043db409c91c32", "wy": "00e75724e813a4191e3a839007f08e2e897388b06d4a00de6de60e536d91fab566"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200041f5799c95be89063b24f26e40cb928c1a868a76fb0094607e8043db409c91c32e75724e813a4191e3a839007f08e2e897388b06d4a00de6de60e536d91fab566", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEH1eZyVvokGOyTybkDLkowahop2+wCUYH\n6AQ9tAnJHDLnVyToE6QZHjqDkAfwji6Jc4iwbUoA3m3mDlNtkfq1Zg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 430, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04a3331a4e1b4223ec2c027edd482c928a14ed358d93f1d4217d39abf69fcb5ccc28d684d2aaabcd6383775caa6239de26d4c6937bb603ecb4196082f4cffd509d", "wx": "00a3331a4e1b4223ec2c027edd482c928a14ed358d93f1d4217d39abf69fcb5ccc", "wy": "28d684d2aaabcd6383775caa6239de26d4c6937bb603ecb4196082f4cffd509d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004a3331a4e1b4223ec2c027edd482c928a14ed358d93f1d4217d39abf69fcb5ccc28d684d2aaabcd6383775caa6239de26d4c6937bb603ecb4196082f4cffd509d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEozMaThtCI+wsAn7dSCySihTtNY2T8dQh\nfTmr9p/LXMwo1oTSqqvNY4N3XKpiOd4m1MaTe7YD7LQZYIL0z/1QnQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 431, "comment": "extreme value for k", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee502200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "043f3952199774c7cf39b38b66cb1042a6260d8680803845e4d433adba3bb248185ea495b68cbc7ed4173ee63c9042dc502625c7eb7e21fb02ca9a9114e0a3a18d", "wx": "3f3952199774c7cf39b38b66cb1042a6260d8680803845e4d433adba3bb24818", "wy": "5ea495b68cbc7ed4173ee63c9042dc502625c7eb7e21fb02ca9a9114e0a3a18d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200043f3952199774c7cf39b38b66cb1042a6260d8680803845e4d433adba3bb248185ea495b68cbc7ed4173ee63c9042dc502625c7eb7e21fb02ca9a9114e0a3a18d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEPzlSGZd0x885s4tmyxBCpiYNhoCAOEXk\n1DOtujuySBhepJW2jLx+1Bc+5jyQQtxQJiXH634h+wLKmpEU4KOhjQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 432, "comment": "extreme value for k and edgecase s", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04cdfb8c0f422e144e137c2412c86c171f5fe3fa3f5bbb544e9076288f3ced786e054fd0721b77c11c79beacb3c94211b0a19bda08652efeaf92513a3b0a163698", "wx": "00cdfb8c0f422e144e137c2412c86c171f5fe3fa3f5bbb544e9076288f3ced786e", "wy": "054fd0721b77c11c79beacb3c94211b0a19bda08652efeaf92513a3b0a163698"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004cdfb8c0f422e144e137c2412c86c171f5fe3fa3f5bbb544e9076288f3ced786e054fd0721b77c11c79beacb3c94211b0a19bda08652efeaf92513a3b0a163698", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzfuMD0IuFE4TfCQSyGwXH1/j+j9bu1RO\nkHYojzzteG4FT9ByG3fBHHm+rLPJQhGwoZvaCGUu/q+SUTo7ChY2mA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 433, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0473598a6a1c68278fa6bfd0ce4064e68235bc1c0f6b20a928108be336730f87e3cbae612519b5032ecc85aed811271a95fe7939d5d3460140ba318f4d14aba31d", "wx": "73598a6a1c68278fa6bfd0ce4064e68235bc1c0f6b20a928108be336730f87e3", "wy": "00cbae612519b5032ecc85aed811271a95fe7939d5d3460140ba318f4d14aba31d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000473598a6a1c68278fa6bfd0ce4064e68235bc1c0f6b20a928108be336730f87e3cbae612519b5032ecc85aed811271a95fe7939d5d3460140ba318f4d14aba31d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEc1mKahxoJ4+mv9DOQGTmgjW8HA9rIKko\nEIvjNnMPh+PLrmElGbUDLsyFrtgRJxqV/nk51dNGAUC6MY9NFKujHQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 434, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0458debd9a7ee2c9d59132478a5440ae4d5d7ed437308369f92ea86c82183f10a16773e76f5edbf4da0e4f1bdffac0f57257e1dfa465842931309a24245fda6a5d", "wx": "58debd9a7ee2c9d59132478a5440ae4d5d7ed437308369f92ea86c82183f10a1", "wy": "6773e76f5edbf4da0e4f1bdffac0f57257e1dfa465842931309a24245fda6a5d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000458debd9a7ee2c9d59132478a5440ae4d5d7ed437308369f92ea86c82183f10a16773e76f5edbf4da0e4f1bdffac0f57257e1dfa465842931309a24245fda6a5d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEWN69mn7iydWRMkeKVECuTV1+1Dcwg2n5\nLqhsghg/EKFnc+dvXtv02g5PG9/6wPVyV+HfpGWEKTEwmiQkX9pqXQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 435, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "048b904de47967340c5f8c3572a720924ef7578637feab1949acb241a5a6ac3f5b950904496f9824b1d63f3313bae21b89fae89afdfc811b5ece03fd5aa301864f", "wx": "008b904de47967340c5f8c3572a720924ef7578637feab1949acb241a5a6ac3f5b", "wy": "00950904496f9824b1d63f3313bae21b89fae89afdfc811b5ece03fd5aa301864f"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200048b904de47967340c5f8c3572a720924ef7578637feab1949acb241a5a6ac3f5b950904496f9824b1d63f3313bae21b89fae89afdfc811b5ece03fd5aa301864f", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEi5BN5HlnNAxfjDVypyCSTvdXhjf+qxlJ\nrLJBpaasP1uVCQRJb5gksdY/MxO64huJ+uia/fyBG17OA/1aowGGTw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 436, "comment": "extreme value for k and s^-1", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04f4892b6d525c771e035f2a252708f3784e48238604b4f94dc56eaa1e546d941a346b1aa0bce68b1c50e5b52f509fb5522e5c25e028bc8f863402edb7bcad8b1b", "wx": "00f4892b6d525c771e035f2a252708f3784e48238604b4f94dc56eaa1e546d941a", "wy": "346b1aa0bce68b1c50e5b52f509fb5522e5c25e028bc8f863402edb7bcad8b1b"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004f4892b6d525c771e035f2a252708f3784e48238604b4f94dc56eaa1e546d941a346b1aa0bce68b1c50e5b52f509fb5522e5c25e028bc8f863402edb7bcad8b1b", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE9IkrbVJcdx4DXyolJwjzeE5II4YEtPlN\nxW6qHlRtlBo0axqgvOaLHFDltS9Qn7VSLlwl4Ci8j4Y0Au23vK2LGw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 437, "comment": "extreme value for k", "flags": ["ArithmeticError"], "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179802200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5hIOtp3JqPEZV2k+/wOEQio/Re0SKaFVBmcR9CP+xDUuA==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 438, "comment": "public key shares x-coordinate with generator", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3045022100bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca60502302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid"}, {"tcId": 439, "comment": "public key shares x-coordinate with generator", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3044022044a5ad0bd0636d9e12bc9e0a6bdd5e1bba77f523842193b3b82e448e05d5f11e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "00b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "publicKeyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5i3xSWI2Vw7mqJbBAPx7vdXAuhLt1l6q+ZjuC9vBO8ndw==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 440, "comment": "public key shares x-coordinate with generator", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3045022100bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca60502302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid"}, {"tcId": 441, "comment": "public key shares x-coordinate with generator", "flags": ["PointDuplication"], "msg": "313233343030", "sig": "3044022044a5ad0bd0636d9e12bc9e0a6bdd5e1bba77f523842193b3b82e448e05d5f11e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "wx": "782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963", "wy": "00af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeCyO0X47Kng7VGTzOwllKnHGeOBexR6E\n4rz8Zjo96WOvmstCgLjH98QvTvmrpiRewewXEv04oPqWQY2M1qphUg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 442, "comment": "pseudorandom signature", "flags": ["ValidSignature"], "msg": "", "sig": "3045022100f80ae4f96cdbc9d853f83d47aae225bf407d51c56b7776cd67d0dc195d99a9dc02204cfc1d941e08cb9aceadde0f4ccead76b30d332fc442115d50e673e28686b70b", "result": "valid"}, {"tcId": 443, "comment": "pseudorandom signature", "flags": ["ValidSignature"], "msg": "4d7367", "sig": "30440220109cd8ae0374358984a8249c0a843628f2835ffad1df1a9a69aa2fe72355545c02205390ff250ac4274e1cb25cd6ca6491f6b91281e32f5b264d87977aed4a94e77b", "result": "valid"}, {"tcId": 444, "comment": "pseudorandom signature", "flags": ["ValidSignature"], "msg": "313233343030", "sig": "3045022100d035ee1f17fdb0b2681b163e33c359932659990af77dca632012b30b27a057b302201939d9f3b2858bc13e3474cb50e6a82be44faa71940f876c1cba4c3e989202b6", "result": "valid"}, {"tcId": 445, "comment": "pseudorandom signature", "flags": ["ValidSignature"], "msg": "0000000000000000000000000000000000000000", "sig": "304402204f053f563ad34b74fd8c9934ce59e79c2eb8e6eca0fef5b323ca67d5ac7ed23802204d4b05daa0719e773d8617dce5631c5fd6f59c9bdc748e4b55c970040af01be5", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "01060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv8AAAABBgSS1aVnPg8l2NUPt+WMSdhtRtQhaVXgqj1A4Q==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 446, "comment": "y-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "304402206d6a4f556ccce154e7fb9f19e76c3deca13d59cc2aeb4ecad968aab2ded45965022053b9fa74803ede0fc4441bf683d56c564d3e274e09ccf47390badd1471c05fb7", "result": "valid"}, {"tcId": 447, "comment": "y-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3044022100aad503de9b9fd66b948e9acf596f0a0e65e700b28b26ec56e6e45e846489b3c4021f0ddc3a2f89abb817bb85c062ce02f823c63fc26b269e0bc9b84d81a5aa123d", "result": "valid"}, {"tcId": 448, "comment": "y-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "30450221009182cebd3bb8ab572e167174397209ef4b1d439af3b200cdf003620089e43225022054477c982ea019d2e1000497fc25fcee1bccae55f2ac27530ae53b29c4b356a4", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "00fffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv/////++fttKlqYwfDaJyrwSBpztieSuSvelqoeVcK7Tg==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 449, "comment": "y-coordinate of the public key is large", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "304402203854a3998aebdf2dbc28adac4181462ccac7873907ab7f212c42db0e69b56ed802203ed3f6b8a388d02f3e4df9f2ae9c1bd2c3916a686460dffcd42909cd7f82058e", "result": "valid"}, {"tcId": 450, "comment": "y-coordinate of the public key is large", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100e94dbdc38795fe5c904d8f16d969d3b587f0a25d2de90b6d8c5c53ff887e360702207a947369c164972521bb8af406813b2d9f94d2aeaa53d4c215aaa0a2578a2c5d", "result": "valid"}, {"tcId": 451, "comment": "y-coordinate of the public key is large", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3044022049fc102a08ca47b60e0858cd0284d22cddd7233f94aaffbb2db1dd2cf08425e102205b16fca5a12cdb39701697ad8e39ffd6bdec0024298afaa2326aea09200b14d6", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "wx": "013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0", "wy": "00f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAAAAAT/SIkjWTZX3PCm0irSGMYUL5QP9\nAPhGi18PcOD27nqkO8LG/SWx2CaSQcvdnbsNrJbcliMfQwcF+DhxfQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 452, "comment": "x-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3044022041efa7d3f05a0010675fcb918a45c693da4b348df21a59d6f9cd73e0d831d67a02204454ada693e5e26b7bd693236d340f80545c834577b6f73d378c7bcc534244da", "result": "valid"}, {"tcId": 453, "comment": "x-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100b615698c358b35920dd883eca625a6c5f7563970cdfc378f8fe0cee17092144c022025f47b326b5be1fb610b885153ea84d41eb4716be66a994e8779989df1c863d4", "result": "valid"}, {"tcId": 454, "comment": "x-coordinate of the public key is small", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "304502210087cf8c0eb82d44f69c60a2ff5457d3aaa322e7ec61ae5aecfd678ae1c1932b0e02203add3b115815047d6eb340a3e008989eaa0f8708d1794814729094d08d2460d3", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "0425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "wx": "25afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dffffffff", "wy": "00fa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a0342000425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEJa/WiayrrtZ8Hylt5ZQG+MVQ9XFGoLTs\nLJeHbf/////6RqduUgMi37xJHsTwzBl0IPxOpYg9j23VPDVLxPZ8NQ==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 455, "comment": "x-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3044022062f48ef71ace27bf5a01834de1f7e3f948b9dce1ca1e911d5e13d3b104471d8202205ea8f33f0c778972c4582080deda9b341857dd64514f0849a05f6964c2e34022", "result": "valid"}, {"tcId": 456, "comment": "x-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100f6b0e2f6fe020cf7c0c20137434344ed7add6c4be51861e2d14cbda472a6ffb402206416c8dd3e5c5282b306e8dc8ff34ab64cc99549232d678d714402eb6ca7aa0f", "result": "valid"}, {"tcId": 457, "comment": "x-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100db09d8460f05eff23bc7e436b67da563fa4b4edb58ac24ce201fa8a358125057022046da116754602940c8999c8d665f786c50f5772c0a3cdbda075e77eabc64df16", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "04d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "wx": "00d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb9", "wy": "3f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a03420004d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAE0S5sZrZ3NMPITSYBz1013Al+J2N/CspK\nT9t0tqrdO7k/W9/4i9VzbfiY5pkAbtdQ8RzwfFhmzXrXDHEh/////w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 458, "comment": "y-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "30440220592c41e16517f12fcabd98267674f974b588e9f35d35406c1a7bb2ed1d19b7b802203e65a06bd9f83caaeb7b00f2368d7e0dece6b12221269a9b5b765198f840a3a1", "result": "valid"}, {"tcId": 459, "comment": "y-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100be0d70887d5e40821a61b68047de4ea03debfdf51cdf4d4b195558b959a032b202207d994b2d8f1dbbeb13534eb3f6e5dccd85f5c4133c27d9e64271b1826ce1f67d", "result": "valid"}, {"tcId": 460, "comment": "y-coordinate of the public key has many trailing 1's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100fae92dfcb2ee392d270af3a5739faa26d4f97bfd39ed3cbee4d29e26af3b206a02206c9ba37f9faa6a1fd3f65f23b4e853d4692a7274240a12db7ba3884830630d16", "result": "valid"}]}, {"type": "EcdsaBitcoinVerify", "publicKey": {"type": "EcPublicKey", "curve": "secp256k1", "keySize": 256, "uncompressed": "046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "wx": "6d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000", "wy": "00e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb"}, "publicKeyDer": "3056301006072a8648ce3d020106052b8104000a034200046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "publicKeyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbUp/YNR3Sk8KqLve25U8fup5CUB+MWR1\nVmS8KAAAAADmWdNOTfONnoyeqt+6NmEsdpGVvobHeqw/NueLU4aA+w==\n-----END PUBLIC KEY-----\n", "sha": "SHA-256", "tests": [{"tcId": 461, "comment": "x-coordinate of the public key has many trailing 0's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "30440220176a2557566ffa518b11226694eb9802ed2098bfe278e5570fe1d5d7af18a94302201291df6a0ed5fc0d15098e70bcf13a009284dfd0689d3bb4be6ceeb9be1487c4", "result": "valid"}, {"tcId": 462, "comment": "x-coordinate of the public key has many trailing 0's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3044022060be20c3dbc162dd34d26780621c104bbe5dace630171b2daef0d826409ee5c20220427f7e4d889d549170bda6a9409fb1cb8b0e763d13eea7bd97f64cf41dc6e497", "result": "valid"}, {"tcId": 463, "comment": "x-coordinate of the public key has many trailing 0's", "flags": ["EdgeCasePublicKey"], "msg": "4d657373616765", "sig": "3045022100edf03cf63f658883289a1a593d1007895b9f236d27c9c1f1313089aaed6b16ae02201a4dd6fc0814dc523d1fefa81c64fbf5e618e651e7096fccadbb94cd48e5e0cd", "result": "valid"}]}]}