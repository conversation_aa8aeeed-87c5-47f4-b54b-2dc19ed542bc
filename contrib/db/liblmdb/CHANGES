LMDB 0.9 Change Log

LMDB 0.9.18 Release Engineering
	Fix robust mutex detection on glibc 2.10-11 (ITS#8330)
	Check for utf8_to_utf16 failures (ITS#7992)
	Catch strdup failure in mdb_dbi_open
	Build
		Additional makefile var tweaks (ITS#8169)
	Documentation
		Add Getting Started page


LMDB 0.9.17 Release (2015/11/30)
	Fix ITS#7377 catch calloc failure
	Fix ITS#8237 regression from ITS#7589
	Fix ITS#8238 page_split for DUPFIXED pages
	Fix ITS#8221 MDB_PAGE_FULL on delete/rebalance
	Fix ITS#8258 rebalance/split assert
	Fix ITS#8263 cursor_put cursor tracking
	Fix ITS#8264 cursor_del cursor tracking
	Fix ITS#8310 cursor_del cursor tracking
	Fix ITS#8299 mdb_del cursor tracking
	Fix ITS#8300 mdb_del cursor tracking
	Fix ITS#8304 mdb_del cursor tracking
	Fix ITS#7771 fakepage cursor tracking
	Fix ITS#7789 ensure mapsize >= pages in use
	Fix ITS#7971 mdb_txn_renew0() new reader slots
	Fix ITS#7969 use __sync_synchronize on non-x86
	Fix ITS#8311 page_split from update_key
	Fix ITS#8312 loose pages in nested txn
	Fix ITS#8313 mdb_rebalance dummy cursor
	Fix ITS#8315 dirty_room in nested txn
	Fix ITS#8323 dirty_list in nested txn
	Fix ITS#8316 page_merge cursor tracking
	Fix ITS#8321 cursor tracking
	Fix ITS#8319 mdb_load error messages
	Fix ITS#8320 mdb_load plaintext input
	Added mdb_txn_id() (ITS#7994)
	Added robust mutex support
	Miscellaneous cleanup/simplification
	Build
		Create install dirs if needed (ITS#8256)
		Fix ThreadProc decl on Win32/MSVC (ITS#8270)
		Added ssize_t typedef for MSVC (ITS#8067)
		Use ANSI apis on Windows (ITS#8069)
		Use O_SYNC if O_DSYNC,MDB_DSYNC are not defined (ITS#7209)
		Allow passing AR to make (ITS#8168)
		Allow passing mandir to make install (ITS#8169)

LMDB 0.9.16 Release (2015/08/14)
	Fix cursor EOF bug (ITS#8190)
	Fix handling of subDB records (ITS#8181)
	Fix mdb_midl_shrink() usage (ITS#8200)

LMDB 0.9.15 Release (2015/06/19)
	Fix txn init (ITS#7961,#7987)
	Fix MDB_PREV_DUP (ITS#7955,#7671)
	Fix compact of empty env (ITS#7956)
	Fix mdb_copy file mode
	Fix mdb_env_close() after failed mdb_env_open()
	Fix mdb_rebalance collapsing root (ITS#8062)
	Fix mdb_load with large values (ITS#8066)
	Fix to retry writes on EINTR (ITS#8106)
	Fix mdb_cursor_del on empty DB (ITS#8109)
	Fix MDB_INTEGERDUP key compare (ITS#8117)
	Fix error handling (ITS#7959,#8157,etc.)
	Fix race conditions (ITS#7969,7970)
	Added workaround for fdatasync bug in ext3fs
	Build
		Don't use -fPIC for static lib
		Update .gitignore (ITS#7952,#7953)
		Cleanup for "make test" (ITS#7841), "make clean", mtest*.c
		Misc. Android/Windows cleanup
	Documentation
		Fix MDB_APPEND doc
		Fix MDB_MAXKEYSIZE doc (ITS#8156)
		Fix mdb_cursor_put,mdb_cursor_del EACCES description
		Fix mdb_env_sync(MDB_RDONLY env) doc (ITS#8021)
		Clarify MDB_WRITEMAP doc (ITS#8021)
		Clarify mdb_env_open doc
		Clarify mdb_dbi_open doc

LMDB 0.9.14 Release (2014/09/20)
	Fix to support 64K page size (ITS#7713)
	Fix to persist decreased as well as increased mapsizes (ITS#7789)
	Fix cursor bug when deleting last node of a DUPSORT key
	Fix mdb_env_info to return FIXEDMAP address
	Fix ambiguous error code from writing to closed DBI (ITS#7825)
	Fix mdb_copy copying past end of file (ITS#7886)
	Fix cursor bugs from page_merge/rebalance
	Fix to dirty fewer pages in deletes (mdb_page_loose())
	Fix mdb_dbi_open creating subDBs (ITS#7917)
	Fix mdb_cursor_get(_DUP) with single value (ITS#7913)
	Fix Windows compat issues in mtests (ITS#7879)
	Add compacting variant of mdb_copy
	Add BigEndian integer key compare code
	Add mdb_dump/mdb_load utilities

LMDB 0.9.13 Release (2014/06/18)
	Fix mdb_page_alloc unlimited overflow page search
	Documentation
		Re-fix MDB_CURRENT doc (ITS#7793)
		Fix MDB_GET_MULTIPLE/MDB_NEXT_MULTIPLE doc

LMDB 0.9.12 Release (2014/06/13)
	Fix MDB_GET_BOTH regression (ITS#7875,#7681)
	Fix MDB_MULTIPLE writing multiple keys (ITS#7834)
	Fix mdb_rebalance (ITS#7829)
	Fix mdb_page_split (ITS#7815)
	Fix md_entries count (ITS#7861,#7828,#7793)
	Fix MDB_CURRENT (ITS#7793)
	Fix possible crash on Windows DLL detach
	Misc code cleanup
	Documentation
		mdb_cursor_put: cursor moves on error (ITS#7771)


LMDB 0.9.11 Release (2014/01/15)
	Add mdb_env_set_assert() (ITS#7775)
	Fix: invalidate txn on page allocation errors (ITS#7377)
	Fix xcursor tracking in mdb_cursor_del0() (ITS#7771)
	Fix corruption from deletes (ITS#7756)
	Fix Windows/MSVC build issues
	Raise safe limit of max MDB_MAXKEYSIZE
	Misc code cleanup
	Documentation
		Remove spurious note about non-overlapping flags (ITS#7665)

LMDB 0.9.10 Release (2013/11/12)
	Add MDB_NOMEMINIT option
	Fix mdb_page_split() again (ITS#7589)
	Fix MDB_NORDAHEAD definition (ITS#7734)
	Fix mdb_cursor_del() positioning (ITS#7733)
	Partial fix for larger page sizes (ITS#7713)
	Fix Windows64/MSVC build issues

LMDB 0.9.9 Release (2013/10/24)
	Add mdb_env_get_fd()
	Add MDB_NORDAHEAD option
	Add MDB_NOLOCK option
	Avoid wasting space in mdb_page_split() (ITS#7589)
	Fix mdb_page_merge() cursor fixup (ITS#7722)
	Fix mdb_cursor_del() on last delete (ITS#7718)
	Fix adding WRITEMAP on existing env (ITS#7715)
	Fix nested txns (ITS#7515)
	Fix mdb_env_copy() O_DIRECT bug (ITS#7682)
	Fix mdb_cursor_set(SET_RANGE) return code (ITS#7681)
	Fix mdb_rebalance() cursor fixup (ITS#7701)
	Misc code cleanup
	Documentation
		Note that by default, readers need write access


LMDB 0.9.8 Release (2013/09/09)
	Allow mdb_env_set_mapsize() on an open environment
	Fix mdb_dbi_flags() (ITS#7672)
	Fix mdb_page_unspill() in nested txns
	Fix mdb_cursor_get(CURRENT|NEXT) after a delete
	Fix mdb_cursor_get(DUP) to always return key (ITS#7671)
	Fix mdb_cursor_del() to always advance to next item (ITS#7670)
	Fix mdb_cursor_set(SET_RANGE) for tree with single page (ITS#7681)
	Fix mdb_env_copy() retry open if O_DIRECT fails (ITS#7682)
	Tweak mdb_page_spill() to be less aggressive
	Documentation
		Update caveats since mdb_reader_check() added in 0.9.7

LMDB 0.9.7 Release (2013/08/17)
	Don't leave stale lockfile on failed RDONLY open (ITS#7664)
	Fix mdb_page_split() ref beyond cursor depth
	Fix read txn data race (ITS#7635)
	Fix mdb_rebalance (ITS#7536, #7538)
	Fix mdb_drop() (ITS#7561)
	Misc DEBUG macro fixes
	Add MDB_NOTLS envflag
	Add mdb_env_copyfd()
	Add mdb_txn_env() (ITS#7660)
	Add mdb_dbi_flags() (ITS#7661)
	Add mdb_env_get_maxkeysize()
	Add mdb_env_reader_list()/mdb_env_reader_check()
	Add mdb_page_spill/unspill, remove hard txn size limit
	Use shorter names for semaphores (ITS#7615)
	Build
		Fix install target (ITS#7656)
	Documentation
		Misc updates for cursors, DB handles, data lifetime

LMDB 0.9.6 Release (2013/02/25)
	Many fixes/enhancements

LMDB 0.9.5 Release (2012/11/30)
	Renamed from libmdb to liblmdb
	Many fixes/enhancements
