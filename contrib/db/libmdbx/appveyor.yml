version: 0.3.2.{build}

environment:
  matrix:
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_GENERATOR: Visual Studio 16 2019
      TOOLSET: 142
      MDBX_BUILD_SHARED_LIBRARY: OFF
      MDBX_AVOID_CRT: OFF
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_GENERATOR: Visual Studio 16 2019
      TOOLSET: 142
      MDBX_BUILD_SHARED_LIBRARY: ON
      MDBX_AVOID_CRT: ON
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_GENERATOR: Visual Studio 16 2019
      TOOLSET: 142
      MDBX_BUILD_SHARED_LIBRARY: OFF
      MDBX_AVOID_CRT: ON
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_GENERATOR: Visual Studio 16 2019
      TOOLSET: 142
      MDBX_BUILD_SHARED_LIBRARY: ON
      MDBX_AVOID_CRT: OFF
    - APPVEY<PERSON>_BUILD_WORKER_IMAGE: Visual Studio 2017
      CMAKE_GENERATOR: Visual Studio 15 2017
      TOOLSET: 141
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      CMAKE_GENERATOR: Visual Studio 14 2015
      TOOLSET: 140

branches:
  except:
    - coverity_scan

configuration:
- Debug
- Release

platform:
- Win32
- x64

before_build:
- git clean -x -f -d
- git submodule sync
- git fetch --tags --prune
- git submodule update --init --recursive
- git submodule foreach --recursive git fetch --tags --prune
- cmake --version

build_script:
- ps: |
    Write-Output "*******************************************************************************"
    Write-Output "Configuration: $env:CONFIGURATION"
    Write-Output "Platform: $env:PLATFORM"
    Write-Output "Toolchain: $env:CMAKE_GENERATOR v$env:TOOLSET"
    Write-Output "Options: MDBX_AVOID_CRT=$env:MDBX_AVOID_CRT MDBX_BUILD_SHARED_LIBRARY=$env:MDBX_BUILD_SHARED_LIBRARY"
    Write-Output "*******************************************************************************"

    md _build -Force | Out-Null
    cd _build

    $generator = $env:CMAKE_GENERATOR
    if ($env:TOOLSET -lt 142) {
        if ($env:PLATFORM -eq "x64") {
            $generator = "$generator Win64"
        }
        & cmake -G "$generator" -D CMAKE_CONFIGURATION_TYPES="Debug;Release" -D MDBX_AVOID_CRT:BOOL=$env:MDBX_AVOID_CRT -D MDBX_BUILD_SHARED_LIBRARY:BOOL=$env:MDBX_BUILD_SHARED_LIBRARY ..
    } else {
        & cmake -G "$generator" -A $env:PLATFORM -D CMAKE_CONFIGURATION_TYPES="Debug;Release" -DMDBX_AVOID_CRT:BOOL=$env:MDBX_AVOID_CRT -D MDBX_BUILD_SHARED_LIBRARY:BOOL=$env:MDBX_BUILD_SHARED_LIBRARY ..
    }
    if ($LastExitCode -ne 0) {
        throw "Exec: $ErrorMessage"
    }
    Write-Output "*******************************************************************************"

    & cmake --build . --config $env:CONFIGURATION
    if ($LastExitCode -ne 0) {
        throw "Exec: $ErrorMessage"
    }
    Write-Output "*******************************************************************************"

test_script:
- ps: |
    if (($env:PLATFORM -ne "ARM") -and ($env:PLATFORM -ne "ARM64")) {
        & ./$env:CONFIGURATION/mdbx_test.exe --progress --console=no --pathname=test.db --dont-cleanup-after basic > test.log
        Get-Content test.log | Select-Object -last 42
        if ($LastExitCode -ne 0) {
            throw "Exec: $ErrorMessage"
        } else {
            & ./$env:CONFIGURATION/mdbx_chk.exe -nvv test.db | Tee-Object -file chk.log | Select-Object -last 42
        }
    }

on_failure:
- ps: Push-AppveyorArtifact \projects\libmdbx\_build\test.log
- ps: Push-AppveyorArtifact \projects\libmdbx\_build\test.db
- ps: Push-AppveyorArtifact \projects\libmdbx\_build\chk.log
