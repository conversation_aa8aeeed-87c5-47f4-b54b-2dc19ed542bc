/* This is <PERSON><PERSON>ake-template for lib<PERSON><PERSON><PERSON><PERSON>'s config.h
 ******************************************************************************/

/* *INDENT-OFF* */
/* clang-format off */

#cmakedefine HAVE_VALGRIND_MEMCHECK_H
#cmakedefine HAS_RELAXED_CONSTEXPR

#cmakedefine LTO_ENABLED
#cmakedefine MDBX_USE_VALGRIND
#cmakedefine ENABLE_GPROF
#cmakedefine ENABLE_GCOV
#cmakedefine ENABLE_ASAN

/* Common */
#cmakedefine01 MDBX_TXN_CHECKPID
#cmakedefine01 MDBX_TXN_CHECKOWNER
#cmakedefine01 MDBX_BUILD_SHARED_LIBRARY

/* Windows */
#cmakedefine01 MDBX_CONFIG_MANUAL_TLS_CALL<PERSON>CK
#cm<PERSON><PERSON><PERSON><PERSON> MDBX_AVOID_CRT

/* MacOS */
#cmakedefine01 MDBX_OSX_SPEED_INSTEADOF_DURABILITY

/* POSIX */
#cmakedefine01 MDBX_USE_ROBUST
#cmakedefine01 MDBX_USE_OFDLOCKS
#cmakedefine01 MDBX_DISABLE_GNU_SOURCE

/* Simulate "AUTO" values of tristate options */
#cmakedefine MDBX_TXN_CHECKPID_AUTO
#ifdef MDBX_TXN_CHECKPID_AUTO
#undef MDBX_TXN_CHECKPID
#endif
#cmakedefine MDBX_USE_ROBUST_AUTO
#ifdef MDBX_USE_ROBUST_AUTO
#undef MDBX_USE_ROBUST
#endif
#cmakedefine MDBX_USE_OFDLOCKS_AUTO
#ifdef MDBX_USE_OFDLOCKS_AUTO
#undef MDBX_USE_OFDLOCKS
#endif

/* Build Info */
#cmakedefine MDBX_BUILD_TIMESTAMP "@MDBX_BUILD_TIMESTAMP@"
#cmakedefine MDBX_BUILD_TARGET "@MDBX_BUILD_TARGET@"
#cmakedefine MDBX_BUILD_CONFIG "@MDBX_BUILD_CONFIG@"
#cmakedefine MDBX_BUILD_COMPILER "@MDBX_BUILD_COMPILER@"
#cmakedefine MDBX_BUILD_FLAGS "@MDBX_BUILD_FLAGS@"
#cmakedefine MDBX_BUILD_SOURCERY @MDBX_BUILD_SOURCERY@

/* *INDENT-ON* */
/* clang-format on */
