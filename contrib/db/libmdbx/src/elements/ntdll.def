LIBRARY ntdll
EXPORTS
CsrAllocateCaptureBuffer
CsrAllocateMessagePointer
CsrCaptureMessageBuffer
CsrCaptureMessageMultiUnicodeStringsInPlace
CsrCaptureMessageString
CsrCaptureTimeout
CsrClientCallServer
CsrClientConnectToServer
CsrFreeCaptureBuffer
CsrGetProcessId
CsrIdentifyAlertableThread
CsrSetPriorityClass
DbgBreakPoint
DbgPrint
DbgPrintEx
DbgPrintReturnControlC
DbgPrompt
DbgQueryDebugFilterState
DbgSetDebugFilterState
DbgUiConnectToDbg
DbgUiContinue
DbgUiConvertStateChangeStructure
DbgUiDebugActiveProcess
DbgUiGetThreadDebugObject
DbgUiIssueRemoteBreakin
DbgUiRemoteBreakin
DbgUiSetThreadDebugObject
DbgUiStopDebugging
DbgUiWaitStateChange
DbgUserBreakPoint
KiRaiseUserExceptionDispatcher
KiUserApcDispatcher
KiUserCallbackDispatcher
KiUserExceptionDispatcher
LdrAccessResource
LdrAddRefDll
LdrDisableThreadCalloutsForDll
LdrEnumResources
LdrEnumerateLoadedModules
LdrFindEntryForAddress
LdrFindResourceDirectory_U
LdrFindResourceEx_U
LdrFindResource_U
LdrFlushAlternateResourceModules
LdrGetDllHandle
LdrGetDllHandleEx
LdrGetProcedureAddress
LdrInitShimEngineDynamic
LdrInitializeThunk
LdrLoadAlternateResourceModule
LdrLoadDll
LdrLockLoaderLock
LdrProcessRelocationBlock
LdrQueryImageFileExecutionOptions
LdrQueryProcessModuleInformation
LdrSetAppCompatDllRedirectionCallback
LdrSetDllManifestProber
LdrShutdownProcess
LdrShutdownThread
LdrUnloadAlternateResourceModule
LdrUnloadDll
LdrUnlockLoaderLock
LdrVerifyImageMatchesChecksum
NlsAnsiCodePage
NlsMbCodePageTag
NlsMbOemCodePageTag
NtAcceptConnectPort
NtAccessCheck
NtAccessCheckAndAuditAlarm
NtAccessCheckByType
NtAccessCheckByTypeAndAuditAlarm
NtAccessCheckByTypeResultList
NtAccessCheckByTypeResultListAndAuditAlarm
NtAccessCheckByTypeResultListAndAuditAlarmByHandle
NtAddAtom
NtAddBootEntry
NtAdjustGroupsToken
NtAdjustPrivilegesToken
NtAlertResumeThread
NtAlertThread
NtAllocateLocallyUniqueId
NtAllocateUserPhysicalPages
NtAllocateUuids
NtAllocateVirtualMemory
NtAreMappedFilesTheSame
NtAssignProcessToJobObject
NtCallbackReturn
NtCancelIoFile
NtCancelTimer
NtClearEvent
NtClose
NtCloseObjectAuditAlarm
NtCompactKeys
NtCompareTokens
NtCompleteConnectPort
NtCompressKey
NtConnectPort
NtContinue
NtCreateDebugObject
NtCreateDirectoryObject
NtCreateEvent
NtCreateEventPair
NtCreateFile
NtCreateIoCompletion
NtCreateJobObject
NtCreateJobSet
NtCreateKey
NtCreateKeyedEvent
NtCreateMailslotFile
NtCreateMutant
NtCreateNamedPipeFile
NtCreatePagingFile
NtCreatePort
NtCreateProcess
NtCreateProcessEx
NtCreateProfile
NtCreateSection
NtCreateSemaphore
NtCreateSymbolicLinkObject
NtCreateThread
NtCreateTimer
NtCreateToken
NtCreateWaitablePort
NtDebugActiveProcess
NtDebugContinue
NtDelayExecution
NtDeleteAtom
NtDeleteBootEntry
NtDeleteFile
NtDeleteKey
NtDeleteObjectAuditAlarm
NtDeleteValueKey
NtDeviceIoControlFile
NtDisplayString
NtDuplicateObject
NtDuplicateToken
NtEnumerateBootEntries
NtEnumerateKey
NtEnumerateSystemEnvironmentValuesEx
NtEnumerateValueKey
NtExtendSection
NtFilterToken
NtFindAtom
NtFlushBuffersFile
NtFlushInstructionCache
NtFlushKey
NtFlushVirtualMemory
NtFlushWriteBuffer
NtFreeUserPhysicalPages
NtFreeVirtualMemory
NtFsControlFile
NtGetContextThread
NtGetDevicePowerState
NtGetWriteWatch
NtImpersonateAnonymousToken
NtImpersonateClientOfPort
NtImpersonateThread
NtInitializeRegistry
NtInitiatePowerAction
NtIsProcessInJob
NtIsSystemResumeAutomatic
NtListenPort
NtLoadDriver
NtLoadKey
NtLoadKey2
NtLockFile
NtLockProductActivationKeys
NtLockRegistryKey
NtLockVirtualMemory
NtMakePermanentObject
NtMakeTemporaryObject
NtMapUserPhysicalPages
NtMapUserPhysicalPagesScatter
NtMapViewOfSection
NtModifyBootEntry
NtNotifyChangeDirectoryFile
NtNotifyChangeKey
NtNotifyChangeMultipleKeys
NtOpenDirectoryObject
NtOpenEvent
NtOpenEventPair
NtOpenFile
NtOpenIoCompletion
NtOpenJobObject
NtOpenKey
NtOpenKeyedEvent
NtOpenMutant
NtOpenObjectAuditAlarm
NtOpenProcess
NtOpenProcessToken
NtOpenProcessTokenEx
NtOpenSection
NtOpenSemaphore
NtOpenSymbolicLinkObject
NtOpenThread
NtOpenThreadToken
NtOpenThreadTokenEx
NtOpenTimer
NtPlugPlayControl
NtPowerInformation
NtPrivilegeCheck
NtPrivilegeObjectAuditAlarm
NtPrivilegedServiceAuditAlarm
NtProtectVirtualMemory
NtPulseEvent
NtQueryAttributesFile
NtQueryBootEntryOrder
NtQueryBootOptions
NtQueryDebugFilterState
NtQueryDefaultLocale
NtQueryDefaultUILanguage
NtQueryDirectoryFile
NtQueryDirectoryObject
NtQueryEaFile
NtQueryEvent
NtQueryFullAttributesFile
NtQueryInformationAtom
NtQueryInformationFile
NtQueryInformationJobObject
NtQueryInformationPort
NtQueryInformationProcess
NtQueryInformationThread
NtQueryInformationToken
NtQueryInstallUILanguage
NtQueryIntervalProfile
NtQueryIoCompletion
NtQueryKey
NtQueryMultipleValueKey
NtQueryMutant
NtQueryObject
NtQueryOpenSubKeys
NtQueryPerformanceCounter
NtQueryPortInformationProcess
NtQueryQuotaInformationFile
NtQuerySection
NtQuerySecurityObject
NtQuerySemaphore
NtQuerySymbolicLinkObject
NtQuerySystemEnvironmentValue
NtQuerySystemEnvironmentValueEx
NtQuerySystemInformation
NtQuerySystemTime
NtQueryTimer
NtQueryTimerResolution
NtQueryValueKey
NtQueryVirtualMemory
NtQueryVolumeInformationFile
NtQueueApcThread
NtRaiseException
NtRaiseHardError
NtReadFile
NtReadFileScatter
NtReadRequestData
NtReadVirtualMemory
NtRegisterThreadTerminatePort
NtReleaseKeyedEvent
NtReleaseMutant
NtReleaseSemaphore
NtRemoveIoCompletion
NtRemoveProcessDebug
NtRenameKey
NtReplaceKey
NtReplyPort
NtReplyWaitReceivePort
NtReplyWaitReceivePortEx
NtReplyWaitReplyPort
NtRequestPort
NtRequestWaitReplyPort
NtResetEvent
NtResetWriteWatch
NtRestoreKey
NtResumeProcess
NtResumeThread
NtSaveKey
NtSaveKeyEx
NtSaveMergedKeys
NtSecureConnectPort
NtSetBootEntryOrder
NtSetBootOptions
NtSetContextThread
NtSetDebugFilterState
NtSetDefaultHardErrorPort
NtSetDefaultLocale
NtSetDefaultUILanguage
NtSetEaFile
NtSetEvent
NtSetEventBoostPriority
NtSetHighEventPair
NtSetHighWaitLowEventPair
NtSetInformationDebugObject
NtSetInformationFile
NtSetInformationJobObject
NtSetInformationKey
NtSetInformationObject
NtSetInformationProcess
NtSetInformationThread
NtSetInformationToken
NtSetIntervalProfile
NtSetIoCompletion
NtSetLdtEntries
NtSetLowEventPair
NtSetLowWaitHighEventPair
NtSetQuotaInformationFile
NtSetSecurityObject
NtSetSystemEnvironmentValue
NtSetSystemEnvironmentValueEx
NtSetSystemInformation
NtSetSystemPowerState
NtSetSystemTime
NtSetThreadExecutionState
NtSetTimer
NtSetTimerResolution
NtSetUuidSeed
NtSetValueKey
NtSetVolumeInformationFile
NtShutdownSystem
NtSignalAndWaitForSingleObject
NtStartProfile
NtStopProfile
NtSuspendProcess
NtSuspendThread
NtSystemDebugControl
NtTerminateJobObject
NtTerminateProcess
NtTerminateThread
NtTestAlert
NtTraceEvent
NtTranslateFilePath
NtUnloadDriver
NtUnloadKey
NtUnloadKeyEx
NtUnlockFile
NtUnlockVirtualMemory
NtUnmapViewOfSection
NtVdmControl
NtWaitForDebugEvent
NtWaitForKeyedEvent
NtWaitForMultipleObjects
NtWaitForSingleObject
NtWaitHighEventPair
NtWaitLowEventPair
NtWriteFile
NtWriteFileGather
NtWriteRequestData
NtWriteVirtualMemory
NtYieldExecution
PfxFindPrefix
PfxInitialize
PfxInsertPrefix
PfxRemovePrefix
RtlAbortRXact
RtlAbsoluteToSelfRelativeSD
RtlAcquirePebLock
RtlAcquireResourceExclusive
RtlAcquireResourceShared
RtlActivateActivationContext
RtlActivateActivationContextEx
RtlActivateActivationContextUnsafeFast
RtlAddAccessAllowedAce
RtlAddAccessAllowedAceEx
RtlAddAccessAllowedObjectAce
RtlAddAccessDeniedAce
RtlAddAccessDeniedAceEx
RtlAddAccessDeniedObjectAce
RtlAddAce
RtlAddActionToRXact
RtlAddAtomToAtomTable
RtlAddAttributeActionToRXact
RtlAddAuditAccessAce
RtlAddAuditAccessAceEx
RtlAddAuditAccessObjectAce
RtlAddCompoundAce
RtlAddRefActivationContext
RtlAddRefMemoryStream
RtlAddVectoredExceptionHandler
RtlAddressInSectionTable
RtlAdjustPrivilege
RtlAllocateAndInitializeSid
RtlAllocateHandle
RtlAllocateHeap
RtlAnsiCharToUnicodeChar
RtlAnsiStringToUnicodeSize
RtlAnsiStringToUnicodeString
RtlAppendAsciizToString
RtlAppendPathElement
RtlAppendStringToString
RtlAppendUnicodeStringToString
RtlAppendUnicodeToString
RtlApplicationVerifierStop
RtlApplyRXact
RtlApplyRXactNoFlush
RtlAreAllAccessesGranted
RtlAreAnyAccessesGranted
RtlAreBitsClear
RtlAreBitsSet
RtlAssert
RtlCancelTimer
RtlCaptureContext
RtlCaptureStackBackTrace
RtlCharToInteger
RtlCheckForOrphanedCriticalSections
RtlCheckRegistryKey
RtlClearAllBits
RtlClearBits
RtlCloneMemoryStream
RtlCommitMemoryStream
RtlCompactHeap
RtlCompareMemory
RtlCompareMemoryUlong
RtlCompareString
RtlCompareUnicodeString
RtlCompressBuffer
RtlComputeCrc32
RtlComputeImportTableHash
RtlComputePrivatizedDllName_U
RtlConsoleMultiByteToUnicodeN
RtlConvertExclusiveToShared
RtlConvertSharedToExclusive
RtlConvertSidToUnicodeString
RtlConvertToAutoInheritSecurityObject
RtlCopyLuid
RtlCopyLuidAndAttributesArray
RtlCopyMemoryStreamTo
RtlCopyOutOfProcessMemoryStreamTo
RtlCopySecurityDescriptor
RtlCopySid
RtlCopySidAndAttributesArray
RtlCopyString
RtlCopyUnicodeString
RtlCreateAcl
RtlCreateActivationContext
RtlCreateAndSetSD
RtlCreateAtomTable
RtlCreateBootStatusDataFile
RtlCreateEnvironment
RtlCreateHeap
RtlCreateProcessParameters
RtlCreateQueryDebugBuffer
RtlCreateRegistryKey
RtlCreateSecurityDescriptor
RtlCreateServiceSid
RtlCreateSystemVolumeInformationFolder
RtlCreateTagHeap
RtlCreateTimer
RtlCreateTimerQueue
RtlCreateUnicodeString
RtlCreateUnicodeStringFromAsciiz
RtlCreateUserProcess
RtlCreateUserSecurityObject
RtlCreateUserThread
RtlCustomCPToUnicodeN
RtlCutoverTimeToSystemTime
RtlDeNormalizeProcessParams
RtlDeactivateActivationContext
RtlDeactivateActivationContextUnsafeFast
RtlDebugPrintTimes
RtlDecodePointer
RtlDecodeSystemPointer
RtlDecompressBuffer
RtlDecompressFragment
RtlDefaultNpAcl
RtlDelete
RtlDeleteAce
RtlDeleteAtomFromAtomTable
RtlDeleteCriticalSection
RtlDeleteElementGenericTable
RtlDeleteElementGenericTableAvl
RtlDeleteNoSplay
RtlDeleteRegistryValue
RtlDeleteResource
RtlDeleteSecurityObject
RtlDeleteTimer
RtlDeleteTimerQueue
RtlDeleteTimerQueueEx
RtlDeregisterWait
RtlDeregisterWaitEx
RtlDestroyAtomTable
RtlDestroyEnvironment
RtlDestroyHandleTable
RtlDestroyHeap
RtlDestroyProcessParameters
RtlDestroyQueryDebugBuffer
RtlDetermineDosPathNameType_U
RtlDllShutdownInProgress
RtlDnsHostNameToComputerName
RtlDoesFileExists_U
RtlDosApplyFileIsolationRedirection_Ustr
RtlDosPathNameToNtPathName_U
RtlDosSearchPath_U
RtlDosSearchPath_Ustr
RtlDowncaseUnicodeChar
RtlDowncaseUnicodeString
RtlDumpResource
RtlDuplicateUnicodeString
RtlEmptyAtomTable
RtlEnableEarlyCriticalSectionEventCreation
RtlEncodePointer
RtlEncodeSystemPointer
RtlEnterCriticalSection
RtlEnumProcessHeaps
RtlEnumerateGenericTable
RtlEnumerateGenericTableAvl
RtlEnumerateGenericTableLikeADirectory
RtlEnumerateGenericTableWithoutSplaying
RtlEnumerateGenericTableWithoutSplayingAvl
RtlEqualComputerName
RtlEqualDomainName
RtlEqualLuid
RtlEqualPrefixSid
RtlEqualSid
RtlEqualString
RtlEqualUnicodeString
RtlEraseUnicodeString
RtlExitUserThread
RtlExpandEnvironmentStrings_U
RtlFillMemory
RtlFinalReleaseOutOfProcessMemoryStream
RtlFindActivationContextSectionGuid
RtlFindActivationContextSectionString
RtlFindCharInUnicodeString
RtlFindClearBits
RtlFindClearBitsAndSet
RtlFindClearRuns
RtlFindLastBackwardRunClear
RtlFindLeastSignificantBit
RtlFindLongestRunClear
RtlFindMessage
RtlFindMostSignificantBit
RtlFindNextForwardRunClear
RtlFindSetBits
RtlFindSetBitsAndClear
RtlFirstEntrySList
RtlFirstFreeAce
RtlFlushSecureMemoryCache
RtlFormatCurrentUserKeyPath
RtlFormatMessage
RtlFreeAnsiString
RtlFreeHandle
RtlFreeHeap
RtlFreeOemString
RtlFreeSid
RtlFreeThreadActivationContextStack
RtlFreeUnicodeString
RtlGUIDFromString
RtlGenerate8dot3Name
RtlGetAce
RtlGetActiveActivationContext
RtlGetCallersAddress
RtlGetCompressionWorkSpaceSize
RtlGetControlSecurityDescriptor
RtlGetCurrentDirectory_U
RtlGetCurrentPeb
RtlGetDaclSecurityDescriptor
RtlGetElementGenericTable
RtlGetElementGenericTableAvl
RtlGetFrame
RtlGetFullPathName_U
RtlGetGroupSecurityDescriptor
RtlGetLastNtStatus
RtlGetLastWin32Error
RtlGetLengthWithoutLastFullDosOrNtPathElement
RtlGetLengthWithoutTrailingPathSeperators
RtlGetLongestNtPathLength
RtlGetNativeSystemInformation
RtlGetNtGlobalFlags
RtlGetNtProductType
RtlGetNtVersionNumbers
RtlGetOwnerSecurityDescriptor
RtlGetProcessHeaps
RtlGetSaclSecurityDescriptor
RtlGetSecurityDescriptorRMControl
RtlGetSetBootStatusData
RtlGetUnloadEventTrace
RtlGetUserInfoHeap
RtlGetVersion
RtlHashUnicodeString
RtlIdentifierAuthoritySid
RtlImageDirectoryEntryToData
RtlImageNtHeader
RtlImageRvaToSection
RtlImageRvaToVa
RtlImpersonateSelf
RtlInitAnsiString
RtlInitCodePageTable
RtlInitMemoryStream
RtlInitNlsTables
RtlInitOutOfProcessMemoryStream
RtlInitString
RtlInitUnicodeString
RtlInitUnicodeStringEx
RtlInitializeAtomPackage
RtlInitializeBitMap
RtlInitializeContext
RtlInitializeCriticalSection
RtlInitializeCriticalSectionAndSpinCount
RtlInitializeGenericTable
RtlInitializeGenericTableAvl
RtlInitializeHandleTable
RtlInitializeRXact
RtlInitializeResource
RtlInitializeSListHead
RtlInitializeSid
RtlInsertElementGenericTable
RtlInsertElementGenericTableAvl
RtlInt64ToUnicodeString
RtlIntegerToChar
RtlIntegerToUnicodeString
RtlInterlockedFlushSList
RtlInterlockedPopEntrySList
RtlInterlockedPushEntrySList
RtlInterlockedPushListSList
RtlIpv4AddressToStringA
RtlIpv4AddressToStringExA
RtlIpv4AddressToStringExW
RtlIpv4AddressToStringW
RtlIpv4StringToAddressA
RtlIpv4StringToAddressExA
RtlIpv4StringToAddressExW
RtlIpv4StringToAddressW
RtlIpv6AddressToStringA
RtlIpv6AddressToStringExA
RtlIpv6AddressToStringExW
RtlIpv6AddressToStringW
RtlIpv6StringToAddressA
RtlIpv6StringToAddressExA
RtlIpv6StringToAddressExW
RtlIpv6StringToAddressW
RtlIsActivationContextActive
RtlIsDosDeviceName_U
RtlIsGenericTableEmpty
RtlIsGenericTableEmptyAvl
RtlIsNameLegalDOS8Dot3
RtlIsTextUnicode
RtlIsThreadWithinLoaderCallout
RtlIsValidHandle
RtlIsValidIndexHandle
RtlLargeIntegerToChar
RtlLeaveCriticalSection
RtlLengthRequiredSid
RtlLengthSecurityDescriptor
RtlLengthSid
RtlLocalTimeToSystemTime
RtlLockBootStatusData
RtlLockHeap
RtlLockMemoryStreamRegion
RtlLogStackBackTrace
RtlLookupAtomInAtomTable
RtlLookupElementGenericTable
RtlLookupElementGenericTableAvl
RtlMakeSelfRelativeSD
RtlMapGenericMask
RtlMapSecurityErrorToNtStatus
RtlMoveMemory
RtlMultiAppendUnicodeStringBuffer
RtlMultiByteToUnicodeN
RtlMultiByteToUnicodeSize
RtlNewInstanceSecurityObject
RtlNewSecurityGrantedAccess
RtlNewSecurityObject
RtlNewSecurityObjectEx
RtlNewSecurityObjectWithMultipleInheritance
RtlNormalizeProcessParams
RtlNtPathNameToDosPathName
RtlNtStatusToDosError
RtlNtStatusToDosErrorNoTeb
RtlNumberGenericTableElements
RtlNumberGenericTableElementsAvl
RtlNumberOfClearBits
RtlNumberOfSetBits
RtlOemStringToUnicodeSize
RtlOemStringToUnicodeString
RtlOemToUnicodeN
RtlOpenCurrentUser
RtlPcToFileHeader
RtlPinAtomInAtomTable
RtlPopFrame
RtlPrefixString
RtlPrefixUnicodeString
RtlProtectHeap
RtlPushFrame
RtlQueryAtomInAtomTable
RtlQueryDepthSList
RtlQueryEnvironmentVariable_U
RtlQueryHeapInformation
RtlQueryInformationAcl
RtlQueryInformationActivationContext
RtlQueryInformationActiveActivationContext
RtlQueryInterfaceMemoryStream
RtlQueryProcessBackTraceInformation
RtlQueryProcessDebugInformation
RtlQueryProcessHeapInformation
RtlQueryProcessLockInformation
RtlQueryRegistryValues
RtlQuerySecurityObject
RtlQueryTagHeap
RtlQueryTimeZoneInformation
RtlQueueApcWow64Thread
RtlQueueWorkItem
RtlRaiseException
RtlRaiseStatus
RtlRandom
RtlRandomEx
RtlReAllocateHeap
RtlReadMemoryStream
RtlReadOutOfProcessMemoryStream
RtlRealPredecessor
RtlRealSuccessor
RtlRegisterSecureMemoryCacheCallback
RtlRegisterWait
RtlReleaseActivationContext
RtlReleaseMemoryStream
RtlReleasePebLock
RtlReleaseResource
RtlRemoteCall
RtlRemoveVectoredExceptionHandler
RtlResetRtlTranslations
RtlRestoreLastWin32Error
RtlRevertMemoryStream
RtlRunDecodeUnicodeString
RtlRunEncodeUnicodeString
RtlSecondsSince1970ToTime
RtlSecondsSince1980ToTime
RtlSeekMemoryStream
RtlSelfRelativeToAbsoluteSD
RtlSelfRelativeToAbsoluteSD2
RtlSetAllBits
RtlSetAttributesSecurityDescriptor
RtlSetBits
RtlSetControlSecurityDescriptor
RtlSetCriticalSectionSpinCount
RtlSetCurrentDirectory_U
RtlSetCurrentEnvironment
RtlSetDaclSecurityDescriptor
RtlSetEnvironmentVariable
RtlSetGroupSecurityDescriptor
RtlSetHeapInformation
RtlSetInformationAcl
RtlSetIoCompletionCallback
RtlSetLastWin32Error
RtlSetLastWin32ErrorAndNtStatusFromNtStatus
RtlSetMemoryStreamSize
RtlSetOwnerSecurityDescriptor
RtlSetProcessIsCritical
RtlSetSaclSecurityDescriptor
RtlSetSecurityDescriptorRMControl
RtlSetSecurityObject
RtlSetSecurityObjectEx
RtlSetThreadIsCritical
RtlSetThreadPoolStartFunc
RtlSetTimeZoneInformation
RtlSetTimer
RtlSetUserFlagsHeap
RtlSetUserValueHeap
RtlSizeHeap
RtlSplay
RtlStartRXact
RtlStatMemoryStream
RtlStringFromGUID
RtlSubAuthorityCountSid
RtlSubAuthoritySid
RtlSubtreePredecessor
RtlSubtreeSuccessor
RtlSystemTimeToLocalTime
RtlTimeFieldsToTime
RtlTimeToElapsedTimeFields
RtlTimeToSecondsSince1970
RtlTimeToSecondsSince1980
RtlTimeToTimeFields
RtlTraceDatabaseAdd
RtlTraceDatabaseCreate
RtlTraceDatabaseDestroy
RtlTraceDatabaseEnumerate
RtlTraceDatabaseFind
RtlTraceDatabaseLock
RtlTraceDatabaseUnlock
RtlTraceDatabaseValidate
RtlTryEnterCriticalSection
RtlUnhandledExceptionFilter
RtlUnhandledExceptionFilter2
RtlUnicodeStringToAnsiSize
RtlUnicodeStringToAnsiString
RtlUnicodeStringToCountedOemString
RtlUnicodeStringToInteger
RtlUnicodeStringToOemSize
RtlUnicodeStringToOemString
RtlUnicodeToCustomCPN
RtlUnicodeToMultiByteN
RtlUnicodeToMultiByteSize
RtlUnicodeToOemN
RtlUniform
RtlUnlockBootStatusData
RtlUnlockHeap
RtlUnlockMemoryStreamRegion
RtlUnwind
RtlUpcaseUnicodeChar
RtlUpcaseUnicodeString
RtlUpcaseUnicodeStringToAnsiString
RtlUpcaseUnicodeStringToCountedOemString
RtlUpcaseUnicodeStringToOemString
RtlUpcaseUnicodeToCustomCPN
RtlUpcaseUnicodeToMultiByteN
RtlUpcaseUnicodeToOemN
RtlUpdateTimer
RtlUpperChar
RtlUpperString
RtlValidAcl
RtlValidRelativeSecurityDescriptor
RtlValidSecurityDescriptor
RtlValidSid
RtlValidateHeap
RtlValidateProcessHeaps
RtlValidateUnicodeString
RtlVerifyVersionInfo
RtlWalkFrameChain
RtlWalkHeap
RtlWriteMemoryStream
RtlWriteRegistryValue
RtlZeroHeap
RtlZeroMemory
RtlZombifyActivationContext
RtlpApplyLengthFunction
RtlpEnsureBufferSize
RtlpNotOwnerCriticalSection
RtlpNtCreateKey
RtlpNtEnumerateSubKey
RtlpNtMakeTemporaryKey
RtlpNtOpenKey
RtlpNtQueryValueKey
RtlpNtSetValueKey
RtlpUnWaitCriticalSection
RtlpWaitForCriticalSection
RtlxAnsiStringToUnicodeSize
RtlxOemStringToUnicodeSize
RtlxUnicodeStringToAnsiSize
RtlxUnicodeStringToOemSize
VerSetConditionMask
ZwAcceptConnectPort
ZwAccessCheck
ZwAccessCheckAndAuditAlarm
ZwAccessCheckByType
ZwAccessCheckByTypeAndAuditAlarm
ZwAccessCheckByTypeResultList
ZwAccessCheckByTypeResultListAndAuditAlarm
ZwAccessCheckByTypeResultListAndAuditAlarmByHandle
ZwAddAtom
ZwAddBootEntry
ZwAdjustGroupsToken
ZwAdjustPrivilegesToken
ZwAlertResumeThread
ZwAlertThread
ZwAllocateLocallyUniqueId
ZwAllocateUserPhysicalPages
ZwAllocateUuids
ZwAllocateVirtualMemory
ZwAreMappedFilesTheSame
ZwAssignProcessToJobObject
ZwCallbackReturn
ZwCancelIoFile
ZwCancelTimer
ZwClearEvent
ZwClose
ZwCloseObjectAuditAlarm
ZwCompactKeys
ZwCompareTokens
ZwCompleteConnectPort
ZwCompressKey
ZwConnectPort
ZwContinue
ZwCreateDebugObject
ZwCreateDirectoryObject
ZwCreateEvent
ZwCreateEventPair
ZwCreateFile
ZwCreateIoCompletion
ZwCreateJobObject
ZwCreateJobSet
ZwCreateKey
ZwCreateKeyedEvent
ZwCreateMailslotFile
ZwCreateMutant
ZwCreateNamedPipeFile
ZwCreatePagingFile
ZwCreatePort
ZwCreateProcess
ZwCreateProcessEx
ZwCreateProfile
ZwCreateSection
ZwCreateSemaphore
ZwCreateSymbolicLinkObject
ZwCreateThread
ZwCreateTimer
ZwCreateToken
ZwCreateWaitablePort
ZwDebugActiveProcess
ZwDebugContinue
ZwDelayExecution
ZwDeleteAtom
ZwDeleteBootEntry
ZwDeleteFile
ZwDeleteKey
ZwDeleteObjectAuditAlarm
ZwDeleteValueKey
ZwDeviceIoControlFile
ZwDisplayString
ZwDuplicateObject
ZwDuplicateToken
ZwEnumerateBootEntries
ZwEnumerateKey
ZwEnumerateSystemEnvironmentValuesEx
ZwEnumerateValueKey
ZwExtendSection
ZwFilterToken
ZwFindAtom
ZwFlushBuffersFile
ZwFlushInstructionCache
ZwFlushKey
ZwFlushVirtualMemory
ZwFlushWriteBuffer
ZwFreeUserPhysicalPages
ZwFreeVirtualMemory
ZwFsControlFile
ZwGetContextThread
ZwGetDevicePowerState
ZwGetWriteWatch
ZwImpersonateAnonymousToken
ZwImpersonateClientOfPort
ZwImpersonateThread
ZwInitializeRegistry
ZwInitiatePowerAction
ZwIsProcessInJob
ZwIsSystemResumeAutomatic
ZwListenPort
ZwLoadDriver
ZwLoadKey
ZwLoadKey2
ZwLockFile
ZwLockProductActivationKeys
ZwLockRegistryKey
ZwLockVirtualMemory
ZwMakePermanentObject
ZwMakeTemporaryObject
ZwMapUserPhysicalPages
ZwMapUserPhysicalPagesScatter
ZwMapViewOfSection
ZwModifyBootEntry
ZwNotifyChangeDirectoryFile
ZwNotifyChangeKey
ZwNotifyChangeMultipleKeys
ZwOpenDirectoryObject
ZwOpenEvent
ZwOpenEventPair
ZwOpenFile
ZwOpenIoCompletion
ZwOpenJobObject
ZwOpenKey
ZwOpenKeyedEvent
ZwOpenMutant
ZwOpenObjectAuditAlarm
ZwOpenProcess
ZwOpenProcessToken
ZwOpenProcessTokenEx
ZwOpenSection
ZwOpenSemaphore
ZwOpenSymbolicLinkObject
ZwOpenThread
ZwOpenThreadToken
ZwOpenThreadTokenEx
ZwOpenTimer
ZwPlugPlayControl
ZwPowerInformation
ZwPrivilegeCheck
ZwPrivilegeObjectAuditAlarm
ZwPrivilegedServiceAuditAlarm
ZwProtectVirtualMemory
ZwPulseEvent
ZwQueryAttributesFile
ZwQueryBootEntryOrder
ZwQueryBootOptions
ZwQueryDebugFilterState
ZwQueryDefaultLocale
ZwQueryDefaultUILanguage
ZwQueryDirectoryFile
ZwQueryDirectoryObject
ZwQueryEaFile
ZwQueryEvent
ZwQueryFullAttributesFile
ZwQueryInformationAtom
ZwQueryInformationFile
ZwQueryInformationJobObject
ZwQueryInformationPort
ZwQueryInformationProcess
ZwQueryInformationThread
ZwQueryInformationToken
ZwQueryInstallUILanguage
ZwQueryIntervalProfile
ZwQueryIoCompletion
ZwQueryKey
ZwQueryMultipleValueKey
ZwQueryMutant
ZwQueryObject
ZwQueryOpenSubKeys
ZwQueryPerformanceCounter
ZwQueryPortInformationProcess
ZwQueryQuotaInformationFile
ZwQuerySection
ZwQuerySecurityObject
ZwQuerySemaphore
ZwQuerySymbolicLinkObject
ZwQuerySystemEnvironmentValue
ZwQuerySystemEnvironmentValueEx
ZwQuerySystemInformation
ZwQuerySystemTime
ZwQueryTimer
ZwQueryTimerResolution
ZwQueryValueKey
ZwQueryVirtualMemory
ZwQueryVolumeInformationFile
ZwQueueApcThread
ZwRaiseException
ZwRaiseHardError
ZwReadFile
ZwReadFileScatter
ZwReadRequestData
ZwReadVirtualMemory
ZwRegisterThreadTerminatePort
ZwReleaseKeyedEvent
ZwReleaseMutant
ZwReleaseSemaphore
ZwRemoveIoCompletion
ZwRemoveProcessDebug
ZwRenameKey
ZwReplaceKey
ZwReplyPort
ZwReplyWaitReceivePort
ZwReplyWaitReceivePortEx
ZwReplyWaitReplyPort
ZwRequestPort
ZwRequestWaitReplyPort
ZwResetEvent
ZwResetWriteWatch
ZwRestoreKey
ZwResumeProcess
ZwResumeThread
ZwSaveKey
ZwSaveKeyEx
ZwSaveMergedKeys
ZwSecureConnectPort
ZwSetBootEntryOrder
ZwSetBootOptions
ZwSetContextThread
ZwSetDebugFilterState
ZwSetDefaultHardErrorPort
ZwSetDefaultLocale
ZwSetDefaultUILanguage
ZwSetEaFile
ZwSetEvent
ZwSetEventBoostPriority
ZwSetHighEventPair
ZwSetHighWaitLowEventPair
ZwSetInformationDebugObject
ZwSetInformationFile
ZwSetInformationJobObject
ZwSetInformationKey
ZwSetInformationObject
ZwSetInformationProcess
ZwSetInformationThread
ZwSetInformationToken
ZwSetIntervalProfile
ZwSetIoCompletion
ZwSetLdtEntries
ZwSetLowEventPair
ZwSetLowWaitHighEventPair
ZwSetQuotaInformationFile
ZwSetSecurityObject
ZwSetSystemEnvironmentValue
ZwSetSystemEnvironmentValueEx
ZwSetSystemInformation
ZwSetSystemPowerState
ZwSetSystemTime
ZwSetThreadExecutionState
ZwSetTimer
ZwSetTimerResolution
ZwSetUuidSeed
ZwSetValueKey
ZwSetVolumeInformationFile
ZwShutdownSystem
ZwSignalAndWaitForSingleObject
ZwStartProfile
ZwStopProfile
ZwSuspendProcess
ZwSuspendThread
ZwSystemDebugControl
ZwTerminateJobObject
ZwTerminateProcess
ZwTerminateThread
ZwTestAlert
ZwTraceEvent
ZwTranslateFilePath
ZwUnloadDriver
ZwUnloadKey
ZwUnloadKeyEx
ZwUnlockFile
ZwUnlockVirtualMemory
ZwUnmapViewOfSection
ZwVdmControl
ZwWaitForDebugEvent
ZwWaitForKeyedEvent
ZwWaitForMultipleObjects
ZwWaitForSingleObject
ZwWaitHighEventPair
ZwWaitLowEventPair
ZwWriteFile
ZwWriteFileGather
ZwWriteRequestData
ZwWriteVirtualMemory
ZwYieldExecution
__isascii
__iscsym
__iscsymf
__toascii
_alldiv
_alldvrm
_allmul
_allrem
_allshl
_allshr
_atoi64
_aulldiv
_aulldvrm
_aullrem
_aullshr
_fltused
_i64toa
_i64tow
_itoa
_itow
_lfind
_ltoa
_ltow
_memccpy
_memicmp
_snprintf
_snwprintf
_splitpath
_strcmpi
_stricmp
_strlwr
_strnicmp
_strupr
_ui64toa
_ui64tow
_ultoa
_ultow
_vsnprintf
_vsnwprintf
_wcsicmp
_wcslwr
_wcsnicmp
_wcsupr
_wtoi
_wtoi64
_wtol
abs
atan
atoi
atol
bsearch
ceil
cos
fabs
floor
isalnum
isalpha
iscntrl
isdigit
isgraph
islower
isprint
ispunct
isspace
isupper
iswalpha
iswctype
iswdigit
iswlower
iswspace
iswxdigit
isxdigit
labs
log
mbstowcs
memchr
memcmp
memcpy
memmove
memset
pow
qsort
sin
sprintf
sqrt
sscanf
strcat
strchr
strcmp
strcpy
strcspn
strlen
strncat
strncmp
strncpy
strpbrk
strrchr
strspn
strstr
strtol
strtoul
swprintf
tan
tolower
toupper
towlower
towupper
vDbgPrintEx
vDbgPrintExWithPrefix
vsprintf
wcscat
wcschr
wcscmp
wcscpy
wcscspn
wcslen
wcsncat
wcsncmp
wcsncpy
wcspbrk
wcsrchr
wcsspn
wcsstr
wcstol
wcstombs
wcstoul
