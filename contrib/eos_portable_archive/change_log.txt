IMPORTANT: 

This is the last release from me as EOS employee. I plan to contribute all of this
work to the official boost libraries distribution and will continue to support users. 
<PERSON><PERSON> joined me recently and already added a valuable tutorial for you!

Changelog:

26.06.2012	5.0	Ported to boost versions up to 1.49.
			Added support for wstring, added tutorial by <PERSON><PERSON>.

01.04.2011	4.2	Ported to boost versions up to 1.46.1.
			Allow serialization of inf and nan values.

17.12.2009	4.1	Ported to boost versions up to 1.41.

		4.0	Changes in inheritance make arrays work.

13.02.2009	3.1	Shared pointer serialization capabilities added
			Ported to recent boost versions (up to 1.38)

25.09.2008	3.0	Refactored, fixed and ported to recent boost versions
			Archives are now named eos::portable_[io]archive

17.09.2008	2.1	Improved floating point handling and error detection.
			Extracted the exception class into an extra file.

28.04.2008	2.0	Major Bugfix resolving negative number collision!

28.11.2007	1.1	Small Bugfix in portable_binary_archive_exception class:
			throwing specifiers did not match base class declaration

12.11.2007	1.0	Initial Release to boost-users!

<PERSON>
<EMAIL>