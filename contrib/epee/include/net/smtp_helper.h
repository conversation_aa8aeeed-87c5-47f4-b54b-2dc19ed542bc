// Copyright (c) 2006-2013, <PERSON><PERSON>, www.sabelnikov.net
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
// notice, this list of conditions and the following disclaimer in the
// documentation and/or other materials provided with the distribution.
// * Neither the name of the Andrey N<PERSON> Sabelnikov nor the
// names of its contributors may be used to endorse or promote products
// derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER  BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 




#pragma once
#include "smtp.h"

namespace epee
{
namespace net_utils
{
	namespace smtp
	{

		inline bool send_mail(const std::string& server, int port, const std::string& login, const std::string& pass, const std::string& from_addres, const std::string& from_name, const std::string& maillist, const std::string& subject, const std::string& mail_body)
		{
			net_utils::smtp::CSMTPClient smtp;

			if ( !smtp.ServerConnect( server.c_str(), port ) )
			{
				LOG_PRINT("Reporting: Failed to connect to server " << server <<":"<< port, LOG_LEVEL_0);
				return false;
			}

			if(login.size() && pass.size())
			{
				if ( !smtp.ServerLogin( login.c_str(), pass.c_str()) )
				{
					LOG_PRINT("Reporting: Failed to auth on server " << server <<":"<< port, LOG_LEVEL_0);
					return false;

				}
			}

			if ( !smtp.SendMessage( from_addres.c_str(),
				from_name.c_str(),
				maillist.c_str(),
				subject.c_str(),
				"bicycle-client",
				(LPBYTE)mail_body.data(),
				mail_body.size()))
			{
				char *szErrorText = smtp.GetLastErrorText();
				if ( szErrorText )
				{
					LOG_PRINT("Failed to send message, error text: " << szErrorText, LOG_LEVEL_0);
				}
				else
				{
					LOG_PRINT("Failed to send message, error text: null", LOG_LEVEL_0);
				}
				return false;
			}

			smtp.ServerDisconnect();

			return true;


		}
	}
}
}