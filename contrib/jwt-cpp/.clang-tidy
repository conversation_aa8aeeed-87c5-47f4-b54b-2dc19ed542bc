Checks: '-*,
         bugprone-*,
         cert-*,
         clang-analyzer-*,
         clang-diagnostic-*,
         -clang-diagnostic-c++17-extensions,
         google-*,
         -google-runtime-references,
         -google-readability-braces-around-statements,
         hicpp-*,
         -hicpp-braces-around-statements,
         -hicpp-signed-bitwise,
         misc-*,
         -misc-non-private-member-variables-in-classes,
         llvm-*,
         modernize-*,
         -modernize-use-trailing-return-type,
         performance-*,
         portability-*,
         readability-*,
         -readability-magic-numbers,
         -readability-braces-around-statements,
         -readability-uppercase-literal-suffix'

CheckOptions:
  - key: readability-identifier-naming.TypedefCase
    value: lower_case
  - key: readability-identifier-naming.StructCase
    value: lower_case
  - key: readability-identifier-naming.ClassCase
    value: lower_case
  - key: readability-identifier-naming.VariableCase
    value: lower_case
  - key: readability-identifier-naming.ParameterCase
    value: lower_case
  - key: readability-identifier-naming.FunctionCase
    value: lower_case
  - key: readability-identifier-naming.NamespaceCase
    value: lower_case
  - key: readability-identifier-naming.GlobalConstantCase
    value: lower_case