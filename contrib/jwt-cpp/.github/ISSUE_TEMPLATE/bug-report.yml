name: Bug Report 🐛
description: File a bug report

labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
    validations:
      required: false
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen? Feel free to include some screenshots
      placeholder: Tell us what you see!
      value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: reproduce
    attributes:
      label: How To Reproduce?
      description: Please provide a small snippet to reproduce the issue
      placeholder: Some C++ code or Shell code to recreate th problem
      value: |
        ```c++
        #include "jwt-cpp/jwt.h"
        int main() {
           return 0;
        }
        ```
  - type: dropdown
    id: version
    attributes:
      label: Version
      description: What version of our software are you running?
      options:
        - 0.7.0
        - 0.6.0
        - 0.5.2
        - Older (please let us know if the "What happened" box)
    validations:
      required: true
  - type: dropdown
    id: operating-system
    attributes:
      label: What OS are you seeing the problem on?
      multiple: true
      options:
        - Windows
        - Linux
        - MacOS
        - Other (please let us know if the "What happened" box)
    validations:
      required: true
  - type: dropdown
    id: compiler
    attributes:
      label: What compiler are you seeing the problem on?
      multiple: true
      options:
        - GCC
        - Clang
        - MSVC
        - Other (please let us know if the "What happened" box)       
    validations:
      required: true 
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://example.com)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
