name: Publish a Badges
description: Publishes all badges
inputs:
  github_token:
    description: The token to use to publish the changes
    required: false
    default: ${{ github.token }}
runs:
  using: composite
  steps:
    - uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ inputs.github_token }}
        publish_branch: badges
        publish_dir: ./badges
        keep_files: true
        user_name: "github-actions[bot]"
        user_email: "github-actions[bot]@users.noreply.github.com"
