name: Install CMake
description: Download, Build and Cache CMake
inputs:
  version:
    description: The desired CMake version to install
    required: true
  url:
    description: "The corresponding URL to download the source code from"
    required: true
runs:
  using: composite
  steps:
  - name: Cache CMake
    id: cache-cmake
    uses: actions/cache@v3
    with:
      path: cmake-${{ inputs.version }}
      key: ${{ runner.name }}-${{ runner.os }}-${{ runner.arch }}-${{ job.container.id }}-cmake-${{ inputs.version }}
  - name: Build cmake
    if: steps.cache-cmake.outputs.cache-hit != 'true'
    run: |
      wget ${{ inputs.url }}
      tar -zxf cmake-${{ inputs.version }}.tar.gz
      cd cmake-${{ inputs.version }}
      ./bootstrap
      make -j $(nproc)
    shell: bash
  - name: Install cmake
    run: |
      cd cmake-${{ inputs.version }}
      # Depending if we run in on a GitHub Actions or from within a Docker image we have different permissions
      if [[ $EUID > 0 ]]; then
        # If we are not root then we need to sudo
        sudo make install
      else
        # Default docker image does not have users setup so we are only root and can not sudo
        make install
      fi
    shell: bash
