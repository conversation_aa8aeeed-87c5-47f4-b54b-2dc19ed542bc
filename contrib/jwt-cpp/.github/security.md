# Reporting Security Issues

If you believe you have found a security vulnerability in JWT-CPP, we encourage you to let us know right away.
We will investigate all legitimate reports and do our best to quickly fix the problem.

Please refer to the section below for our responsible disclosure policy:

## Disclosure Policy

Please contact one or more of the maintainers using the email advertised on our GitHub profiles:

- [@Thalhammer](https://github.com/Thalhammer)
- [@prince-chrismc](https://github.com/prince-chrismc)

Please provide as many details as possible about version, platform, and workflow as possible.
Having steps and reproducible code is better and is always greatly appreciated.

## Supported Version

Typically, fixes will be immediately released as a new patch release. However, older affected versions may receive
a new patch upon request.
