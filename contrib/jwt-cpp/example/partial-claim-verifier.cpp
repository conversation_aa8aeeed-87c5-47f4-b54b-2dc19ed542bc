/// @file partial-claim-verifier.cpp
#include "jwt-cpp/traits/nlohmann-json/defaults.h"

#include <iostream>

int main() {
	std::string rsa_priv_key = R"(***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)";

	auto role_claim = nlohmann::json{{"my-service", {{"roles", {"foo", "bar", "baz"}}}}};

	auto token = jwt::create()
					 .set_issuer("auth0")
					 .set_type("JWT")
					 .set_id("rsa-create-example")
					 .set_issued_now()
					 .set_expires_in(std::chrono::seconds{36000})
					 .set_payload_claim("resource-access", role_claim)
					 .sign(jwt::algorithm::rs256("", rsa_priv_key, "", ""));

	std::cout << "token: " << token << std::endl;

	std::string rsa_pub_key = R"(-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuGbXWiK3dQTyCbX5xdE4
yCuYp0AF2d15Qq1JSXT/lx8CEcXb9RbDddl8jGDv+spi5qPa8qEHiK7FwV2KpRE9
83wGPnYsAm9BxLFb4YrLYcDFOIGULuk2FtrPS512Qea1bXASuvYXEpQNpGbnTGVs
WXI9C+yjHztqyL2h8P6mlThPY9E9ue2fCqdgixfTFIF9Dm4SLHbphUS2iw7w1JgT
69s7of9+I9l5lsJ9cozf1rxrXX4V1u/SotUuNB3Fp8oB4C1fLBEhSlMcUJirz1E8
AziMCxS+VrRPDM+zfvpIJg3JljAh3PJHDiLu902v9w+Iplu1WyoB2aPfitxEhRN0
YwIDAQAB
-----END PUBLIC KEY-----)";

	auto decoded = jwt::decode(token);

	for (const auto& e : decoded.get_payload_json())
		std::cout << e.first << " = " << e.second << std::endl;

	std::cout << std::endl;

	auto role_verifier = [](const jwt::verify_context& ctx, std::error_code& ec) {
		using error = jwt::error::token_verification_error;

		auto c = ctx.get_claim(false, ec);
		if (ec) return;
		if (c.get_type() == jwt::json::type::object) {
			auto obj = c.to_json();
			try {
				auto roles = obj["my-service"]["roles"].get<nlohmann::json::array_t>();
				if (roles.end() == std::find(roles.begin(), roles.end(), "foo")) ec = error::claim_value_missmatch;
			} catch (const std::exception& ex) { ec = error::claim_value_missmatch; }
		} else
			ec = error::claim_type_missmatch;
	};

	/* [verifier check custom claim] */
	auto verifier = jwt::verify()
						.allow_algorithm(jwt::algorithm::rs256(rsa_pub_key, "", "", ""))
						.with_issuer("auth0")
						// Check for "foo" in /my-service/role
						.with_claim("resource-access", role_verifier);
	/* [verifier check custom claim] */

	try {
		verifier.verify(decoded);
		std::cout << "Success!" << std::endl;
	} catch (const std::exception& ex) { std::cout << "Error: " << ex.what() << std::endl; }

	return 0;
}
