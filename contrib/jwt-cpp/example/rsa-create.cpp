/// @file rsa-create.cpp
#include <iostream>
#include <jwt-cpp/jwt.h>

int main() {
	std::string rsa_priv_key = R"(***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)";

	auto token = jwt::create()
					 .set_issuer("auth0")
					 .set_type("JWT")
					 .set_id("rsa-create-example")
					 .set_issued_now()
					 .set_expires_in(std::chrono::seconds{36000})
					 .set_payload_claim("sample", jwt::claim(std::string{"test"}))
					 .sign(jwt::algorithm::rs256("", rsa_priv_key, "", ""));

	std::cout << "token:\n" << token << std::endl;
}
