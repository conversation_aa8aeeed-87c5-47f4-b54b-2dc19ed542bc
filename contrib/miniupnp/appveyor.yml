version: '2.1.{build}'

install:
  - set PATH=%PATH%;C:\msys64\mingw32\bin

build_script:
  - cd miniupnpc
  - appveyor AddCompilationMessage "Building miniupnpc"
  - mingw32-make -f Makefile.mingw
  - mingw32-make -f Makefile.mingw pythonmodule PYTHON=C:\Python27\python
#  - upnpc-static.exe -l

after_build:
  - 7z a ..\miniupnpc-%APPVEYOR_BUILD_VERSION%.zip *.exe *.dll *.a *.lib
  - 7z a ..\miniupnpc-python-%APPVEYOR_BUILD_VERSION%.zip build\ dist\

artifacts:
  - path: miniupnpc-$(appveyor_build_version).zip
    name: miniupnpc binaries
  - path: miniupnpc-python-$(appveyor_build_version).zip
    name: miniupnpc python module
