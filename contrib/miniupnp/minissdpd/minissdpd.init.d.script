#!/bin/sh
# $Id: minissdpd.init.d.script,v 1.2 2007/09/23 17:46:57 nanard Exp $
# MiniUPnP project
# author: <PERSON>
# website: http://miniupnp.free.fr/ or http://miniupnp.tuxfamily.org/

MINISSDPD=/usr/sbin/minissdpd
PIDFILE=/var/run/minissdpd.pid
# get default interface
IF=`route | grep default |awk -- '{ print $8 }'`
ARGS="-i $IF"

test -f $MINISSDPD || exit 0

. /lib/lsb/init-functions

case "$1" in
start)  log_daemon_msg "Starting minissdpd" "minissdpd"
        start-stop-daemon --start --quiet --pidfile $PIDFILE \
           --exec $MINISSDPD -- $ARGS $LSBNAMES
        log_end_msg $?
        ;;
stop)   log_daemon_msg "Stopping minissdpd" "minissdpd"
        start-stop-daemon --stop --quiet --pidfile $PIDFILE
        log_end_msg $?
        ;;
restart|reload|force-reload)
        log_daemon_msg "Restarting minissdpd" "minissdpd"
        start-stop-daemon --stop --retry 5 --quiet --pidfile $PIDFILE
        start-stop-daemon --start --quiet --pidfile $PIDFILE \
           --exec $MINISSDPD -- $ARGS $LSBNAMES
        log_end_msg $?
        ;;
*)      log_action_msg "Usage: /etc/init.d/minissdpd {start|stop|restart|reload|force-reload}"
        exit 2
        ;;
esac
exit 0

