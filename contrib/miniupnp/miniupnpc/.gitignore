deb_dist/
build/
*.o
*.a
*.so
*.dll
*.dll.def
*.exe
*.lib
*.dylib
Makefile.bak
miniupnpcstrings.h
pythonmodule
pythonmodule3
upnpc-shared
upnpc-static
minihttptestserver
minixmlvalid
testminiwget
validateminiwget
validateminixml
java/miniupnpc_*.jar
_jnaerator.*
out.errors.txt
jnaerator-*.jar
miniupnpc.h.bak
testupnpreplyparse
validateupnpreplyparse
testportlistingparse
validateportlistingparse
listdevices
testigddescparse
validateigddescparse
dist/
miniupnpc.egg-info/
init
miniupnpc.pc
