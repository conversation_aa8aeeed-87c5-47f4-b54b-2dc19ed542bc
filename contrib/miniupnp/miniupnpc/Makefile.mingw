# $Id: Makefile.mingw,v 1.21 2015/09/18 12:45:16 nanard Exp $
# Miniupnp project.
# http://miniupnp.free.fr/ or https://miniupnp.tuxfamily.org/
# (c) 2005-2018 <PERSON>
# This Makefile is made for MinGW
#
# To cross compile on a *nix machine :
# make -f Makefile.mingw DLLWRAP=mingw32-dllwrap CC=mingw32-gcc AR=mingw32-ar
#
CC ?= gcc
DLLWRAP = dllwrap
SH = /bin/sh
ifeq ($(OS),Windows_NT)
RM = del
else
RM = rm -f
endif
#CFLAGS = -Wall -g -DDEBUG -D_WIN32_WINNT=0X501
CFLAGS = -Wall -Os -DNDEBUG -D_WIN32_WINNT=0X501
LDLIBS = -lws2_32 -liphlpapi
# -lwsock32
# -liphlpapi is needed for GetBestRoute() and GetIpAddrTable()
PYTHON=\utils\python25\python
OBJS=miniwget.o minixml.o igd_desc_parse.o minisoap.o \
     minissdpc.o \
     miniupnpc.o upnpreplyparse.o upnpcommands.o upnperrors.o \
     connecthostport.o portlistingparse.o receivedata.o \
     upnpdev.o
OBJSDLL=$(addprefix dll/, $(OBJS))

all:	init upnpc-static upnpc-shared testminixml libminiupnpc.a \
	miniupnpc.dll listdevices

init:
	mkdir dll
	echo init > init

clean:
	$(RM) upnpc testminixml *.o
	$(RM) dll\*.o
	$(RM) *.exe
	$(RM) miniupnpc.dll
	$(RM) libminiupnpc.a

libminiupnpc.a:	$(OBJS)
	$(AR) cr $@ $?

pythonmodule:	libminiupnpc.a
	$(PYTHON) setupmingw32.py build --compiler=mingw32
	$(PYTHON) setupmingw32.py install --skip-build

miniupnpc.dll:	libminiupnpc.a $(OBJSDLL)
	$(DLLWRAP) -k --driver-name $(CC) \
	--def miniupnpc.def \
	--output-def miniupnpc.dll.def \
	--implib miniupnpc.lib -o $@ \
	$(OBJSDLL) $(LDLIBS)

miniupnpc.lib:	miniupnpc.dll
#	echo $@ generated with $<

dll/upnpc.o:	upnpc.o
#	echo $@ generated with $<

.c.o:
	$(CC) $(CFLAGS) -DMINIUPNP_STATICLIB -c -o $@ $<
	$(CC) $(CFLAGS) -DMINIUPNP_EXPORTS -c -o dll/$@ $<

upnpc.o:	upnpc.c
	$(CC) $(CFLAGS) -DMINIUPNP_STATICLIB -c -o $@ $<
	$(CC) $(CFLAGS) -c -o dll/$@ $<

# --enable-stdcall-fixup
upnpc-static:	upnpc.o libminiupnpc.a
	$(CC) -o $@ $^ $(LDLIBS)

upnpc-shared:	dll/upnpc.o miniupnpc.lib
	$(CC) -o $@ $^ $(LDLIBS)

listdevices: listdevices.o libminiupnpc.a
	$(CC) -o $@ $^ $(LDLIBS)

wingenminiupnpcstrings:	wingenminiupnpcstrings.o

wingenminiupnpcstrings.o:	wingenminiupnpcstrings.c

# To make miniupnpcstrings.h from miniupnpcstrings.h.in we either
# use a custom executable (if running make under windows) or use
# sed (if cross compiling from another platform).
ifeq ($(OS),Windows_NT)
miniupnpcstrings.h: miniupnpcstrings.h.in wingenminiupnpcstrings
	wingenminiupnpcstrings $< $@
else
miniupnpcstrings.h:	miniupnpcstrings.h.in VERSION
	sed 's|OS_STRING ".*"|OS_STRING "Windows/Mingw32"|' $< | \
	sed 's|MINIUPNPC_VERSION_STRING ".*"|MINIUPNPC_VERSION_STRING "$(shell cat VERSION)"|' > $@
endif

minixml.o:	minixml.c minixml.h

upnpc.o:	miniwget.h minisoap.h miniupnpc.h igd_desc_parse.h
upnpc.o:	upnpreplyparse.h upnpcommands.h upnperrors.h miniupnpcstrings.h

miniwget.o:	miniwget.c miniwget.h miniupnpcstrings.h connecthostport.h

minisoap.o:	minisoap.c minisoap.h miniupnpcstrings.h

miniupnpc.o:	miniupnpc.c miniupnpc.h minisoap.h miniwget.h minixml.h

igd_desc_parse.o:	igd_desc_parse.c igd_desc_parse.h

testminixml:	minixml.o igd_desc_parse.o testminixml.c

upnpreplyparse.o:	upnpreplyparse.c upnpreplyparse.h minixml.h

upnpcommands.o:	upnpcommands.c upnpcommands.h upnpreplyparse.h miniupnpc.h portlistingparse.h

minissdpc.o:	minissdpc.c minissdpc.h receivedata.h

upnpdev.o:	upnpdev.c upnpdev.h

