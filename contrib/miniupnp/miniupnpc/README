Project: miniupnp
Project web page: http://miniupnp.free.fr/ or https://miniupnp.tuxfamily.org/
github: https://github.com/miniupnp/miniupnp
Author: <PERSON> (c) 2005-2017 <PERSON>
This software is subject to the conditions detailed in the
LICENSE file provided within this distribution.


* miniUPnP Client - miniUPnPc *

To compile, simply run 'gmake' (could be 'make' on your system).
Under win32, to compile with MinGW, type "mingw32make.bat".
MS Visual C solution and project files are supplied in the msvc/ subdirectory.

The compilation is known to work under linux, FreeBSD,
OpenBSD, MacOS X, AmigaOS and cygwin.
The official AmigaOS4.1 SDK was used for AmigaOS4 and GeekGadgets for AmigaOS3.
upx (http://upx.sourceforge.net) is used to compress the win32 .exe files.

To install the library and headers on the system use :
> su
> make install
> exit

alternatively, to install into a specific location, use :
> INSTALLPREFIX=/usr/local make install

upnpc.c is a sample client using the libminiupnpc.
To use the libminiupnpc in your application, link it with
libminiupnpc.a (or .so) and use the following functions found in miniupnpc.h,
upnpcommands.h and miniwget.h :
- upnpDiscover()
- UPNP_GetValidIGD()
- miniwget()
- parserootdesc()
- GetUPNPUrls()
- UPNP_* (calling UPNP methods)

Note : use #include <miniupnpc/miniupnpc.h> etc... for the includes
and -lminiupnpc for the link

Discovery process is speeded up when MiniSSDPd is running on the machine.


* Python module *

you can build a python module with 'make pythonmodule'
and install it with 'make installpythonmodule'.
setup.py (and setupmingw32.py) are included in the distribution.


Feel free to contact me if you have any problem :
e-mail : <EMAIL>

If you are using libminiupnpc in your application, please
send me an email !

For any question, you can use the web forum :
https://miniupnp.tuxfamily.org/forum/

Bugs should be reported on github :
https://github.com/miniupnp/miniupnp/issues
