$Id: apiversions.txt,v 1.9 2016/01/24 17:24:36 nanard Exp $

Differences in API between miniUPnPc versions

API version 17
  change struct UPNPDev
  move getHTTPResponse() to miniwget_private.h
  updated macro :
    #define MINIUPNPC_API_VERSION 17

API version 16
  added "status_code" argument to getHTTPResponse(), miniwget() and miniwget_getaddr()
  updated macro :
    #define MINIUPNPC_API_VERSION 16

API version 15
  changed "sameport" argument of upnpDiscover() upnpDiscoverAll() upnpDiscoverDevice()
    to "localport". When 0 or 1, behaviour is not changed, but it can take
    any other value between 2 and 65535
    Existing programs should be compatible
  updated macro :
    #define MINIUPNPC_API_VERSION  15

API version 14
miniupnpc.h
  add ttl argument to upnpDiscover() upnpDiscoverAll() upnpDiscoverDevice()
  upnpDiscoverDevices()
  getDevicesFromMiniSSDPD() :
    connectToMiniSSDPD() / disconnectFromMiniSSDPD()
    requestDevicesFromMiniSSDPD() / receiveDevicesFromMiniSSDPD()
  updated macro :
    #define MINIUPNPC_API_VERSION  14

API version 13
miniupnpc.h:
  add searchalltype param to upnpDiscoverDevices() function
  updated macro :
    #define MINIUPNPC_API_VERSION  13

API version 12
miniupnpc.h :
  add upnpDiscoverAll() / upnpDiscoverDevice() / upnpDiscoverDevices()
    functions
  updated macros :
    #define MINIUPNPC_API_VERSION  12

API version 11

upnpreplyparse.h / portlistingparse.h :
  removed usage of sys/queue.h / bsdqueue.h

miniupnpc.h:
  updated macros :
    #define MINIUPNPC_API_VERSION  11

====================== miniUPnPc version 1.9 ======================
API version 10

upnpcommands.h:
  added argument remoteHost to UPNP_GetSpecificPortMappingEntry()

miniupnpc.h:
  updated macros :
    #define MINIUPNPC_VERSION      "1.9"
    #define MINIUPNPC_API_VERSION  10

====================== miniUPnPc version 1.8 ======================
API version 9

miniupnpc.h:
  updated macros :
    #define MINIUPNPC_VERSION      "1.8"
    #define MINIUPNPC_API_VERSION  9
  added "unsigned int scope_id;" to struct UPNPDev
  added scope_id argument to GetUPNPUrls()



====================== miniUPnPc version 1.7 ======================
API version 8

miniupnpc.h :
  add new macros :
    #define MINIUPNPC_VERSION      "1.7"
    #define MINIUPNPC_API_VERSION  8
  add rootdescURL to struct UPNPUrls



====================== miniUPnPc version 1.6 ======================
API version 8

Adding support for IPv6.
igd_desc_parse.h :
  struct IGDdatas_service :
    add char presentationurl[MINIUPNPC_URL_MAXSIZE];
  struct IGDdatas :
    add struct IGDdatas_service IPv6FC;
miniupnpc.h :
  new macros :
    #define UPNPDISCOVER_SUCCESS (0)
    #define UPNPDISCOVER_UNKNOWN_ERROR (-1)
    #define UPNPDISCOVER_SOCKET_ERROR (-101)
    #define UPNPDISCOVER_MEMORY_ERROR (-102)
  simpleUPnPcommand() prototype changed (but is normaly not used by API users)
  add arguments ipv6 and error to upnpDiscover() :
     struct UPNPDev *
     upnpDiscover(int delay, const char * multicastif,
                  const char * minissdpdsock, int sameport,
                  int ipv6,
                  int * error);
  add controlURL_6FC member to struct UPNPUrls :
    struct UPNPUrls {
       char * controlURL;
       char * ipcondescURL;
       char * controlURL_CIF;
       char * controlURL_6FC;
    };

upnpcommands.h :
  add leaseDuration argument to UPNP_AddPortMapping()
  add desc, enabled and leaseDuration arguments to UPNP_GetSpecificPortMappingEntry()
  add UPNP_GetListOfPortMappings() function (IGDv2)
  add IGDv2 IPv6 related functions :
    UPNP_GetFirewallStatus()
    UPNP_GetOutboundPinholeTimeout()
    UPNP_AddPinhole()
    UPNP_UpdatePinhole()
    UPNP_DeletePinhole()
    UPNP_CheckPinholeWorking()
    UPNP_GetPinholePackets()



====================== miniUPnPc version 1.5 ======================
API version 5

new function :
int UPNPIGD_IsConnected(struct UPNPUrls *, struct IGDdatas *);
new macro in upnpcommands.h :
#define UPNPCOMMAND_HTTP_ERROR

====================== miniUPnPc version 1.4 ======================
Same API as version 1.3

====================== miniUPnPc version 1.3 ======================
API version 4

Use UNSIGNED_INTEGER type for
UPNP_GetTotalBytesSent(), UPNP_GetTotalBytesReceived(),
UPNP_GetTotalPacketsSent(), UPNP_GetTotalPacketsReceived()
Add remoteHost argument to UPNP_AddPortMapping() and UPNP_DeletePortMapping()

====================== miniUPnPc version 1.2 ======================
API version 3

added sameport argument to upnpDiscover()
struct UPNPDev *
upnpDiscover(int delay, const char * multicastif,
             const char * minissdpdsock, int sameport);

====================== miniUPnPc Version 1.1 ======================
Same API as 1.0


====================== miniUPnPc Version 1.0 ======================
API version 2


struct UPNPDev {
      struct UPNPDev * pNext;
      char * descURL;
      char * st;
      char buffer[2];
};
struct UPNPDev * upnpDiscover(int delay, const char * multicastif,
                              const char * minissdpdsock);

