﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Fichiers sources">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Fichiers d%27en-tête">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Fichiers de ressources">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\connecthostport.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\igd_desc_parse.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\minisoap.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\minissdpc.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\miniupnpc.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\miniwget.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\minixml.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\portlistingparse.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\receivedata.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\upnpcommands.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\upnpdev.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\upnperrors.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="..\upnpreplyparse.c">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\connecthostport.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\declspec.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\igd_desc_parse.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\minisoap.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\minissdpc.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\miniupnpc.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\miniupnpcstrings.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\miniupnpctypes.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\miniwget.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\minixml.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\portlistingparse.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\receivedata.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\upnpcommands.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\upnpdev.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\upnperrors.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="..\upnpreplyparse.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
  </ItemGroup>
</Project>