MiniUPnP project.
(c) 2006-2017 <PERSON>page : http://miniupnp.free.fr/
Mirror: https://miniupnp.tuxfamily.org/
github: https://github.com/miniupnp/miniupnp

miniupnpd is still under developpement. This documentation is
likely to be a little outdated when you read it. So please go on the
web forum https://miniupnp.tuxfamily.org/ if you need more information.

================================ *BSD/pf =================================
To Build and Install :

- use BSD make to compile.
- you can first 'make config.h' then edit config.h to your preferences and
  finally 'make'
  Alternatively to editing config.h, options can be passed to genconfig.sh
  For more details :
  > ./genconfig.sh -h
- add "rdr-anchor miniupnpd" or/and "anchor miniupnpd" lines to /etc/pf.conf
  (Since OpenBSD 4.7, rdr-anchor lines are no longer used and should be
  removed, leaving only the anchor lines).
- some FreeBSD users reported that it is also necessary for them
  to explicitly allow udp traffic on *********/8 by adding the two following
  lines to /etc/pf.conf :
   pass out on $int_if from any to *********/8 keep state
   pass in on $int_if from any to *********/8 keep state
- don't forget to " pfctl -f /etc/pf.conf "
- you can check your modifications are taken into account with
  "pfctl -s nat" and "pfctl -s rule". Look for the "rdr-anchor miniupnpd"
  (if applicable) and/or "anchor miniupnpd" lines.
- OpenBSD users may need to add a multicast_host= line to /etc/rc.conf.local
  see $man 8 netstart
- install as root using :
  # make install
  or
  # PREFIX=/usr/local make install
- run as root : The daemon needs rights to modify pf rules.

=========================== *BSD,*Solaris/ipf =============================

genconfig.sh and the Makefile try to detect wether ipf or pf should be
used. If it fails, edit config.h and Makefile by hand.
In Makefile, the FWNAME variable value should be pf or ipf.
Installation steps are allmost the same as with pf.

*Solaris users would be interested in reading informations from :
http://blogs.sun.com/avalon/category/IPFilter

============================= Mac OS X/ipfw ===============================

- To enable non standard compilation options,
  > ./genconfig.sh -h
  Or edit config.h after it has been generated by genconfig.sh
- use 'bsdmake' (if available) or 'make -f Makefile.macosx' to build

============================== Mac OS X/pf ================================

Starting with Mac OS X 10.7 Lion, pf replaced ipfw as the OS X firewall.
also bsdmake is not available anymore.
Make sure you have installed the Xcode commande line tools (from the
Xcode Preferences menu or using 'xcode-select --install' command)

You'll need to download xnu sources : https://github.com/opensource-apple/xnu
> INCLUDES="-I.../xnu/bsd -I.../xnu/libkern" make -f Makefile.macosx

============================ Linux/netfilter ==============================
To Build and install :

- make sure you have libiptc available on your system :
  if you are using debian, "apt-get install iptables-dev"
  Some versions of the iptables-dev package don't include the
  necessary files : read "how to get libiptc with its headers on debian" below.
  In anycase, libiptc is available in iptables sources packages
  from http://netfilter.org
- edit and run netfilter/iptables_init.sh shell script.
  This script must allways be run before the daemon
  to set up initial rules and chains.
- Build and edit the config.h file
  > make -f Makefile.linux config.h
  > vi config.h
- Build the daemon
  > make -f Makefile.linux
  If not using iptables from your system,
  > IPTABLESPATH=/path/to/iptables-1.4.1 make -f Makefile.linux
- install as root using :
  > make -f Makefile.linux install
- A miniupnpd script should be installed to /etc/init.d
  and the configuration files to /etc/miniupnpd
- anytime, you can use the netfilter/iptables_flush.sh
  script to flush all rules added by the daemon.
- after killing the daemon, you can get back to
  iptables initial state by running the netfilter/iptables_removeall.sh
  script. Don't forget to edit the script to your convenience.

NOTE: a /etc/init.d/miniupnpd script will be installed.
  If it suits you, you can use is with start, stop or restart argument.
  # /etc/init.d/miniupnpd restart


How to get libiptc with its headers on debian :
(Note: that should be useless now that netfilter/tiny_nf_nat.h is included)
- Use apt-get to get sources :
  > apt-get source iptables
  you should then have an iptables-x.x.x/ directory.
- configure and compile :
  > cd iptables-x.x.x/
  > ./configure --enable-static
  > make
- it is now possible to compile miniupnpd using the following command :
  > IPTABLESPATH=/path/to/iptables-x.x.x make -f Makefile.linux

======================== Linux/netfilter nftables =========================

work is in progress. To build :
 > make -f Makefile.linux_nft

see :
https://miniupnp.tuxfamily.org/forum/viewtopic.php?p=4370
https://github.com/miniupnp/miniupnp/pull/114

=========================== Configuration =============================
Edit the /etc/miniupnpd.conf file to set options. Almost all options are
also available through command line switches.

A basic configuration would set :
ext_ifname :   WAN network interface (interface connected to the internet)
listening_ip : LAN network interface (network where to supply NAT traversal)
enable_natpmp=yes
enable_upnp=yes
and the permission rules (see below).

Historically, LAN had to be specified by IP/mask, such as
listening_ip=***********/24
but if you compiled with IPv6 support, you need to specify an interface name :
listening_ip=eth0
The current code assumes there is only one IPv4 address assigned to LAN
interfaces. That is not the case with some CARP setup, there is then a risk
the wrong mask would be picked. You can force the mask when using interface
names :
listening_ip=eth0/24

Miniupnpd supports some kind of security check for allowing or disallowing
redirection to be made. The UPnP permission rules are read from the
miniupnpd.conf configuration file.
When a new redirection is requested, permission rules are evaluated in
top-down order and the first permission rule matched gives the response :
redirection allowed or denied. If no rule is matching, the redirection is
allowed, so it is a good practice to have a "catch all" deny permission
rule at the end of your permission ruleset.
Sample permission ruleset :
allow 4662-4672 ************/32 4662-4672
deny 0-65535 ************/32 0-65535
allow 1024-65535 ***********/24 1024-65535
deny 0-65535 0.0.0.0/0 0-65535
With this ruleset, redirections are allowed only for host on the subnet
***********/************* for the ports 1024 or above. There is an exception
for the host ************ for which only redirections from/to port 4662 to
4672 are allowed.

You can generate the uuid for your UPnP device with the uuidgen available
under linux. The following following OpenBSD package is also providing
a "uuid" tool :
http://www.openbsd.org/4.0_packages/i386/uuid-1.5.0.tgz-long.html
An web based uuid generator is also available :
http://kruithof.xs4all.nl/uuid/uuidgen

On linux systems, one could also use the command
'cat /proc/sys/kernel/random/uuid' to generate an uuid.

More simple, use the genuuid makefile target :
> make genuuid
or
> make -f Makefile.linux genuuid
This target is needed by the "install" target, so it is done automatically
during install.

To stop the daemon use :
  # kill `cat /var/run/miniupnpd.pid`
or if your linux system use /etc/init.d/
  # /etc/init.d/miniupnpd stop


* Signals :
miniupnpd handles the following signals :
SIGUSR1: Send public IP address change notification
SIGUSR2: Handle special actions in Tomato Firmware version
         Or rewrite the lease_file
SIGINT:  Close gracefully
SIGTERM: Close gracefully
SIGPIPE: Ignore

There is code to detect change in network interfaces bsd/ifacewatcher.c and
linux/ifacewatcher.c, but if that code doesn't work for you, you may want to
send SIGUSR1 to miniupnpd if you public IP address changed.
