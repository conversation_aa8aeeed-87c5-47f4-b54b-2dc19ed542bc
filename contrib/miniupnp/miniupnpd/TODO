- detect and inform of IP address changes or connectivity status change
  => Done (ifacewatcher 2011/05)
  To be improved.
- improve logging.
- improve NAT-PMP compliance
  => to be checked

- <PERSON><PERSON>'s version used to dynamically include the local LAN IP and WAN IP as the windows icon name - if it was displayed...

- I just enabled my lease file to save the port mappings so I don't have to recreate them every time miniupnp gets restarted but instead what happens is the lease file gets recreated and the leases erased. 
  => Done (2009/02/14)

- http://miniupnp.tuxfamily.org/forum/viewtopic.php?p=1091

support IGD v2 : http://upnp.org/specs/gw/igd2/
 - Lease Duration support (mandatory in v2) => DONE
 - WANIPv6FirewallControl
   - pf : updatepinhole to do
   - netfilter : ok ?
   - ipfw/ipf : TODO

implement port_in_use() for NetBSD

- Do we need to TRIM arguments from SOAP ?
