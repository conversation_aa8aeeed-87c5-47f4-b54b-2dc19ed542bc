# WAN network interface
#ext_ifname=eth1
#ext_ifname=xl1
# If the WAN interface has several IP addresses, you
# can specify the one to use below
#ext_ip=

# LAN network interfaces IPs / networks
# There can be multiple listening IPs for SSDP traffic, in that case
# use multiple 'listening_ip=...' lines, one for each network interface.
# It can be IP address or network interface name (ie. "eth0")
# It is mandatory to use the network interface name in order to enable IPv6
# HTTP is available on all interfaces.
# When MULTIPLE_EXTERNAL_IP is enabled, the external IP
# address associated with the subnet follows. For example:
#  listening_ip=***********/24 ***********
#listening_ip=***********/24
#listening_ip=********/16
#listening_ip=eth0
# CAUTION: mixing up WAN and LAN interfaces may introduce security risks!
# Be sure to assign the correct interfaces to LAN and WAN and consider
# implementing UPnP permission rules at the bottom of this configuration file

# Port for HTTP (descriptions and SOAP) traffic. Set to 0 for autoselect.
#http_port=0
# Port for HTTPS. Set to 0 for autoselect (default)
#https_port=0

# Path to the UNIX socket used to communicate with MiniSSDPd
# If running, MiniSSDPd will manage M-SEARCH answering.
# default is /var/run/minissdpd.sock
#minissdpdsocket=/var/run/minissdpd.sock

# Enable NAT-PMP support (default is no)
#enable_natpmp=yes

# Enable UPNP support (default is yes)
#enable_upnp=no

# PCP
# Configure the minimum and maximum lifetime of a port mapping in seconds
# 120s and 86400s (24h) are suggested values from PCP-base
#min_lifetime=120
#max_lifetime=86400

# Chain names for netfilter (not used for pf or ipf).
# default is MINIUPNPD for both
#upnp_forward_chain=forwardUPnP
#upnp_nat_chain=UPnP
#upnp_nat_postrouting_chain=UPnP-Postrouting

# Lease file location
#lease_file=/var/log/upnp.leases

# To enable the next few runtime options, see compile time
# ENABLE_MANUFACTURER_INFO_CONFIGURATION (config.h)

# Name of this service, default is "`uname -s` router"
#friendly_name=MiniUPnPd router

# Manufacturer name, default is "`uname -s`"
#manufacturer_name=Manufacturer corp

# Manufacturer URL, default is URL of OS vendor
#manufacturer_url=http://miniupnp.free.fr/

# Model name, default is "`uname -s` router"
#model_name=Router Model

# Model description, default is "`uname -s` router"
#model_description=Very Secure Router - Model

# Model URL, default is URL of OS vendor
#model_url=http://miniupnp.free.fr/

# Bitrates reported by daemon in bits per second
# by default miniupnpd tries to get WAN interface speed
#bitrate_up=1000000
#bitrate_down=********

# Secure Mode, UPnP clients can only add mappings to their own IP
#secure_mode=yes
secure_mode=no

# Default presentation URL is HTTP address on port 80
# If set to an empty string, no presentationURL element will appear
# in the XML description of the device, which prevents MS Windows
# from displaying an icon in the "Network Connections" panel.
#presentation_url=http://www.mylan/index.php

# Report system uptime instead of daemon uptime
system_uptime=yes

# Notify interval in seconds. default is 30 seconds.
#notify_interval=240
notify_interval=60

# Unused rules cleaning.
# never remove any rule before this threshold for the number
# of redirections is exceeded. default to 20
#clean_ruleset_threshold=10
# Clean process work interval in seconds. default to 0 (disabled).
# a 600 seconds (10 minutes) interval makes sense
clean_ruleset_interval=600

# Log packets in pf (default is no)
#packet_log=no

# Anchor name in pf (default is miniupnpd)
#anchor=miniupnpd

# ALTQ queue in pf
# Filter rules must be used for this to be used.
# compile with PF_ENABLE_FILTER_RULES (see config.h file)
#queue=queue_name1

# Tag name in pf
#tag=tag_name1

# Make filter rules in pf quick or not. default is yes
# active when compiled with PF_ENABLE_FILTER_RULES (see config.h file)
#quickrules=no

# UUID, generate your own UUID with "make genuuid"
uuid=00000000-0000-0000-0000-000000000000

# Daemon's serial and model number when reporting to clients
# (in XML description)
#serial=12345678
#model_number=1

# If compiled with IGD_V2 defined, force reporting IGDv1 in rootDesc (default
# is no)
#force_igd_desc_v1=no

# UPnP permission rules
# (allow|deny) (external port range) IP/mask (internal port range)
# A port range is <min port>-<max port> or <port> if there is only
# one port in the range.
# IP/mask format must be nnn.nnn.nnn.nnn/nn
# It is advised to only allow redirection of port >= 1024
# and end the rule set with "deny 0-65535 0.0.0.0/0 0-65535"
# The following default ruleset allows specific LAN side IP addresses
# to request only ephemeral ports. It is recommended that users
# modify the IP ranges to match their own internal networks, and
# also consider implementing network-specific restrictions
# CAUTION: failure to enforce any rules may permit insecure requests to be made!
allow 1024-65535 ***********/24 1024-65535
allow 1024-65535 ***********/24 1024-65535
allow 1024-65535 ***********/23 22
allow 12345 *************/32 54321
deny 0-65535 0.0.0.0/0 0-65535
