# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Build folder html - TESTNET

on:
  push:
    branches:
      - 'main'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18.15.0'

      - name: Install Dependencies
        working-directory: html_source
        run: npm i

      - name: Build to folder html
        working-directory: html_source
        run: npm run build --html

      - name: Commit and Push Changes
        run: |
          git config user.name '${{ secrets.USER_NAME }}'
          git config user.email '${{ secrets.USER_EMAIL }}'
          git add .
          git commit -m 'Auto-build folder html'
          git push origin ${{ github.ref }}
