{"ACCESSIBILITY": {"ADD_WALLET": {"DESCRIPTIONS": {"DESCRIPTION1": "A page where you can go to create a new wallet, open an existing wallet, or restore a wallet."}}, "ASSETS": {"LABELS": {"LABEL1": "Assets table", "LABEL2": "Pagination", "LABEL3": "Previous", "LABEL4": "Next", "LABEL5": "Menu for asset: {{ full_name }}", "LABEL6": "Asset Row: {{ full_name }}"}}, "ASSIGN_ALIAS": {"DESCRIPTIONS": {"DESCRIPTION1": "Adding an alias for your wallet. You can create your own unique name for your wallet, and a comment. Other users can send funds to this name."}}, "AUTH": {"LOGIN": {"ARIAL_LABEL1": "Login card with form", "ARIAL_LABEL2": "Setup Master Password Form", "ARIAL_LABEL3": "Enter a master password here", "ARIAL_LABEL4": "Confirm new password here", "ARIAL_LABEL5": "Submit master password", "ARIAL_LABEL6": "Skip password creation", "ARIAL_LABEL7": "Login with Master Password", "ARIAL_LABEL8": "Login with password", "ARIAL_LABEL9": "Reset application"}}, "BACK_BUTTON": {"ARIA_LABEL1": "Back"}, "CREATE_SWAP": {"LABELS": {"LABEL1": "Select asset", "LABEL2": "Reverse"}}, "CREATE_WALLET": {"DESCRIPTIONS": {"DESCRIPTION1": "On this page you can create a wallet"}}, "EDIT_ALIAS": {"DESCRIPTIONS": {"DESCRIPTION1": "Page for editing alias. You can only change the comment."}}, "HISTORY": {"LABELS": {"LABEL1": "History table"}}, "MY_ALIASES_DIALOG": {"LABELS": {"LABEL1": "Edit", "LABEL2": "Send"}}, "PAGINATION": {"LABELS": {"LABEL1": "Pagination", "LABEL2": "Previous", "LABEL3": "Next"}}, "RESTORE_WALLET": {"DESCRIPRIONS": {"DESCRIPTION1": "Wallet recovery page. Fill in all fields and enter the seed phrase."}}, "SEND": {"LABELS": {"LABEL1": "Remove destinastions row", "LABEL2": "Toggle input mode"}}, "SETTINGS": {"ARIA_LABEL1": "Copy Build Version", "ARIA_LABEL2": "Copy secret", "ARIA_LABEL3": "Regenerate"}, "STAKING": {"LABELS": {"LABEL1": "Selected period:"}}, "TRANSFER_ALIAS": {"DESCRIPTIONS": {"DESCRIPTION1": "Transfer page alias send to another wallet"}}, "WALLET_CARD": {"ARIA_LABEL1": "Wallet synchronization progress"}}, "ADD_ANOTHER_DESTINATION": {"TEXT1": "Add another destination", "TEXT2": "You can add another address for sending"}, "ASSETS": {"DROP_DOWN_MENU": {"ASSET_DETAILS": "Asset Details", "REMOVE_ASSET": "Remove asset", "SEND": "Send", "SWAP": "<PERSON><PERSON><PERSON>"}, "FORM": {"ERRORS": {"INSUFICCIENT_FUNDS": "Insuficcient funds", "WRONG_ASSET_ID": "Wrong asset id"}}, "MODALS": {"ASSET_DETAILS": {"LABELS": {"CURRENT_SUPPLY": "Current supply", "ID": "Asset ID", "MAX_SUPPLE": "Max supply", "NAME": "Asset name", "OWNER": "Owner", "TICKER": "Ticker"}, "TITLE": "Asset Details"}, "CONFIRM_MODAL": {"TITLE": "Do you want delete {{ full_name }}"}}, "TABLE": {"LABELS": {"BALANCE": "Balance", "NAME": "Name", "PRICE": "Price", "VALUE": "Value"}}}, "ASSIGN_ALIAS": {"BUTTON_ASSIGN": "Assign", "COMMENT": {"LABEL": "Comment", "PLACEHOLDER": "", "TOOLTIP": "The comment will be visible to anyone who wants to make a payment to your alias. You can provide details about your business, contacts, or include any text. Comments can be edited later."}, "COST": "Alias fee {{value}} {{currency}}", "FORM_ERRORS": {"MAX_LENGTH": "Maximum comment length reached", "NAME_EXISTS": "Alias name already exists", "NAME_LENGTH": "The alias must be 6-25 characters long", "NAME_REQUIRED": "Name is required", "NAME_WRONG": "Invalid name. Use only letters (a–z), numbers (0–9), and special characters (-.)", "NO_MONEY": "Insufficient funds to assign the alias"}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": " <PERSON>ter alias", "TOOLTIP": "An alias is a shortened form or your account. An alias can only include Latin letters, numbers and characters “.” and “-”. It must start with “@”."}, "ONE_ALIAS": "You can create only one alias per wallet", "REQUEST_ADD_REG": "The alias will be assigned within 10 minutes"}, "BACKEND_LOCALIZATION": {"INCOME_TRANSFER_CONFIRMED": "Payment received", "INCOME_TRANSFER_UNCONFIRMED": "Incoming payment (not confirmed)", "IS_CONFIRMED": "", "IS_MINIMIZE": "Zano application is minimized to the system tray", "IS_RECEIVED": "", "LOCKED": "Blocked", "MINED": "Mined", "QUIT": "Quit", "RESTORE": "You can recover it by clicking or using the context menu", "TRAY_MENU_MINIMIZE": "Minimize", "TRAY_MENU_SHOW": "Resize"}, "BREADCRUMBS": {"ADD_WALLET": "Add wallet", "ASSIGN_ALIAS": "Assign alias", "CONTRACTS": "Contracts", "CREATE_WALLET": "Create new wallet", "EDIT_ALIAS": "Edit alias", "NEW_PURCHASE": "New purchase", "OLD_PURCHASE": "Purchase", "OPEN_WALLET": "Open existing wallet", "RESTORE_WALLET": "<PERSON><PERSON> from backup", "SAVE_PHRASE": "Save your seed phrase", "TRANSFER_ALIAS": "Transfer alias", "WALLET_DETAILS": "Wallet details"}, "BURN_CUSTOM_ASSET": {"BUTTONS": {"BUTTON1": "Cancel", "BUTTON2": "Confirm"}, "LABELS": {"LABEL1": "Burn", "LABEL2": "Amount"}}, "CELL_ASSET_BALANCE": {"LABELS": {"LABEL1": "Available balance", "LABEL2": "Unconfirmed balance"}, "TOOLTIPS": {"TOOLTIP1": "Architecture demands 10 confirmations for this change transaction and that amount gets locked in the wallet. Therefore, lock time is 10 blocks behind or approximately 10 minutes."}}, "COMMON": {"BACK": "Go back", "COPY": "Copy", "LOADING": "Loading..."}, "CONFIRM": {"BUTTON_CANCEL": "Cancel", "BUTTON_CONFIRM": "Send", "MESSAGE": {"COMMENT": "Comment", "FROM": "From", "SEND": "Send", "TO": "To"}, "TITLE": "Transaction"}, "CONFIRM_CREATE_CUSTOM_ASSET": {"BUTTONS": {"BUTTON1": "Cancel", "BUTTON2": "Confirm"}, "TABLE": {"LABEL1": "Ticker", "LABEL2": "Full Name", "LABEL3": "Total Max Supply", "LABEL4": "Current Supply", "LABEL5": "Decimal Point", "LABEL6": "Meta Info"}, "TITLE": "Confirm"}, "CONFIRM_SWAP": {"BREADCRUMBS": {"ITEM1": "<PERSON><PERSON><PERSON>", "ITEM2": "Confirm swap"}, "FORM": {"BUTTONS": {"BUTTON1": "Accept Proposal", "BUTTON2": "Back"}, "LABELS": {"LABEL1": "Swap proposal hex", "LABEL2": "Proposal details"}, "PLACEHOLDERS": {"PLACEHOLDER1": "Enter <PERSON> proposal hex"}, "TABLE": {"LABELS": {"LABEL1": "Sending", "LABEL2": "Receiving", "LABEL3": "Expiration time"}}}}, "CONTACTS": {"ADD": "Add/edit contact", "ADD_CONTACT": "Add contact", "BUTTON": {"ADD": "Add contact", "ADD_EDIT": "Add/Save", "DELETE": "Delete", "EDIT": "Edit", "GO_TO_WALLET": "Go to wallet", "IMPORT_EXPORT": "Import/export", "SEND": "Send"}, "COPY": "- Copy", "ERROR_EMPTY_LIST": "Contact list is empty", "ERROR_EXPORT": "Invalid file type. Save file as .csv", "ERROR_IMPORT": "Error is occured while reading file!", "ERROR_IMPORT_EMPTY": "File is empty", "ERROR_TYPE_FILE": "Please import valid .csv file", "EXPORT": "Export", "FORM": {"ADDRESS": "Address", "NAME": "Name", "NOTES": "Notes"}, "FORM_ERRORS": {"ADDRESS_DUBLICATED": "Address is dublicated", "ADDRESS_NOT_VALID": "Address not valid", "ADDRESS_REQUIRED": "Address is required", "MAX_LENGTH": "Maximum notes length reached", "NAME_DUBLICATED": "Name is dublicated", "NAME_LENGTH": "The name must be 4-25 characters long", "NAME_REQUIRED": "Name is required", "SET_MASTER_PASSWORD": "Set master password"}, "IMPORT": "Import", "IMPORT_EXPORT": "Import or export contacts", "OPEN_ADD_WALLET": "Open/Add wallet", "SEND": "Send", "SEND_FROM": "Send from", "SEND_TO": "To", "SUCCESS_EXPORT": "Contacts are exported", "SUCCESS_IMPORT": "Contacts are imported", "SUCCESS_SAVE": "Contact is edited", "SUCCESS_SENT": "Contact added", "TABLE": {"ADDRESS": "Address", "ALIAS": "<PERSON><PERSON>", "EMPTY": "Contact list is empty", "NAME": "Name", "NOTES": "Notes"}, "TITLE": "Contact list"}, "CONTEXT_MENU": {"COPY": "copy", "PASTE": "paste", "SELECT": "select all"}, "CONTRACTS": {"AMOUNT": "Amount", "COMMENTS": "Comments", "CONTRACTS": "Contracts", "DATE": "Date", "EMPTY": "No active contracts", "LISTING_BUTTON": "Create listing", "PURCHASE": "Purchase", "PURCHASE_BUTTON": "New Purchase", "SELL": "<PERSON>ll", "STATUS": "Status", "STATUS_MESSAGES": {"BUYER": {"ACCEPTED": "<PERSON><PERSON> accepted your contract proposal", "BEING_CANCELLED": "Cancellation in progress", "CANCELLED": "Contract canceled", "COMPLETED": "Contract completed", "EXPIRED": "The contract proposal has expired", "IGNORED": "<PERSON><PERSON> ignored your contract proposal", "IGNORED_CANCEL": "The seller ignored your proposal to cancel the contract", "NOT_RECEIVED": "Delivery failed", "NULLIFIED": "All deposits burned", "WAIT": "Waiting for deposits confirmation", "WAITING": "Waiting for response", "WAITING_CANCEL": "Waiting for contract cancellation", "WAITING_SELLER": "Waiting for delivery"}, "SELLER": {"ACCEPTED": "Contract started", "BEING_CANCELLED": "Cancellation in progress", "CANCELLED": "Contract canceled", "COMPLETED": "Contract completed", "EXPIRED": "Contract proposal has expired", "IGNORED": "You ignored contract proposal", "IGNORED_CANCEL": "You ignored cancellation proposal", "NEW_CONTRACT": "New contract proposal", "NOT_RECEIVED": "Delivery failed", "NULLIFIED": "All deposits burned", "PROPOSAL_CANCEL": "New proposal to cancel contract and return deposits", "WAIT": "Waiting for contract confirmation", "WAITING_BUYER": "Waiting for delivery"}}, "TIME_LEFT": {"REMAINING_LESS_ONE": "Less than an hour to respond", "REMAINING_MANY": "{{time}} hours remain", "REMAINING_MANY_ALT": "{{time}} hours remain", "REMAINING_MANY_ALT_RESPONSE": "{{time}} hours remain", "REMAINING_MANY_ALT_WAITING": "Waiting for {{time}} hours", "REMAINING_MANY_RESPONSE": "{{time}} hours remain", "REMAINING_MANY_WAITING": "Waiting for {{time}} hours", "REMAINING_ONE": "{{time}} hour remains", "REMAINING_ONE_RESPONSE": "{{time}} hour remains", "REMAINING_ONE_WAITING": "Waiting for {{time}} hour"}}, "CREATE_NEW_ASSETS": {"BREADCRUMBS": {"BREADCRUMB1": "Custom Assets", "BREADCRUMB2": "Create new asset"}, "FORM": {"BUTTONS": {"BUTTON1": "Create"}, "LABELS": {"LABEL1": "Ticker", "LABEL2": "Full name", "LABEL3": "Total Max Supply", "LABEL4": "Current Supply", "LABEL5": "Decimal Point", "LABEL6": "Meta info"}, "PLACEHOLDERS": {"PLACEHOLDER1": "Confidential token", "PLACEHOLDER2": "Comment"}}}, "CREATE_SWAP": {"BREADCRUMBS": {"ITEM1": "<PERSON><PERSON><PERSON>", "ITEM2": "Create swap"}, "FORM": {"BUTTONS": {"BUTTON1": "Create proposal", "BUTTON2": "Cancel"}, "ERRORS": {"ERROR1": "Swap for the same asset is not allowed."}, "LABELS": {"LABEL1": "Sending", "LABEL2": "Receiving", "LABEL3": "Receiver address"}, "PLACEHOLDERS": {"PLACEHOLDER1": "Please enter the amount"}}}, "CREATE_WALLET": {"BUTTON_CREATE": "Create wallet", "BUTTON_SELECT": "Select wallet location", "CONFIRM": "Confirm wallet password", "ERROR_CANNOT_SAVE_SYSTEM": "Wallet files cannot be saved to the OS partition", "ERROR_CANNOT_SAVE_TOP": "Existing wallet files cannot be replaced or overwritten", "FORM_ERRORS": {"CONFIRM_NOT_MATCH": "Passwords don`t match", "MAX_LENGTH": "Maximum name length reached", "NAME_DUPLICATE": "Name is duplicate", "NAME_REQUIRED": "This field is required"}, "NAME": "Wallet name", "PASS": "Set wallet password", "TITLE_SAVE": "Save the wallet file."}, "CUSTOM_ASSETS": {"BUTTONS": {"BUTTON1": "Create new assets"}, "DROP_MENU": {"LABELS": {"LABEL1": "Asset Details", "LABEL2": "Emit", "LABEL3": "Update", "LABEL4": "Burn"}}, "TABLE": {"LABELS": {"LABEL1": "Asset ID", "LABEL2": "Ticker", "LABEL3": "Name", "LABEL4": "Total max supply", "LABEL5": "Current Supply", "LABEL6": "Decimal point"}}, "TEXT": {"EMPTY": "No custom assets"}}, "DEEPLINK": {"BUTTONS": {"BUTTON1": "Next...", "BUTTON2": "Sign & Send...", "BUTTON3": "Close", "BUTTON4": "Ok"}, "LABELS": {"LABEL1": "Select wallet for action:", "LABEL10": "Operation successful", "LABEL11": "Operation hash", "LABEL12": "Your wallets have not loaded yet. Try this action a little later.", "LABEL2": "Offer title", "LABEL3": "Description", "LABEL4": "Category", "LABEL5": "Price", "LABEL6": "Preview url", "LABEL7": "Contacts", "LABEL8": "Comments", "LABEL9": "Transaction fee"}}, "EDIT_ALIAS": {"BUTTON_EDIT": "Edit", "COMMENT": {"LABEL": "Comment", "PLACEHOLDER": ""}, "COST": "Fee {{value}} {{currency}}", "FORM_ERRORS": {"MAX_LENGTH": "Maximum comment length reached", "NO_MONEY": "You do not have enough funds to change the comment to this alias"}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "@ Enter alias"}}, "EMIT_CUSTOM_ASSET": {"BUTTONS": {"BUTTON1": "Cancel", "BUTTON2": "Confirm"}, "LABELS": {"LABEL1": "Emit", "LABEL2": "Amount"}}, "ERRORS": {"ACCESS_DENIED": "Access denied", "ADDRESS_NOT_VALID": "Address not valid", "AMOUNT_GREATER_CURRENT_SUPPLY": "Amount greater current supply", "ASSET_HAS_NOT_BEEN_ADDED_TO_WALLET": "Asset has not been added", "ASSET_NOT_FOUND": "Asset not found", "BAD_ARG": "Invalid argument", "CANNOT_BE_GREATER_THAN_TOTAL_MAX_SUPPLY": "Cannot be greater than Total max supply", "CANT_ESTIMATE_WRAP_OPERATION_COST": "Can't estimate wrap operation cost, please try again later", "CORE_BUSY": "Internal error: core is busy", "DAEMON_BUSY": "Internal error: daemon is busy", "FILE_EXIST": "A file with that name already exists. Enter another name to save the file under", "FILE_NOT_FOUND": "File not found", "FILE_NOT_SAVED": "You cannot save a wallet file in this folder. Please choose another folder.", "FILE_RESTORED": "The wallet file was corrupted. We have recovered the keys and the wallet from the blockchain", "GREATER_THAN_TOTAL_MAX_SUPPLY": "Greater than total max supply", "HEX_NOT_VALID": "Hex not valid", "INSUFFICIENT_FUNDS": "Insufficient funds", "INVALID_FULL_NAME_ASSET_PATTERN": "Invalid asset name. Use only letters (A-Z, a–z), numbers (0–9), and special characters (\".,:!-()\" and space)", "INVALID_PROPOSAL": "Invalid proposal", "INVALID_TICKER_PATTERN": "Invalid ticker. Use only letters (A-Z, a–z), numbers (0–9)", "MAX": "Max {{ max }}", "MAX_DECIMAL_POINT": "Max decimal point {{ max }}", "MAX_LENGTH": "Maximin length {{ requiredLength }}", "MIN": "Min {{ min }}", "MIN_LENGTH": "Min length {{ requiredLength }}", "NOT_ENOUGH_MONEY": "Insufficient funds in account", "NOT_ENOUGH_OUTPUTS_TO_MIX": "Mix-in number is too big for current blockchain state. There are not enough unspent outputs to mix with", "NOT_FILE_ZANO_WALLET": "This file is not recognized as a Zano wallet..", "NO_MONEY": "Not enough funds for this transaction", "NO_MONEY_REMOVE_OFFER": "There is no fee for deleting an offer, but in order to protect the network against flood transactions you need to have at least {{fee}} {{currency}} in your wallet", "OPS_UNKNOWN": "Ops... Unknown error...", "REGEXP_INVALID_PASSWORD": "Invalid password. Use only letters (a–z), numbers (0–9), and special characters !@#$%^&*()_+-={}[]|:;\"'<>,.?/~. Maximum length: 40 characters.", "REQUIRED": "Required field", "TO_BIG_TOTAL_SUPPLY": "To big total supply, reduce it or make decimal point smaller", "TRANSACTION_ERROR": "Error. Transaction not completed.", "TRANSACTION_IS_TO_BIG": "Transaction exceeds network limit, send required amount with multiple transactions", "TRANSFER_ATTEMPT": "There is no connection to Zano network", "TX_IS_TOO_BIG": "Transaction exceeds network limit, send required amount with multiple transactions", "TX_TYPE_COIN_BASE": "Error. The payment was not completed.", "TX_TYPE_NEW_ALIAS": "Error. Failed to register alias to safe", "TX_TYPE_NEW_ALIAS_END": "Please try again.", "TX_TYPE_NORMAL": "Error. The payment from the wallet", "TX_TYPE_NORMAL_END": "was not completed.", "TX_TYPE_NORMAL_TO": "to", "TX_TYPE_UPDATE_ALIAS": "Error. Failed to change comment to alias in safe", "WALLET_RPC_ERROR_CODE_NOT_ENOUGH_MONEY": "Insufficient funds for this transaction", "WALLET_WATCH_ONLY_NOT_SUPPORTED": "Watch-only wallets can only be opened by simplewallet", "WALLET_WRONG_ID": "Invalid wallet ID", "WRAP_SERVICE_IS_INACTIVE": "Wrap service is inactive", "WRONG_PASSWORD": "Invalid password", "WRONG_PASSWORD_MUST_BE": "Invalid password. Password must be"}, "EXPORT_HISTORY": {"CANCEL": "Cancel", "EXPORT": "Export to file...", "EXPORT_BUTTON": "Export history...", "FILTER": "Filter POS Transactions", "FORMAT": "Format", "SAVED_FILE": "Select path to save transactions history ", "TITLE": "Export Wallet History", "TOOLTIP": "Export wallet transactions history to file"}, "HISTORY": {"ADDRESS": "Address", "AMOUNT": "Amount", "DATE": "Date", "DETAILS": {"COMMENT": "Comment", "CONFIRMATION": "Confirmation", "HEIGHT": "Height", "ID": "Transaction ID", "INPUTS": "Inputs", "OUTPUTS": "Outputs", "PAYMENT_ID": "Payment ID", "SIZE": "Transaction size", "SIZE_VALUE": "{{value}} bytes"}, "FEE": "Fee", "HIDDEN": "Hidden", "LOCK_TOOLTIP": "Locked till {{date}}", "NO_FEE": "No Fee", "RECEIVED": "Received", "SEND": "<PERSON><PERSON>", "STATUS": "Status", "STATUS_TOOLTIP": "Confirmations {{current}}/{{total}}", "TYPE_MESSAGES": {"CANCEL_CONTRACT": "Cancel and return deposits", "COMPLETE_BUYER": "Contract completed", "COMPLETE_SELLER": "Contract completed", "CREATE_ALIAS": "Fee for assigning alias", "CREATE_CONTRACT": "Contract proposal", "HIDDEN": "hidden", "NULLIFY_CONTRACT": "Burn deposits", "PLEDGE_CONTRACT": "Contract deposit", "POS_REWARD": "POS reward", "POW_REWARD": "POW reward", "PROPOSAL_CANCEL_CONTRACT": "Cancellation request", "SERVICE_TRANSACTIONS": "Service transactions", "UNDEFINED": "Undefined", "UPDATE_ALIAS": "Fee for editing alias"}}, "LOGIN": {"BUTTON_NEXT": "Next", "BUTTON_RESET": "Reset", "BUTTON_SKIP": "<PERSON><PERSON>", "DIALOGS": {"CONFIRMATION": {"RESET": {"MESSAGE": "This will permanently delete all your secret data and reset the app to its default state. This action cannot be undone. Are you sure you want to proceed?", "TITLE": "Reset App Data?"}}}, "FORM_ERRORS": {"CONFIRM_REQUIRED": "Confirmation is required", "INVALID_PASS": "Invalid Password", "MISMATCH": "Mismatch", "PASS_REQUIRED": "Password is required", "WRONG_PASSWORD": "Wrong password"}, "INCORRECT_PASSWORD": "Invalid password", "MASTER_PASS": "Master password", "SETUP_CONFIRM_PASS": "Confirm the password", "SETUP_MASTER_PASS": "Setup master password"}, "MAIN": {"BUTTON_NEW_WALLET": "Create new wallet", "BUTTON_OPEN_WALLET": "Open existing wallet", "BUTTON_RESTORE_BACKUP": "<PERSON><PERSON> from backup", "CHOOSE_PATH": "Please choose a path", "HELP": "How to create wallet?", "TITLE": "Create or open the wallet to start using <PERSON>ano"}, "MESSAGES": {"ADDRESS": "Address", "MESSAGE": "Message", "SEND_BUTTON": "Send", "SEND_PLACEHOLDER": "Type a message..."}, "MIGRATE_WALLET_TO_ZARCANUM": {"BUTTON1": "Migrate", "LINK1": "What does it mean?", "TEXT1": "There are {{ total_bare_outs }} outputs with total amount of {{ total_amount }} ZANO.", "TEXT2": "They can be converted in {{ txs_count }} transactions with total fee = {{ expected_total_fee }}.", "TITLE": "Migrate wallet to Zarcanum"}, "MODALS": {"ADD_TOKEN": "Add token", "CANCEL": "Cancel", "ERROR": "Error", "INFO": "Information", "OK": "OK", "SUCCESS": "Success"}, "MY_ALIASES_DIALOG": {"BUTTON1": "Close", "TITLE": "My Aliases"}, "OPEN_WALLET": {"BUTTON": "Open wallet", "FILE_NOT_FOUND1": "Wallet file not found", "FILE_NOT_FOUND2": "<br/><br/> It might have been renamed or moved. <br/> To open it, use the \"Open wallet\" button.", "FORM_ERRORS": {"MAX_LENGTH": "Maximum name length reached", "NAME_DUPLICATE": "Name is duplicate", "NAME_REQUIRED": "Name is required"}, "MODAL": {"LABEL": "Password to this wallet", "NOT_FOUND": "Not found", "OPEN": "Open wallet", "SKIP": "<PERSON><PERSON>", "TITLE": "Type wallet password"}, "NAME": "Wallet name", "PASS": "Wallet password", "WITH_ADDRESS_ALREADY_OPEN": "A wallet with this address is already open"}, "PLACEHOLDERS": {"ADDRESS_PLACEHOLDER": "Enter adress here", "AMOUNT_PLACEHOLDER": "Please enter the amount", "COMMENT_PLACEHOLDER": "Enter your comment here", "CONFIRM_WALET_PASSWORD_PLACEHOLDER": "Confirm wallet password here", "DEPOSIT_PLACEHOLDER": "Enter your deposit here", "DESCRIPTION_PLACEHOLDER": "Enter a description here", "FEE_PLACEHOLDER": "Enter a fee here", "MASTER_PASS_PLACEHOLDER": "Enter a master password here", "NAME_PLACEHOLDER": "Enter a name here", "NOTES_PLACEHOLDER": "Enter a notes here", "PASSWORD_PLACEHOLDER": "Enter a password here", "PASS_PLACEHOLDER": "Enter a master password here", "PLACEHOLDER_CONFIRM": "Confirm new password here", "PLACEHOLDER_NEW": "Enter new password here", "PLACEHOLDER_OLD": "Enter current password here", "PURCHASE_PAYMENT_PLACEHOLDER": "Enter a payment ID here", "SEED_PASSWORD_PLACEHOLDER": "Enter a seed password here", "SEED_PHRASE_PLACEHOLDER": "Enter a seed phrase here", "SELLER_DEPOSIT_PLACEHOLDER": "Enter seller deposit here", "SELLER_PLACEHOLDER": "Enter a seller here", "WALET_PASSWORD_PLACEHOLDER": "Enter a wallet password here", "WALLET_NAME_PLACEHOLDER": "Please enter a name for your wallet"}, "PROGRESS": {"ADD_WALLET": "Add wallet", "CREATE_WALLET": "Create new wallet", "RESTORE_WALLET": "<PERSON><PERSON> from backup", "SELECT_LOCATION": "Select wallet location"}, "PURCHASE": {"ACCEPT_STATE_WAIT_BIG": "Contract started", "AMOUNT": "Amount", "BURN_PROPOSAL": "Deposits burned", "BUTTON_CANCEL_BUYER": "Cancel and return deposits", "BUTTON_CANCEL_SELLER": "Confirm and return deposits", "BUTTON_IGNORE": "Ignore and hide offer", "BUTTON_MAKE_PLEDGE": "Accept and make deposit", "BUTTON_NOT_CANCEL": "Ignore request", "BUTTON_NULLIFY": "Terminate and burn deposits", "BUTTON_NULLIFY_SHORT": "Burn", "BUTTON_RECEIVED": "Complete and release deposits", "BUYER_DEPOSIT": "Buyer deposit", "CANCEL": "Cancel", "COMMENT": "Comment", "DEALS_CANCELED_WAIT": "Cancellation in progress", "DESCRIPTION": "Description", "DETAILS": "Additional details", "FEE": "Fee", "FORM_ERRORS": {"ALIAS_NOT_VALID": "<PERSON><PERSON><PERSON> alias", "AMOUNT_REQUIRED": "Amount required", "AMOUNT_ZERO": "Amount cannot be zero", "COMMENT_MAXIMUM": "Maximum field length reached", "DESC_MAXIMUM": "Maximum field length reached", "DESC_REQUIRED": "Description required", "SELLER_DEPOSIT_REQUIRED": "Seller deposit required", "SELLER_NOT_VALID": "Invalid address", "SELLER_REQUIRED": "Address required", "SELLER_SAME": "Use separate account", "YOUR_DEPOSIT_REQUIRED": "Deposit required"}, "HOUR": "hour", "HOURS": "hours", "IGNORED_ACCEPT": "Contract proposal ignored", "IGNORED_CANCEL": "Contract cancellation proposal ignored", "NEED_MONEY": "Insufficient funds for this transaction", "NULLIFY_QUESTION": "Are you sure you want to burn both deposits?", "PAYMENT": "Payment ID", "PROGRESS_COMPLETE": "Completed", "PROGRESS_NEW": "New purchase", "PROGRESS_RECEIVE": "Reply received", "PROGRESS_WAIT": "Awaiting reply", "SAME_AMOUNT": "Same amount", "SELLER": "<PERSON><PERSON>", "SELLER_DEPOSIT": "Seller deposit", "SEND_BUTTON": "Send", "SEND_CANCEL_PROPOSAL": "Cancellation request sent", "STATUS_MESSAGES": {"BEING_CANCELLED": "Cancellation in progress", "CANCELLED": "Contract canceled", "COMPLETED": "Contract completed", "EXPIRED": "Contract proposal expired", "IGNORED_BUYER": "Contract proposal ignored", "IGNORED_CANCEL_BUYER": "Contract cancellation proposal ignored", "IGNORED_CANCEL_SELLER": "The seller ignored your proposal to cancel the contract", "IGNORED_SELLER": "The seller ignored your contract proposal", "NEW_PURCHASE": "New purchase", "NOT_RECEIVED": "Delivery failed", "NULLIFIED": "All deposits burned", "PROPOSAL_CANCEL_BUYER": "Cancellation request received", "PROPOSAL_CANCEL_SELLER": "Cancellation request sent", "WAITING_BUYER": "Contract proposal received", "WAITING_CONFIRMATION": "Waiting for deposits confirmation", "WAITING_DELIVERY": "Waiting for delivery", "WAITING_SELLER": "Waiting for response"}, "SUCCESS_FINISH_PROPOSAL": "Contract completed", "WAITING_TIME": "Response time", "WAITING_TIME_QUESTION": "Are you sure you want to cancel the contract?", "YOUR_DEPOSIT": "Your deposit"}, "RESTORE_WALLET": {"BUTTON_CREATE": "Create wallet", "BUTTON_SELECT": "Select wallet location", "CHOOSE_PATH": "Please choose a path", "CONFIRM": "Confirm wallet password", "FORM_ERRORS": {"CONFIRM_NOT_MATCH": "Passwords don`t match", "INCORRECT_PASSWORD": "Incorrect password", "KEY_NOT_VALID": "Key not valid", "KEY_REQUIRED": "Key is required", "MAX_LENGTH": "Maximum name length reached", "NAME_DUPLICATE": "Name is duplicate", "NAME_REQUIRED": "Name is required", "PASSWORD_SEED_PHRASE_INCORRECT": "The password for this seed phrase is incorrect", "SEED_PHRASE_IS_NO_VALID": "The seed phrase is not valid."}, "LABEL_NAME": "Wallet name", "LABEL_PHRASE_KEY": "Seed phrase / tracking seed", "NOT_CORRECT_FILE_OR_PASSWORD": "Invalid wallet file or password does not match", "OK": "OK", "PASS": "Set Wallet password", "SEED_PASSWORD": "Seed password"}, "SEED_PHRASE": {"BUTTON_COPIED": "<PERSON>pied", "BUTTON_COPY": "Copy", "BUTTON_CREATE_ACCOUNT": "Create wallet", "TITLE": "Make sure to keep your seed phrase in a safe place. If you forget your seed phrase you will not be able to recover your wallet."}, "SEND": {"ADDRESS": "Address", "AMOUNT": "Amount", "ASSET": "<PERSON><PERSON>", "BUTTON": "Send", "COMMENT": "Comment", "DETAILS": "Advanced option", "ERROR_CODES": {"NOT_ENOUGH_MONEY": "Not enough funds for this transaction"}, "FEE": "Fee", "FORM_ERRORS": {"ADDRESS_NOT_VALID": "Address not valid", "ADDRESS_REQUIRED": "Address is required", "ALIAS_NOT_FOUND": "<PERSON><PERSON> not found", "ALIAS_NOT_VALID": "The alias is not valid", "AMOUNT_REQUIRED": "Amount is required", "AMOUNT_ZERO": "Amount is zero", "FEE_MINIMUM": "The minimum fee required is {{fee}}", "FEE_REQUIRED": "Please enter the fee amount.", "GREAT_THAN_UNWRAPPED_COINS": "Amount is bigger than ERC20 tokens left available", "LESS_THAN_ZANO_NEEDED": "Too small amount to cover ERC20 fee", "MAX_LENGTH": "Maximum comment length reached", "MUST_BE_GREATER_THAN_ZERO": "Must be greater than zero", "WRAP_INFO_NULL": "Failed to request wrap state"}, "HIDE": "Retain receiver address", "INCLUDE_SENDER_ADDRESS": "Show my address to the receiver", "MIXIN": "Mixin", "SUCCESS_SENT": "Transaction sent", "WRAP": {"ESTIMATE": "Transaction Estimates", "FEE": "Ethereum Txn Fee:", "MAIN_TEXT": "This transaction will create wZano (\"Wrapped Zano\") which will be sent to the specified address on the Ethereum network.", "TITLE": "Wrap", "WILL_RECEIVE": "You'll receive:", "ZANO": "ZANO", "wZANO": "wZANO"}}, "SEND_DETAILS_MODAL": {"TITLE1": "Transaction", "TITLE2": "Transaction details"}, "SETTINGS": {"APP_LOCK": {"TIME1": "5 min", "TIME2": "15 min", "TIME3": "1 hour", "TIME4": "Never", "TITLE": "Lock app after:"}, "APP_LOG_TITLE": "Log level:", "CURRENCY_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "DARK_THEME": "Dark theme", "FORM": {"ZANO_COMPANION": {"LABELS": {"LABEL1": "Zano Companion", "LABEL2": "Secret", "LABEL3": "Port", "LABEL4": "(Requires master password)"}}}, "FORM_ERRORS": {"CONFIRM_NOT_MATCH": "The passwords do not match", "CURRENT_PASS_NOT_MATCH": "Old password not match", "PASS_REQUIRED": "Password is required"}, "GRAY_THEME": "Grey theme", "LANGUAGE": {"DE": "De<PERSON>ch", "EN": "English", "FR": "French", "ID": "Indonesian", "IT": "Italian", "PT": "Portuguese", "TITLE": "Language"}, "LAST_BUILD": "Current build: {{value}}", "MASTER_PASSWORD": {"BUTTON": "Save", "CONFIRM": "New password confirmation", "NEW": "New password", "OLD": "Old password", "TITLE1": "Set up Master Password", "TITLE2": "Update Master Password"}, "NOTIFICATIONS": "Notifications", "SCALE": {"100": "100% scale", "125": "125% scale", "150": "150% scale", "75": "75% scale", "TITLE": "Interface scale"}, "SECRET_WAS_COPIED": "Secret was copied", "SETTINGS_SAVED": "Saved! Settings Updated", "SHOW_BALANCE": "Show balance", "TITLE": "Settings", "USE_TOR_TO_RELAY_TRANSACTIONS": "Use TOR to relay transactions"}, "SIDEBAR": {"ACCOUNT": {"MESSAGES": "New offers/Messages", "STAKING": "Staking", "SYNCING": "Syncing wallet"}, "ADD_NEW": "Add", "CONTACTS": "Contacts", "CONTACTS_TOOLTIP": "Contacts option available only with Master Password enabled", "LOG_OUT": "Log out", "LOG_OUT_TOOLTIP": "Logout option available only with Master Password enabled", "SETTINGS": "Settings", "SYNCHRONIZATION": {"COMPLETE": "Completion", "DOWNLOADING": "Downloading", "ERROR": "System error", "LOADING": "Loading blockchain data", "LOGGING_OUT": "Logging out", "MB": "MB", "OFFLINE": "Offline", "ONLINE": "Online", "SLASH": "/", "SYNCING": "Syncing blockchain"}, "TITLE": "Wallets", "UPDATE": {"CRITICAL": "Update available", "CRITICAL_HINT": "Critical update!", "CRITICAL_TOOLTIP": "<span class=\"critical-update\">Critical update available.</span><i class=\"icon\"></i><span>Update strongly recommended!</span>", "IMPORTANT": "Update available", "IMPORTANT_HINT": "Important update!", "IMPORTANT_TOOLTIP": "<span class=\"important-update\">Get new update.</span><br><span>Important update!</span>", "STANDARD": "Update available", "STANDARD_TOOLTIP": "<span class=\"standard-update\">Get new update.</span><br><span>Update is recommended!</span>", "TIME": "System time differs from network", "TIME_TOOLTIP": "<span class=\"wrong-time\">Wrong system time!</span><br><span>Check and repair your system time.</span>"}}, "STAKING": {"GROUP": {"DAY": "day", "MONTH": "month", "WEEK": "week"}, "PERIOD": {"ALL": "All", "MONTH1": "1 month", "MONTH3": "3 month", "MONTH6": "6 month", "WEEK1": "1 week", "WEEK2": "2 week", "YEAR": "1 year"}, "SWITCH": {"OFF": "OFF", "ON": "ON"}, "TITLE": "Staking", "TITLE_GROUP": "Group:", "TITLE_PENDING": "Pending", "TITLE_PERIOD": "Time period:", "TITLE_TOTAL": "Total", "WALLET_STAKING_ON": "{{ value }} staking is ON"}, "SUCCESS_SWEEP_BARE_OUTS": {"DETAILS": "{{ txs_sent }} transactions were successfully sent, {{ bare_outs_swept }}  bare outputs migrated, {{ amount_swept }} coins transferred, and {{ fee_spent }} was spent for fees."}, "SWAP": {"BUTTONS": {"BUTTON1": "Create Swap", "BUTTON2": "Confirm Swap"}}, "SWAP_PROPOSAL_HEX": {"BREADCRUMBS": {"ITEM1": "<PERSON><PERSON><PERSON>", "ITEM2": "Swap proposal hex"}, "FORM": {"BUTTONS": {"BUTTON1": "Copy", "BUTTON2": "Close"}, "LABELS": {"LABEL1": "Swap proposal hex"}, "PLACEHOLDERS": {"PLACEHOLDER1": "Enter <PERSON> proposal hex"}}, "MODALS": {"CONFIRM_MODAL": {"BUTTONS": {"CLOSE": "Back to proposal", "SUBMIT": "Close"}, "MESSAGE": "Please make sure you saved the swap proposal text, you won’t be able to see it again", "TITLE": "Are you sure you want to leave this page?"}}}, "SYNC_MODAL": {"BUTTONS": {"BUTTON1": "OK"}, "LABELS": {"LABEL1": "This action is not available during synchronization..."}}, "TOR_LIB_STATE": {"STATE_CREATING_STREAM": "Creating stream", "STATE_DOWNLOADING_CONSENSUS": "Downloading consensus", "STATE_FAILED": "Failed created stream", "STATE_INITIALIZING": "Initializing", "STATE_MAKING_TUNNEL_A": "Building tunnel to A", "STATE_MAKING_TUNNEL_B": "Building tunnel to B", "STATE_SENDING": "Sending transaction", "STATE_SEND_FAILED": "Sending failed", "STATE_SENT_SUCCESS": "Successfully sent!", "STATE_SUCCESS": "Successfully created stream"}, "TRANSFER_ALIAS": {"ADDRESS": {"LABEL": "Transfer to", "PLACEHOLDER": ""}, "BUTTON_CANCEL": "Cancel", "BUTTON_TRANSFER": "Transfer", "COMMENT": {"LABEL": "Comment", "PLACEHOLDER": ""}, "COST": "Transfer fee {{value}} {{currency}}", "FORM_ERRORS": {"ALIAS_EXISTS": "This account already has an alias", "NO_MONEY": "You do not have enough funds to transfer this alias", "WRONG_ADDRESS": "No wallet exists for the provided address"}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "@ Enter alias"}, "REQUEST_SEND_REG": "The alias will be transferred within 10 minutes"}, "UPDATE_CUSTOM_ASSET": {"BUTTONS": {"BUTTON1": "Cancel", "BUTTON2": "Confirm"}, "LABELS": {"LABEL1": "Update asset", "LABEL2": "Owner"}}, "WALLET": {"AVAILABLE_BALANCE": "Available", "CONFIRM": {"MESSAGE": "To access it you’ll have to add it again", "TITLE": "Remove wallet from the list?"}, "DETAILS": "Details", "LOCK": "Lock", "LOCKED_BALANCE": "Locked", "LOCKED_BALANCE_LINK": "What does that mean?", "MIGRATE": {"BUTTON1": "More Details", "BUTTON2": "Migrate", "TEXT1": "Your wallet contains pre-zarcanum outputs"}, "MODAL_WHITELIST_ASSET": {"FIELD_TITLE": "Asset ID", "FORM_ERRORS": {"ERROR1": "Invalid Asset ID"}, "TITLE": "Whitelist asset"}, "REGISTER_ALIAS": "Register an alias", "TABS": {"ASSETS": "Assets", "CONTRACTS": "Contracts", "CONTROL_ASSETS": "Control assets", "HISTORY": "History", "MESSAGES": "Messages", "RECEIVE": "Receive", "SEND": "Send", "STAKING": "Staking"}, "TOOLTIPS": {"CLOSE": "Close", "EDIT_ALIAS": "Edit alias", "HIDE_BALANCE": "Hide balance", "REMOVE": "Remove wallet", "SETTINGS": "Settings", "SHOW_BALANCE": "Show balance", "TRANSFER_ALIAS": "Transfer alias", "WHITELIST_ASSET": "Whitelist asset"}}, "WALLET_DETAILS": {"BUTTON_REMOVE": "Remove wallet", "BUTTON_SAVE": "Save", "CREATE_PASSWORD_SECURE": "Create a password to secure your seed", "FORM": {"CONFIRM_PASSWORD": "Confirm password", "GENERATE_SECURE_SEED": "Generate Secure Seed", "SECURED_SEED_WILL_REQUIRE": "Secure seed will require this password to restore."}, "FORM_ERRORS": {"MAX_LENGTH": "Maximum name length reached", "NAME_DUPLICATE": "Name is duplicate", "NAME_REQUIRED": "Name is required", "PASSWORDS_DONT_MATCH": "The passwords entered do not match"}, "INFO": "info", "LABEL_FILE_LOCATION": "Wallet file location", "LABEL_NAME": "Wallet name", "LABEL_SEED_PHRASE": "Seed phrase", "REMEMBER_YOU_WILL_REQUIRE": "Secure seed will require password to restore.", "RESYNC_WALLET": "Resync wallet data", "RESYNC_WALLET_BUTTON": "Resync Wallet", "SEED_IS_SECURED": "Seed is secured", "SEED_IS_UNSECURED": "Seed is unsecured", "SEED_PHRASE_HINT": "Click to reveal the seed phrase", "WALLET_OPTIONS": "Wallet options...", "WHITELIST_ASSET": "Whitelist asset"}}