button,
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--button-color);
    transition: all 0.25s ease;
    border: none;
    outline: none;
    background-color: transparent;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 0.8rem;
    font-size: 1.8rem;

    &:not(:disabled) {
        cursor: pointer;
    }

    &:not(:disabled):hover {
        cursor: pointer;
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &.primary,
    &.outline {
        width: 100%;
        padding: 0 2rem 0 2rem;

        &.small {
            min-height: 4.5rem;
        }

        &.big {
            min-height: 5.3rem;
        }
    }

    &.primary {
        background-color: var(--azure-500);
        color: var(--white-500);

        &:not(:disabled) {
            &:focus,
            &:hover,
            &.active {
                background-color: var(--azure-600);
            }
        }
    }

    &.outline {
        border: 1px solid var(--azure-500);

        &:not(:disabled) {
            &:hover,
            &:focus,
            &.active {
                background-color: var(--gray-900);
            }
        }
    }

    &.btn-icon {
        min-width: 2rem;
        min-height: 2rem;

        &.small {
            min-width: 2.8rem;
            min-height: 2.8rem;
        }

        &.big {
            min-width: 4rem;
            min-height: 4rem;
        }
    }

    &.btn-icon {
        background-color: var(--btn-icon-background);
        transition: background-color 0.2s ease-in-out;

        &.circle {
            border-radius: 50%;
        }

        .row-options {
        }

        &:hover,
        &:focus {
            background-color: var(--btn-icon-hover-background);
        }
    }
}

.btn-light-background {
    background-color: var(--btn-icon-background);

    &:not(:disabled):hover {
        background-color: var(--btn-icon-hover-background);
    }
}

.light {
    button,
    .btn {
        &.outline {
            &:not(:disabled) {
                &:hover,
                &:focus,
                &.active {
                    background-color: rgba(0, 0, 0, 0.05);
                }
            }
        }

        &.btn-icon {
            &.row-options {
                color: #1f8feb;
            }
        }
    }
}
