.seed-phrase {
    .content {
        .item {
            .number {
                width: 1.8rem;
                height: 1.8rem;
                border-radius: 50%;
                font-size: 1rem;
            }

            .word {
                white-space: nowrap;
            }
        }
    }
}

.light {
    .seed-phrase {
        .content {
            .item {
                border: var(--border);
                background-color: #1f8feb1a;
                color: #1f8feb;

                .number {
                    background-color: #1f8feb26;
                    color: #1f8feb;
                }
            }
        }
    }
}

.dark {
    .seed-phrase {
        .content {
            .item {
                border: var(--border);
                background-color: var(--gray-900);

                .number {
                    background-color: var(--gray-600);
                }
            }
        }
    }
}
