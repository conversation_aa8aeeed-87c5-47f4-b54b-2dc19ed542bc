.wallet {
    border-radius: 0.8rem;
    position: relative;
    display: flex;
    flex-direction: column;
    max-width: 19rem;
    min-width: 19rem;
    width: 100%;
    padding: 1.2rem;
    background-color: var(--wallet-background);
    border: var(--wallet-border);
    cursor: pointer;
    color: var(--wallet-text);

    &.offset-testnet {
        margin: 1rem 0.5rem 0 0;
    }

    .testnet {
        background-color: var(--red-500);
        color: var(--white);
        text-transform: uppercase;
        font-weight: bold;
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
        border-radius: 999px;
        z-index: 10;

        position: absolute;
        right: -0.5rem;
        top: -0.4rem;
    }

    .content {
        z-index: 10;
    }

    .header {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;

        .left {
            overflow: hidden;

            .name {
                .indicator {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    width: 1.8rem;
                    height: 1.8rem;
                    padding: 0.5rem;
                    margin-right: 0.8rem;
                    font-size: 1.2rem;
                    line-height: 1;
                    background-color: var(--white-500);
                    color: var(--azure-500);
                }
            }
        }
    }

    .balance {
        display: flex;
        align-items: center;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .price {
        font-size: 1.4rem;
        font-weight: 600;
        line-height: 1.2;

        display: flex;
        flex-wrap: nowrap;
        align-items: baseline;

        .percent,
        .currency {
            font-size: 1.4rem;
            font-weight: 400;
        }
    }

    .staking {
        margin-top: 0.8rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .account-synchronization {
        display: flex;
        align-items: center;
        width: 100%;

        .progress-bar {
            border-radius: 1rem;
            flex: 1 0 auto;
            height: 0.4rem;
            overflow: hidden;

            .fill {
                height: 100%;
            }
        }

        .progress-percent {
            flex: 0 0 auto;
            font-size: 1.4rem;
            line-height: 1.2;
            padding-left: 1rem;
        }
    }

    &.active {
        color: var(--wallet-active-text);
        border: none;
        padding: 1.35rem;
        background: var(--wallet-active-background);
    }

    &.auditable,
    &.watch-only {
        border-width: 0;
        padding: 1.35rem;
        background: var(--wallet-auditable-watch-only-background);
    }

    &.auditable {
        &:hover:not(.active) {
            padding: 1.2rem;
            border-width: 0.15rem;
        }

        &.active {
            padding: 1.35rem;
            border: none;
            background: var(--wallet-auditable-active-background);
        }
    }

    &.watch-only {
        color: var(--wallet-watch-only-text);

        &:after {
            content: '';
            display: block;
            background: var(--wallet-watch-only-after-background);
            position: absolute;
            border-radius: 0.6rem;
            left: 0.25rem;
            right: 0.25rem;
            top: 0.25rem;
            bottom: 0.25rem;
            z-index: 1;
        }

        &.active,
        &:hover {
            background: var(--wallet-watch-only-active-background);
        }
    }

    &:hover:not(.active):not(.watch-only) {
        border-color: var(--wallet-border-color-hover);
    }

    &:focus {
        outline: none;
    }

    &:focus-visible {
        outline: 2px solid;
        outline-offset: 4px;
    }

    .progress-bar {
        background-color: var(--gray-800);

        .fill {
            background-color: var(--white-500);
        }
    }
}

app-wallet-card {
    &:last-child {
        .wallet {
            margin-bottom: 0 !important;
        }
    }
}

.light {
    .wallet {
        &.active {
            .header {
                .close {
                    color: #ffffff;
                }
            }

            &.watch-only {
                .header {
                    .close {
                        color: var(--wallet-watch-only-text);
                    }
                }
            }
        }

        .progress-bar {
            background-color: #0c0c3a1a;

            .fill {
                background-color: var(--aqua-500);
            }
        }

        &.active .progress-bar {
            .fill {
                background-color: var(--white-500);
            }
        }
    }
}
