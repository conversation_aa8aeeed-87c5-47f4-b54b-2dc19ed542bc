html,
body {
    position: fixed;
    overflow: hidden;
    overscroll-behavior: none;

    width: 100%;
    min-width: 1200px;
    max-width: 100vw;

    height: 100vh;
    min-height: 700px;
}

body {
    background: var(--main-background);
    color: var(--main-text);
}

app-root {
    display: flex;
    flex-wrap: nowrap;

    width: 100%;
    height: 100%;
}

.page-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 40px;
        flex: 0 0 auto;

        .left,
        .right {
            display: flex;
            align-items: center;
        }
    }

    .page-content {
        width: 100%;
        height: auto;
        display: flex;
        flex-direction: column;
        flex: auto;
        overflow: hidden;
        padding: 2rem;
        border-radius: 0.8rem;
        background-color: var(--page-content-background);
    }
}
