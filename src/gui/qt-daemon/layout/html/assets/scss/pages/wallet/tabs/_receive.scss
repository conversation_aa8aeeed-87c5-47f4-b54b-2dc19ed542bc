app-receive {
    .container {
        .wrap-qr {
            max-width: 30rem;
            max-height: 30rem;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                border-radius: 0.8rem;
            }
        }

        .address {
            width: 27.1rem;
            height: 4rem;
            border: var(--border);
        }
    }
}

.light {
    app-receive {
        .container {
            .wrap-qr {
                img {
                    border: var(--border);
                }
            }
        }
    }
}
