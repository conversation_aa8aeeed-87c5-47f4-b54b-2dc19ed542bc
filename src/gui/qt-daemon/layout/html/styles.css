/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js??ruleSet[1].rules[6].rules[1].use[0]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[6].rules[1].use[1]!./src/styles.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.mat-badge-content{font-weight:600;font-size:12px;font-family:Roboto, "Helvetica Neue", sans-serif}.mat-badge-small .mat-badge-content{font-size:9px}.mat-badge-large .mat-badge-content{font-size:24px}.mat-h1,.mat-headline,.mat-typography .mat-h1,.mat-typography .mat-headline,.mat-typography h1{font:400 24px/32px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2,.mat-title,.mat-typography .mat-h2,.mat-typography .mat-title,.mat-typography h2{font:500 20px/32px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h3,.mat-subheading-2,.mat-typography .mat-h3,.mat-typography .mat-subheading-2,.mat-typography h3{font:400 16px/28px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h4,.mat-subheading-1,.mat-typography .mat-h4,.mat-typography .mat-subheading-1,.mat-typography h4{font:400 15px/24px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h5,.mat-typography .mat-h5,.mat-typography h5{font:400 calc(14px * 0.83)/20px Roboto, "Helvetica Neue", sans-serif;margin:0 0 12px}.mat-h6,.mat-typography .mat-h6,.mat-typography h6{font:400 calc(14px * 0.67)/20px Roboto, "Helvetica Neue", sans-serif;margin:0 0 12px}.mat-body-strong,.mat-body-2,.mat-typography .mat-body-strong,.mat-typography .mat-body-2{font:500 14px/24px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-body,.mat-body-1,.mat-typography .mat-body,.mat-typography .mat-body-1,.mat-typography{font:400 14px/20px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-body p,.mat-body-1 p,.mat-typography .mat-body p,.mat-typography .mat-body-1 p,.mat-typography p{margin:0 0 12px}.mat-small,.mat-caption,.mat-typography .mat-small,.mat-typography .mat-caption{font:400 12px/20px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-display-4,.mat-typography .mat-display-4{font:300 112px/112px Roboto, "Helvetica Neue", sans-serif;letter-spacing:-0.05em;margin:0 0 56px}.mat-display-3,.mat-typography .mat-display-3{font:400 56px/56px Roboto, "Helvetica Neue", sans-serif;letter-spacing:-0.02em;margin:0 0 64px}.mat-display-2,.mat-typography .mat-display-2{font:400 45px/48px Roboto, "Helvetica Neue", sans-serif;letter-spacing:-0.005em;margin:0 0 64px}.mat-display-1,.mat-typography .mat-display-1{font:400 34px/40px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-bottom-sheet-container{font:400 14px/20px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-button,.mat-raised-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button,.mat-fab,.mat-mini-fab{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:14px;font-weight:500}.mat-button-toggle{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-card{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-card-title{font-size:24px;font-weight:500}.mat-card-header .mat-card-title{font-size:20px}.mat-card-subtitle,.mat-card-content{font-size:14px}.mat-checkbox{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-checkbox-layout .mat-checkbox-label{line-height:24px}.mat-chip{font-size:14px;font-weight:500}.mat-chip .mat-chip-trailing-icon.mat-icon,.mat-chip .mat-chip-remove.mat-icon{font-size:18px}.mat-table{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-header-cell{font-size:12px;font-weight:500}.mat-cell,.mat-footer-cell{font-size:14px}.mat-calendar{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-calendar-body{font-size:13px}.mat-calendar-body-label,.mat-calendar-period-button{font-size:14px;font-weight:500}.mat-calendar-table-header th{font-size:11px;font-weight:400}.mat-dialog-title{font:500 20px/32px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-expansion-panel-header{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:15px;font-weight:400}.mat-expansion-panel-content{font:400 14px/20px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-form-field{font-size:inherit;font-weight:400;line-height:1.125;font-family:Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-form-field-wrapper{padding-bottom:1.34375em}.mat-form-field-prefix .mat-icon,.mat-form-field-suffix .mat-icon{font-size:150%;line-height:1.125}.mat-form-field-prefix .mat-icon-button,.mat-form-field-suffix .mat-icon-button{height:1.5em;width:1.5em}.mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-suffix .mat-icon-button .mat-icon{height:1.125em;line-height:1.125}.mat-form-field-infix{padding:.5em 0;border-top:.84375em solid rgba(0,0,0,0)}.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.34375em) scale(0.75);width:133.3333333333%}.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.34374em) scale(0.75);width:133.3333433333%}.mat-form-field-label-wrapper{top:-0.84375em;padding-top:.84375em}.mat-form-field-label{top:1.34375em}.mat-form-field-underline{bottom:1.34375em}.mat-form-field-subscript-wrapper{font-size:75%;margin-top:.6666666667em;top:calc(100% - 1.7916666667em)}.mat-form-field-appearance-legacy .mat-form-field-wrapper{padding-bottom:1.25em}.mat-form-field-appearance-legacy .mat-form-field-infix{padding:.4375em 0}.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.001px);width:133.3333333333%}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.00101px);width:133.3333433333%}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.00102px);width:133.3333533333%}.mat-form-field-appearance-legacy .mat-form-field-label{top:1.28125em}.mat-form-field-appearance-legacy .mat-form-field-underline{bottom:1.25em}.mat-form-field-appearance-legacy .mat-form-field-subscript-wrapper{margin-top:.5416666667em;top:calc(100% - 1.6666666667em)}@media print{.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28122em) scale(0.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28121em) scale(0.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.2812em) scale(0.75)}}.mat-form-field-appearance-fill .mat-form-field-infix{padding:.25em 0 .75em 0}.mat-form-field-appearance-fill .mat-form-field-label{top:1.09375em;margin-top:-0.5em}.mat-form-field-appearance-fill.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-fill.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-0.59375em) scale(0.75);width:133.3333333333%}.mat-form-field-appearance-fill.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-0.59374em) scale(0.75);width:133.3333433333%}.mat-form-field-appearance-outline .mat-form-field-infix{padding:1em 0 1em 0}.mat-form-field-appearance-outline .mat-form-field-label{top:1.84375em;margin-top:-0.25em}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.59375em) scale(0.75);width:133.3333333333%}.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.59374em) scale(0.75);width:133.3333433333%}.mat-grid-tile-header,.mat-grid-tile-footer{font-size:14px}.mat-grid-tile-header .mat-line,.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2),.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:12px}input.mat-input-element{margin-top:-0.0625em}.mat-menu-item{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:14px;font-weight:400}.mat-paginator,.mat-paginator-page-size .mat-select-trigger{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:12px}.mat-radio-button{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-select{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-select-trigger{height:1.125em}.mat-slide-toggle-content{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-slider-thumb-label-text{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:12px;font-weight:500}.mat-stepper-vertical,.mat-stepper-horizontal{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-step-label{font-size:14px;font-weight:400}.mat-step-sub-label-error{font-weight:normal}.mat-step-label-error{font-size:14px}.mat-step-label-selected{font-size:14px;font-weight:500}.mat-tab-group{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-tab-label,.mat-tab-link{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:14px;font-weight:500}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font:500 20px/32px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal;margin:0}.mat-tooltip{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:10px;padding-top:6px;padding-bottom:6px}.mat-tooltip-handset{font-size:14px;padding-top:8px;padding-bottom:8px}.mat-list-item{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-list-option{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-list-base .mat-list-item{font-size:16px}.mat-list-base .mat-list-item .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base .mat-list-item .mat-line:nth-child(n+2){font-size:14px}.mat-list-base .mat-list-option{font-size:16px}.mat-list-base .mat-list-option .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base .mat-list-option .mat-line:nth-child(n+2){font-size:14px}.mat-list-base .mat-subheader{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:14px;font-weight:500}.mat-list-base[dense] .mat-list-item{font-size:12px}.mat-list-base[dense] .mat-list-item .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense] .mat-list-item .mat-line:nth-child(n+2){font-size:12px}.mat-list-base[dense] .mat-list-option{font-size:12px}.mat-list-base[dense] .mat-list-option .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense] .mat-list-option .mat-line:nth-child(n+2){font-size:12px}.mat-list-base[dense] .mat-subheader{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:12px;font-weight:500}.mat-option{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:16px}.mat-optgroup-label{font:500 14px/24px Roboto, "Helvetica Neue", sans-serif;letter-spacing:normal}.mat-simple-snackbar{font-family:Roboto, "Helvetica Neue", sans-serif;font-size:14px}.mat-simple-snackbar-action{line-height:1;font-family:inherit;font-size:inherit;font-weight:500}.mat-tree{font-family:Roboto, "Helvetica Neue", sans-serif}.mat-tree-node,.mat-nested-tree-node{font-weight:400;font-size:14px}.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0)}.cdk-high-contrast-active .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-option{color:#fff}.mat-option:hover:not(.mat-option-disabled),.mat-option:focus:not(.mat-option-disabled){background:rgba(255,255,255,.04)}.mat-option.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled){background:rgba(255,255,255,.04)}.mat-option.mat-active{background:rgba(255,255,255,.04);color:#fff}.mat-option.mat-option-disabled{color:rgba(255,255,255,.5)}.mat-primary .mat-option.mat-selected:not(.mat-option-disabled){color:#c2185b}.mat-accent .mat-option.mat-selected:not(.mat-option-disabled){color:#b0bec5}.mat-warn .mat-option.mat-selected:not(.mat-option-disabled){color:#f44336}.mat-optgroup-label{color:rgba(255,255,255,.7)}.mat-optgroup-disabled .mat-optgroup-label{color:rgba(255,255,255,.5)}.mat-pseudo-checkbox{color:rgba(255,255,255,.7)}.mat-pseudo-checkbox::after{color:#303030}.mat-pseudo-checkbox-disabled{color:#686868}.mat-primary .mat-pseudo-checkbox-checked,.mat-primary .mat-pseudo-checkbox-indeterminate{background:#c2185b}.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-indeterminate,.mat-accent .mat-pseudo-checkbox-checked,.mat-accent .mat-pseudo-checkbox-indeterminate{background:#b0bec5}.mat-warn .mat-pseudo-checkbox-checked,.mat-warn .mat-pseudo-checkbox-indeterminate{background:#f44336}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background:#686868}.mat-app-background{background-color:#303030;color:#fff}.mat-elevation-z0{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z1{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z2{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z3{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z4{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z5{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 5px 8px 0px rgba(0, 0, 0, 0.14),0px 1px 14px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z6{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z7{box-shadow:0px 4px 5px -2px rgba(0, 0, 0, 0.2),0px 7px 10px 1px rgba(0, 0, 0, 0.14),0px 2px 16px 1px rgba(0, 0, 0, 0.12)}.mat-elevation-z8{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z9{box-shadow:0px 5px 6px -3px rgba(0, 0, 0, 0.2),0px 9px 12px 1px rgba(0, 0, 0, 0.14),0px 3px 16px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z10{box-shadow:0px 6px 6px -3px rgba(0, 0, 0, 0.2),0px 10px 14px 1px rgba(0, 0, 0, 0.14),0px 4px 18px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z11{box-shadow:0px 6px 7px -4px rgba(0, 0, 0, 0.2),0px 11px 15px 1px rgba(0, 0, 0, 0.14),0px 4px 20px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z12{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 12px 17px 2px rgba(0, 0, 0, 0.14),0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z13{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 13px 19px 2px rgba(0, 0, 0, 0.14),0px 5px 24px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z14{box-shadow:0px 7px 9px -4px rgba(0, 0, 0, 0.2),0px 14px 21px 2px rgba(0, 0, 0, 0.14),0px 5px 26px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z15{box-shadow:0px 8px 9px -5px rgba(0, 0, 0, 0.2),0px 15px 22px 2px rgba(0, 0, 0, 0.14),0px 6px 28px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z16{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z17{box-shadow:0px 8px 11px -5px rgba(0, 0, 0, 0.2),0px 17px 26px 2px rgba(0, 0, 0, 0.14),0px 6px 32px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z18{box-shadow:0px 9px 11px -5px rgba(0, 0, 0, 0.2),0px 18px 28px 2px rgba(0, 0, 0, 0.14),0px 7px 34px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z19{box-shadow:0px 9px 12px -6px rgba(0, 0, 0, 0.2),0px 19px 29px 2px rgba(0, 0, 0, 0.14),0px 7px 36px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z20{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 20px 31px 3px rgba(0, 0, 0, 0.14),0px 8px 38px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z21{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 21px 33px 3px rgba(0, 0, 0, 0.14),0px 8px 40px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z22{box-shadow:0px 10px 14px -6px rgba(0, 0, 0, 0.2),0px 22px 35px 3px rgba(0, 0, 0, 0.14),0px 8px 42px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z23{box-shadow:0px 11px 14px -7px rgba(0, 0, 0, 0.2),0px 23px 36px 3px rgba(0, 0, 0, 0.14),0px 9px 44px 8px rgba(0, 0, 0, 0.12)}.mat-elevation-z24{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-theme-loaded-marker{display:none}.mat-autocomplete-panel{background:#424242;color:#fff}.mat-autocomplete-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-autocomplete-panel .mat-option.mat-selected:not(.mat-active):not(:hover){background:#424242}.mat-autocomplete-panel .mat-option.mat-selected:not(.mat-active):not(:hover):not(.mat-option-disabled){color:#fff}.mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-hidden .mat-badge-content{display:none}.mat-badge-content{position:absolute;text-align:center;display:inline-block;border-radius:50%;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;pointer-events:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:16px;height:16px;line-height:16px}.mat-badge-small.mat-badge-above .mat-badge-content{top:-8px}.mat-badge-small.mat-badge-below .mat-badge-content{bottom:-8px}.mat-badge-small.mat-badge-before .mat-badge-content{left:-16px}[dir=rtl] .mat-badge-small.mat-badge-before .mat-badge-content{left:auto;right:-16px}.mat-badge-small.mat-badge-after .mat-badge-content{right:-16px}[dir=rtl] .mat-badge-small.mat-badge-after .mat-badge-content{right:auto;left:-16px}.mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-8px}.mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-8px}.mat-badge-medium .mat-badge-content{width:22px;height:22px;line-height:22px}.mat-badge-medium.mat-badge-above .mat-badge-content{top:-11px}.mat-badge-medium.mat-badge-below .mat-badge-content{bottom:-11px}.mat-badge-medium.mat-badge-before .mat-badge-content{left:-22px}[dir=rtl] .mat-badge-medium.mat-badge-before .mat-badge-content{left:auto;right:-22px}.mat-badge-medium.mat-badge-after .mat-badge-content{right:-22px}[dir=rtl] .mat-badge-medium.mat-badge-after .mat-badge-content{right:auto;left:-22px}.mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-11px}.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-11px}.mat-badge-large .mat-badge-content{width:28px;height:28px;line-height:28px}.mat-badge-large.mat-badge-above .mat-badge-content{top:-14px}.mat-badge-large.mat-badge-below .mat-badge-content{bottom:-14px}.mat-badge-large.mat-badge-before .mat-badge-content{left:-28px}[dir=rtl] .mat-badge-large.mat-badge-before .mat-badge-content{left:auto;right:-28px}.mat-badge-large.mat-badge-after .mat-badge-content{right:-28px}[dir=rtl] .mat-badge-large.mat-badge-after .mat-badge-content{right:auto;left:-28px}.mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-14px}.mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-14px}.mat-badge-content{color:#fff;background:#c2185b}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-accent .mat-badge-content{background:#b0bec5;color:rgba(0,0,0,.87)}.mat-badge-warn .mat-badge-content{color:#fff;background:#f44336}.mat-badge-disabled .mat-badge-content{background:#6e6e6e;color:rgba(255,255,255,.5)}.mat-bottom-sheet-container{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12);background:#424242;color:#fff}.mat-button,.mat-icon-button,.mat-stroked-button{color:inherit;background:rgba(0,0,0,0)}.mat-button.mat-primary,.mat-icon-button.mat-primary,.mat-stroked-button.mat-primary{color:#c2185b}.mat-button.mat-accent,.mat-icon-button.mat-accent,.mat-stroked-button.mat-accent{color:#b0bec5}.mat-button.mat-warn,.mat-icon-button.mat-warn,.mat-stroked-button.mat-warn{color:#f44336}.mat-button.mat-primary.mat-button-disabled,.mat-button.mat-accent.mat-button-disabled,.mat-button.mat-warn.mat-button-disabled,.mat-button.mat-button-disabled.mat-button-disabled,.mat-icon-button.mat-primary.mat-button-disabled,.mat-icon-button.mat-accent.mat-button-disabled,.mat-icon-button.mat-warn.mat-button-disabled,.mat-icon-button.mat-button-disabled.mat-button-disabled,.mat-stroked-button.mat-primary.mat-button-disabled,.mat-stroked-button.mat-accent.mat-button-disabled,.mat-stroked-button.mat-warn.mat-button-disabled,.mat-stroked-button.mat-button-disabled.mat-button-disabled{color:rgba(255,255,255,.3)}.mat-button.mat-primary .mat-button-focus-overlay,.mat-icon-button.mat-primary .mat-button-focus-overlay,.mat-stroked-button.mat-primary .mat-button-focus-overlay{background-color:#c2185b}.mat-button.mat-accent .mat-button-focus-overlay,.mat-icon-button.mat-accent .mat-button-focus-overlay,.mat-stroked-button.mat-accent .mat-button-focus-overlay{background-color:#b0bec5}.mat-button.mat-warn .mat-button-focus-overlay,.mat-icon-button.mat-warn .mat-button-focus-overlay,.mat-stroked-button.mat-warn .mat-button-focus-overlay{background-color:#f44336}.mat-button.mat-button-disabled .mat-button-focus-overlay,.mat-icon-button.mat-button-disabled .mat-button-focus-overlay,.mat-stroked-button.mat-button-disabled .mat-button-focus-overlay{background-color:rgba(0,0,0,0)}.mat-button .mat-ripple-element,.mat-icon-button .mat-ripple-element,.mat-stroked-button .mat-ripple-element{opacity:.1;background-color:currentColor}.mat-button-focus-overlay{background:#fff}.mat-stroked-button:not(.mat-button-disabled){border-color:rgba(255,255,255,.12)}.mat-flat-button,.mat-raised-button,.mat-fab,.mat-mini-fab{color:#fff;background-color:#424242}.mat-flat-button.mat-primary,.mat-raised-button.mat-primary,.mat-fab.mat-primary,.mat-mini-fab.mat-primary{color:#fff}.mat-flat-button.mat-accent,.mat-raised-button.mat-accent,.mat-fab.mat-accent,.mat-mini-fab.mat-accent{color:rgba(0,0,0,.87)}.mat-flat-button.mat-warn,.mat-raised-button.mat-warn,.mat-fab.mat-warn,.mat-mini-fab.mat-warn{color:#fff}.mat-flat-button.mat-primary.mat-button-disabled,.mat-flat-button.mat-accent.mat-button-disabled,.mat-flat-button.mat-warn.mat-button-disabled,.mat-flat-button.mat-button-disabled.mat-button-disabled,.mat-raised-button.mat-primary.mat-button-disabled,.mat-raised-button.mat-accent.mat-button-disabled,.mat-raised-button.mat-warn.mat-button-disabled,.mat-raised-button.mat-button-disabled.mat-button-disabled,.mat-fab.mat-primary.mat-button-disabled,.mat-fab.mat-accent.mat-button-disabled,.mat-fab.mat-warn.mat-button-disabled,.mat-fab.mat-button-disabled.mat-button-disabled,.mat-mini-fab.mat-primary.mat-button-disabled,.mat-mini-fab.mat-accent.mat-button-disabled,.mat-mini-fab.mat-warn.mat-button-disabled,.mat-mini-fab.mat-button-disabled.mat-button-disabled{color:rgba(255,255,255,.3)}.mat-flat-button.mat-primary,.mat-raised-button.mat-primary,.mat-fab.mat-primary,.mat-mini-fab.mat-primary{background-color:#c2185b}.mat-flat-button.mat-accent,.mat-raised-button.mat-accent,.mat-fab.mat-accent,.mat-mini-fab.mat-accent{background-color:#b0bec5}.mat-flat-button.mat-warn,.mat-raised-button.mat-warn,.mat-fab.mat-warn,.mat-mini-fab.mat-warn{background-color:#f44336}.mat-flat-button.mat-primary.mat-button-disabled,.mat-flat-button.mat-accent.mat-button-disabled,.mat-flat-button.mat-warn.mat-button-disabled,.mat-flat-button.mat-button-disabled.mat-button-disabled,.mat-raised-button.mat-primary.mat-button-disabled,.mat-raised-button.mat-accent.mat-button-disabled,.mat-raised-button.mat-warn.mat-button-disabled,.mat-raised-button.mat-button-disabled.mat-button-disabled,.mat-fab.mat-primary.mat-button-disabled,.mat-fab.mat-accent.mat-button-disabled,.mat-fab.mat-warn.mat-button-disabled,.mat-fab.mat-button-disabled.mat-button-disabled,.mat-mini-fab.mat-primary.mat-button-disabled,.mat-mini-fab.mat-accent.mat-button-disabled,.mat-mini-fab.mat-warn.mat-button-disabled,.mat-mini-fab.mat-button-disabled.mat-button-disabled{background-color:rgba(255,255,255,.12)}.mat-flat-button.mat-primary .mat-ripple-element,.mat-raised-button.mat-primary .mat-ripple-element,.mat-fab.mat-primary .mat-ripple-element,.mat-mini-fab.mat-primary .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-flat-button.mat-accent .mat-ripple-element,.mat-raised-button.mat-accent .mat-ripple-element,.mat-fab.mat-accent .mat-ripple-element,.mat-mini-fab.mat-accent .mat-ripple-element{background-color:rgba(0,0,0,.1)}.mat-flat-button.mat-warn .mat-ripple-element,.mat-raised-button.mat-warn .mat-ripple-element,.mat-fab.mat-warn .mat-ripple-element,.mat-mini-fab.mat-warn .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-stroked-button:not([class*=mat-elevation-z]),.mat-flat-button:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-raised-button:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-raised-button:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-raised-button.mat-button-disabled:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-fab:not([class*=mat-elevation-z]),.mat-mini-fab:not([class*=mat-elevation-z]){box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-fab:not(.mat-button-disabled):active:not([class*=mat-elevation-z]),.mat-mini-fab:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 12px 17px 2px rgba(0, 0, 0, 0.14),0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-fab.mat-button-disabled:not([class*=mat-elevation-z]),.mat-mini-fab.mat-button-disabled:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.mat-button-toggle{color:rgba(255,255,255,.5)}.mat-button-toggle .mat-button-toggle-focus-overlay{background-color:rgba(255,255,255,.12)}.mat-button-toggle-appearance-standard{color:#fff;background:#424242}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:#fff}.mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle{border-left:solid 1px #595959}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle{border-left:none;border-right:solid 1px #595959}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle+.mat-button-toggle{border-left:none;border-right:none;border-top:solid 1px #595959}.mat-button-toggle-checked{background-color:#212121;color:rgba(255,255,255,.7)}.mat-button-toggle-checked.mat-button-toggle-appearance-standard{color:#fff}.mat-button-toggle-disabled{color:rgba(255,255,255,.3);background-color:#000}.mat-button-toggle-disabled.mat-button-toggle-appearance-standard{background:#424242}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:#424242}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border:solid 1px #595959}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{line-height:48px}.mat-card{background:#424242;color:#fff}.mat-card:not([class*=mat-elevation-z]){box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-card.mat-card-flat:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-card-subtitle{color:rgba(255,255,255,.7)}.mat-checkbox-frame{border-color:rgba(255,255,255,.7)}.mat-checkbox-checkmark{fill:#303030}.mat-checkbox-checkmark-path{stroke:#303030 !important}.mat-checkbox-mixedmark{background-color:#303030}.mat-checkbox-indeterminate.mat-primary .mat-checkbox-background,.mat-checkbox-checked.mat-primary .mat-checkbox-background{background-color:#c2185b}.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,.mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:#b0bec5}.mat-checkbox-indeterminate.mat-warn .mat-checkbox-background,.mat-checkbox-checked.mat-warn .mat-checkbox-background{background-color:#f44336}.mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background,.mat-checkbox-disabled.mat-checkbox-indeterminate .mat-checkbox-background{background-color:#686868}.mat-checkbox-disabled:not(.mat-checkbox-checked) .mat-checkbox-frame{border-color:#686868}.mat-checkbox-disabled .mat-checkbox-label{color:rgba(255,255,255,.5)}.mat-checkbox .mat-ripple-element{background-color:#fff}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-primary .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-primary .mat-ripple-element{background:#c2185b}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element{background:#b0bec5}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-warn .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-warn .mat-ripple-element{background:#f44336}.mat-chip.mat-standard-chip{background-color:#616161;color:#fff}.mat-chip.mat-standard-chip .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip:not(.mat-chip-disabled):active{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-chip.mat-standard-chip:not(.mat-chip-disabled) .mat-chip-remove:hover{opacity:.54}.mat-chip.mat-standard-chip.mat-chip-disabled{opacity:.4}.mat-chip.mat-standard-chip::after{background:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary{background-color:#c2185b;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn{background-color:#f44336;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent .mat-chip-remove{color:rgba(0,0,0,.87);opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent .mat-ripple-element{background-color:rgba(0,0,0,.1)}.mat-table{background:#424242}.mat-table thead,.mat-table tbody,.mat-table tfoot,mat-header-row,mat-row,mat-footer-row,[mat-header-row],[mat-row],[mat-footer-row],.mat-table-sticky{background:inherit}mat-row,mat-header-row,mat-footer-row,th.mat-header-cell,td.mat-cell,td.mat-footer-cell{border-bottom-color:rgba(255,255,255,.12)}.mat-header-cell{color:rgba(255,255,255,.7)}.mat-cell,.mat-footer-cell{color:#fff}.mat-calendar-arrow{fill:#fff}.mat-datepicker-toggle,.mat-datepicker-content .mat-calendar-next-button,.mat-datepicker-content .mat-calendar-previous-button{color:#fff}.mat-calendar-table-header-divider::after{background:rgba(255,255,255,.12)}.mat-calendar-table-header,.mat-calendar-body-label{color:rgba(255,255,255,.7)}.mat-calendar-body-cell-content,.mat-date-range-input-separator{color:#fff;border-color:rgba(0,0,0,0)}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:rgba(255,255,255,.5)}.mat-form-field-disabled .mat-date-range-input-separator{color:rgba(255,255,255,.5)}.mat-calendar-body-in-preview{color:rgba(255,255,255,.24)}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(255,255,255,.5)}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(255,255,255,.3)}.mat-calendar-body-in-range::before{background:rgba(194,24,91,.2)}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(194, 24, 91, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(194, 24, 91, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-calendar-body-selected{background-color:#c2185b;color:#fff}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(194,24,91,.4)}.mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px #fff}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(194,24,91,.3)}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(194,24,91,.3)}}.mat-datepicker-content{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12);background-color:#424242;color:#fff}.mat-datepicker-content.mat-accent .mat-calendar-body-in-range::before{background:rgba(176,190,197,.2)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-bridge-start::before,.mat-datepicker-content.mat-accent [dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(176, 190, 197, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-bridge-end::before,.mat-datepicker-content.mat-accent [dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(176, 190, 197, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-accent .mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-datepicker-content.mat-accent .mat-calendar-body-selected{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-datepicker-content.mat-accent .mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(176,190,197,.4)}.mat-datepicker-content.mat-accent .mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px rgba(0,0,0,.87)}.mat-datepicker-content.mat-accent .cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.mat-datepicker-content.mat-accent .cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(176,190,197,.3)}@media(hover: hover){.mat-datepicker-content.mat-accent .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(176,190,197,.3)}}.mat-datepicker-content.mat-warn .mat-calendar-body-in-range::before{background:rgba(244,67,54,.2)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-bridge-start::before,.mat-datepicker-content.mat-warn [dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(244, 67, 54, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-bridge-end::before,.mat-datepicker-content.mat-warn [dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(244, 67, 54, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-warn .mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-datepicker-content.mat-warn .mat-calendar-body-selected{background-color:#f44336;color:#fff}.mat-datepicker-content.mat-warn .mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(244,67,54,.4)}.mat-datepicker-content.mat-warn .mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px #fff}.mat-datepicker-content.mat-warn .cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.mat-datepicker-content.mat-warn .cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(244,67,54,.3)}@media(hover: hover){.mat-datepicker-content.mat-warn .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(244,67,54,.3)}}.mat-datepicker-content-touch{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-datepicker-toggle-active{color:#c2185b}.mat-datepicker-toggle-active.mat-accent{color:#b0bec5}.mat-datepicker-toggle-active.mat-warn{color:#f44336}.mat-date-range-input-inner[disabled]{color:rgba(255,255,255,.5)}.mat-dialog-container{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12);background:#424242;color:#fff}.mat-divider{border-top-color:rgba(255,255,255,.12)}.mat-divider-vertical{border-right-color:rgba(255,255,255,.12)}.mat-expansion-panel{background:#424242;color:#fff}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-action-row{border-top-color:rgba(255,255,255,.12)}.mat-expansion-panel .mat-expansion-panel-header.cdk-keyboard-focused:not([aria-disabled=true]),.mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled=true]),.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]){background:rgba(255,255,255,.04)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded):not([aria-disabled=true]) .mat-expansion-panel-header:hover{background:#424242}}.mat-expansion-panel-header-title{color:#fff}.mat-expansion-panel-header-description,.mat-expansion-indicator::after{color:rgba(255,255,255,.7)}.mat-expansion-panel-header[aria-disabled=true]{color:rgba(255,255,255,.3)}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header{height:48px}.mat-expansion-panel-header.mat-expanded{height:64px}.mat-form-field-label{color:rgba(255,255,255,.7)}.mat-hint{color:rgba(255,255,255,.7)}.mat-form-field.mat-focused .mat-form-field-label{color:#c2185b}.mat-form-field.mat-focused .mat-form-field-label.mat-accent{color:#b0bec5}.mat-form-field.mat-focused .mat-form-field-label.mat-warn{color:#f44336}.mat-focused .mat-form-field-required-marker{color:#b0bec5}.mat-form-field-ripple{background-color:#fff}.mat-form-field.mat-focused .mat-form-field-ripple{background-color:#c2185b}.mat-form-field.mat-focused .mat-form-field-ripple.mat-accent{background-color:#b0bec5}.mat-form-field.mat-focused .mat-form-field-ripple.mat-warn{background-color:#f44336}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid) .mat-form-field-infix::after{color:#c2185b}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid).mat-accent .mat-form-field-infix::after{color:#b0bec5}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid).mat-warn .mat-form-field-infix::after{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-label{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-label.mat-accent,.mat-form-field.mat-form-field-invalid .mat-form-field-label .mat-form-field-required-marker{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple.mat-accent{background-color:#f44336}.mat-error{color:#f44336}.mat-form-field-appearance-legacy .mat-form-field-label{color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy .mat-hint{color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy .mat-form-field-underline{background-color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-image:linear-gradient(to right, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 33%, transparent 0%);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-standard .mat-form-field-underline{background-color:rgba(255,255,255,.7)}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-image:linear-gradient(to right, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 33%, transparent 0%);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-fill .mat-form-field-flex{background-color:rgba(255,255,255,.1)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{background-color:rgba(255,255,255,.05)}.mat-form-field-appearance-fill .mat-form-field-underline::before{background-color:rgba(255,255,255,.5)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-label{color:rgba(255,255,255,.5)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-underline::before{background-color:rgba(0,0,0,0)}.mat-form-field-appearance-outline .mat-form-field-outline{color:rgba(255,255,255,.3)}.mat-form-field-appearance-outline .mat-form-field-outline-thick{color:#fff}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{color:#c2185b}.mat-form-field-appearance-outline.mat-focused.mat-accent .mat-form-field-outline-thick{color:#b0bec5}.mat-form-field-appearance-outline.mat-focused.mat-warn .mat-form-field-outline-thick{color:#f44336}.mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick{color:#f44336}.mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-label{color:rgba(255,255,255,.5)}.mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:rgba(255,255,255,.15)}.mat-icon.mat-primary{color:#c2185b}.mat-icon.mat-accent{color:#b0bec5}.mat-icon.mat-warn{color:#f44336}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{color:rgba(255,255,255,.7)}.mat-input-element:disabled,.mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after{color:rgba(255,255,255,.5)}.mat-input-element{caret-color:#c2185b}.mat-input-element::placeholder{color:rgba(255,255,255,.5)}.mat-input-element::-moz-placeholder{color:rgba(255,255,255,.5)}.mat-input-element::-webkit-input-placeholder{color:rgba(255,255,255,.5)}.mat-input-element:-ms-input-placeholder{color:rgba(255,255,255,.5)}.mat-input-element:not(.mat-native-select-inline) option{color:rgba(0,0,0,.87)}.mat-input-element:not(.mat-native-select-inline) option:disabled{color:rgba(0,0,0,.38)}.mat-form-field.mat-accent .mat-input-element{caret-color:#b0bec5}.mat-form-field.mat-warn .mat-input-element,.mat-form-field-invalid .mat-input-element{caret-color:#f44336}.mat-form-field-type-mat-native-select.mat-form-field-invalid .mat-form-field-infix::after{color:#f44336}.mat-list-base .mat-list-item{color:#fff}.mat-list-base .mat-list-option{color:#fff}.mat-list-base .mat-subheader{color:rgba(255,255,255,.7)}.mat-list-base .mat-list-item-disabled{background-color:rgba(255,255,255,.12);color:rgba(255,255,255,.5)}.mat-list-option:hover,.mat-list-option:focus,.mat-nav-list .mat-list-item:hover,.mat-nav-list .mat-list-item:focus,.mat-action-list .mat-list-item:hover,.mat-action-list .mat-list-item:focus{background:rgba(255,255,255,.04)}.mat-list-single-selected-option,.mat-list-single-selected-option:hover,.mat-list-single-selected-option:focus{background:rgba(255,255,255,.12)}.mat-menu-panel{background:#424242}.mat-menu-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-menu-item{background:rgba(0,0,0,0);color:#fff}.mat-menu-item[disabled],.mat-menu-item[disabled] .mat-menu-submenu-icon,.mat-menu-item[disabled] .mat-icon-no-color{color:rgba(255,255,255,.5)}.mat-menu-item .mat-icon-no-color,.mat-menu-submenu-icon{color:#fff}.mat-menu-item:hover:not([disabled]),.mat-menu-item.cdk-program-focused:not([disabled]),.mat-menu-item.cdk-keyboard-focused:not([disabled]),.mat-menu-item-highlighted:not([disabled]){background:rgba(255,255,255,.04)}.mat-paginator{background:#424242}.mat-paginator,.mat-paginator-page-size .mat-select-trigger{color:rgba(255,255,255,.7)}.mat-paginator-decrement,.mat-paginator-increment{border-top:2px solid #fff;border-right:2px solid #fff}.mat-paginator-first,.mat-paginator-last{border-top:2px solid #fff}.mat-icon-button[disabled] .mat-paginator-decrement,.mat-icon-button[disabled] .mat-paginator-increment,.mat-icon-button[disabled] .mat-paginator-first,.mat-icon-button[disabled] .mat-paginator-last{border-color:rgba(255,255,255,.5)}.mat-paginator-container{min-height:56px}.mat-progress-bar-background{fill:#552a3b}.mat-progress-bar-buffer{background-color:#552a3b}.mat-progress-bar-fill::after{background-color:#c2185b}.mat-progress-bar.mat-accent .mat-progress-bar-background{fill:#505455}.mat-progress-bar.mat-accent .mat-progress-bar-buffer{background-color:#505455}.mat-progress-bar.mat-accent .mat-progress-bar-fill::after{background-color:#b0bec5}.mat-progress-bar.mat-warn .mat-progress-bar-background{fill:#613532}.mat-progress-bar.mat-warn .mat-progress-bar-buffer{background-color:#613532}.mat-progress-bar.mat-warn .mat-progress-bar-fill::after{background-color:#f44336}.mat-progress-spinner circle,.mat-spinner circle{stroke:#c2185b}.mat-progress-spinner.mat-accent circle,.mat-spinner.mat-accent circle{stroke:#b0bec5}.mat-progress-spinner.mat-warn circle,.mat-spinner.mat-warn circle{stroke:#f44336}.mat-radio-outer-circle{border-color:rgba(255,255,255,.7)}.mat-radio-button.mat-primary.mat-radio-checked .mat-radio-outer-circle{border-color:#c2185b}.mat-radio-button.mat-primary .mat-radio-inner-circle,.mat-radio-button.mat-primary .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-primary.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-primary:active .mat-radio-persistent-ripple{background-color:#c2185b}.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#b0bec5}.mat-radio-button.mat-accent .mat-radio-inner-circle,.mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-accent:active .mat-radio-persistent-ripple{background-color:#b0bec5}.mat-radio-button.mat-warn.mat-radio-checked .mat-radio-outer-circle{border-color:#f44336}.mat-radio-button.mat-warn .mat-radio-inner-circle,.mat-radio-button.mat-warn .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-warn.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-warn:active .mat-radio-persistent-ripple{background-color:#f44336}.mat-radio-button.mat-radio-disabled.mat-radio-checked .mat-radio-outer-circle,.mat-radio-button.mat-radio-disabled .mat-radio-outer-circle{border-color:rgba(255,255,255,.5)}.mat-radio-button.mat-radio-disabled .mat-radio-ripple .mat-ripple-element,.mat-radio-button.mat-radio-disabled .mat-radio-inner-circle{background-color:rgba(255,255,255,.5)}.mat-radio-button.mat-radio-disabled .mat-radio-label-content{color:rgba(255,255,255,.5)}.mat-radio-button .mat-ripple-element{background-color:#fff}.mat-select-value{color:#fff}.mat-select-placeholder{color:rgba(255,255,255,.5)}.mat-select-disabled .mat-select-value{color:rgba(255,255,255,.5)}.mat-select-arrow{color:rgba(255,255,255,.7)}.mat-select-panel{background:#424242}.mat-select-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple){background:rgba(255,255,255,.12)}.mat-form-field.mat-focused.mat-primary .mat-select-arrow{color:#c2185b}.mat-form-field.mat-focused.mat-accent .mat-select-arrow{color:#b0bec5}.mat-form-field.mat-focused.mat-warn .mat-select-arrow{color:#f44336}.mat-form-field .mat-select.mat-select-invalid .mat-select-arrow{color:#f44336}.mat-form-field .mat-select.mat-select-disabled .mat-select-arrow{color:rgba(255,255,255,.5)}.mat-drawer-container{background-color:#303030;color:#fff}.mat-drawer{background-color:#424242;color:#fff}.mat-drawer.mat-drawer-push{background-color:#424242}.mat-drawer:not(.mat-drawer-side){box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-drawer-side{border-right:solid 1px rgba(255,255,255,.12)}.mat-drawer-side.mat-drawer-end{border-left:solid 1px rgba(255,255,255,.12);border-right:none}[dir=rtl] .mat-drawer-side{border-left:solid 1px rgba(255,255,255,.12);border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-left:none;border-right:solid 1px rgba(255,255,255,.12)}.mat-drawer-backdrop.mat-drawer-shown{background-color:rgba(189,189,189,.6)}.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#b0bec5}.mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:rgba(176,190,197,.54)}.mat-slide-toggle.mat-checked .mat-ripple-element{background-color:#b0bec5}.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb{background-color:#c2185b}.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar{background-color:rgba(194,24,91,.54)}.mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element{background-color:#c2185b}.mat-slide-toggle.mat-warn.mat-checked .mat-slide-toggle-thumb{background-color:#f44336}.mat-slide-toggle.mat-warn.mat-checked .mat-slide-toggle-bar{background-color:rgba(244,67,54,.54)}.mat-slide-toggle.mat-warn.mat-checked .mat-ripple-element{background-color:#f44336}.mat-slide-toggle:not(.mat-checked) .mat-ripple-element{background-color:#fff}.mat-slide-toggle-thumb{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);background-color:#bdbdbd}.mat-slide-toggle-bar{background-color:rgba(255,255,255,.5)}.mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-primary .mat-slider-track-fill,.mat-slider.mat-primary .mat-slider-thumb,.mat-slider.mat-primary .mat-slider-thumb-label{background-color:#c2185b}.mat-slider.mat-primary .mat-slider-thumb-label-text{color:#fff}.mat-slider.mat-primary .mat-slider-focus-ring{background-color:rgba(194,24,91,.2)}.mat-slider.mat-accent .mat-slider-track-fill,.mat-slider.mat-accent .mat-slider-thumb,.mat-slider.mat-accent .mat-slider-thumb-label{background-color:#b0bec5}.mat-slider.mat-accent .mat-slider-thumb-label-text{color:rgba(0,0,0,.87)}.mat-slider.mat-accent .mat-slider-focus-ring{background-color:rgba(176,190,197,.2)}.mat-slider.mat-warn .mat-slider-track-fill,.mat-slider.mat-warn .mat-slider-thumb,.mat-slider.mat-warn .mat-slider-thumb-label{background-color:#f44336}.mat-slider.mat-warn .mat-slider-thumb-label-text{color:#fff}.mat-slider.mat-warn .mat-slider-focus-ring{background-color:rgba(244,67,54,.2)}.mat-slider:hover .mat-slider-track-background,.mat-slider.cdk-focused .mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-disabled .mat-slider-track-background,.mat-slider.mat-slider-disabled .mat-slider-track-fill,.mat-slider.mat-slider-disabled .mat-slider-thumb{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-disabled:hover .mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value .mat-slider-focus-ring{background-color:rgba(255,255,255,.12)}.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing .mat-slider-thumb,.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing .mat-slider-thumb-label{background-color:#fff}.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused .mat-slider-thumb,.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused .mat-slider-thumb-label{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing) .mat-slider-thumb{border-color:rgba(255,255,255,.3);background-color:rgba(0,0,0,0)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing):hover .mat-slider-thumb,.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing).cdk-focused .mat-slider-thumb{border-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing):hover.mat-slider-disabled .mat-slider-thumb,.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing).cdk-focused.mat-slider-disabled .mat-slider-thumb{border-color:rgba(255,255,255,.3)}.mat-slider-has-ticks .mat-slider-wrapper::after{border-color:rgba(255,255,255,.7)}.mat-slider-horizontal .mat-slider-ticks{background-image:repeating-linear-gradient(to right, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent);background-image:-moz-repeating-linear-gradient(0.0001deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent)}.mat-slider-vertical .mat-slider-ticks{background-image:repeating-linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent)}.mat-step-header.cdk-keyboard-focused,.mat-step-header.cdk-program-focused,.mat-step-header:hover:not([aria-disabled]),.mat-step-header:hover[aria-disabled=false]{background-color:rgba(255,255,255,.04)}.mat-step-header:hover[aria-disabled=true]{cursor:default}@media(hover: none){.mat-step-header:hover{background:none}}.mat-step-header .mat-step-label,.mat-step-header .mat-step-optional{color:rgba(255,255,255,.7)}.mat-step-header .mat-step-icon{background-color:rgba(255,255,255,.7);color:#fff}.mat-step-header .mat-step-icon-selected,.mat-step-header .mat-step-icon-state-done,.mat-step-header .mat-step-icon-state-edit{background-color:#c2185b;color:#fff}.mat-step-header.mat-accent .mat-step-icon{color:rgba(0,0,0,.87)}.mat-step-header.mat-accent .mat-step-icon-selected,.mat-step-header.mat-accent .mat-step-icon-state-done,.mat-step-header.mat-accent .mat-step-icon-state-edit{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-step-header.mat-warn .mat-step-icon{color:#fff}.mat-step-header.mat-warn .mat-step-icon-selected,.mat-step-header.mat-warn .mat-step-icon-state-done,.mat-step-header.mat-warn .mat-step-icon-state-edit{background-color:#f44336;color:#fff}.mat-step-header .mat-step-icon-state-error{background-color:rgba(0,0,0,0);color:#f44336}.mat-step-header .mat-step-label.mat-step-label-active{color:#fff}.mat-step-header .mat-step-label.mat-step-label-error{color:#f44336}.mat-stepper-horizontal,.mat-stepper-vertical{background-color:#424242}.mat-stepper-vertical-line::before{border-left-color:rgba(255,255,255,.12)}.mat-horizontal-stepper-header::before,.mat-horizontal-stepper-header::after,.mat-stepper-horizontal-line{border-top-color:rgba(255,255,255,.12)}.mat-horizontal-stepper-header{height:72px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header,.mat-vertical-stepper-header{padding:24px 24px}.mat-stepper-vertical-line::before{top:-16px;bottom:-16px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::after,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::before{top:36px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{top:36px}.mat-sort-header-arrow{color:#c6c6c6}.mat-tab-nav-bar,.mat-tab-header{border-bottom:1px solid rgba(255,255,255,.12)}.mat-tab-group-inverted-header .mat-tab-nav-bar,.mat-tab-group-inverted-header .mat-tab-header{border-top:1px solid rgba(255,255,255,.12);border-bottom:none}.mat-tab-label,.mat-tab-link{color:#fff}.mat-tab-label.mat-tab-disabled,.mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.5)}.mat-tab-header-pagination-chevron{border-color:#fff}.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:rgba(255,255,255,.5)}.mat-tab-group[class*=mat-background-]>.mat-tab-header,.mat-tab-nav-bar[class*=mat-background-]{border-bottom:none;border-top:none}.mat-tab-group.mat-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(233,30,99,.3)}.mat-tab-group.mat-primary .mat-ink-bar,.mat-tab-nav-bar.mat-primary .mat-ink-bar{background-color:#c2185b}.mat-tab-group.mat-primary.mat-background-primary>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-primary.mat-background-primary>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-primary.mat-background-primary>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-primary.mat-background-primary>.mat-tab-link-container .mat-ink-bar{background-color:#fff}.mat-tab-group.mat-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(207,216,220,.3)}.mat-tab-group.mat-accent .mat-ink-bar,.mat-tab-nav-bar.mat-accent .mat-ink-bar{background-color:#b0bec5}.mat-tab-group.mat-accent.mat-background-accent>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-accent.mat-background-accent>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-accent.mat-background-accent>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-accent.mat-background-accent>.mat-tab-link-container .mat-ink-bar{background-color:rgba(0,0,0,.87)}.mat-tab-group.mat-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-warn .mat-ink-bar,.mat-tab-nav-bar.mat-warn .mat-ink-bar{background-color:#f44336}.mat-tab-group.mat-warn.mat-background-warn>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-warn.mat-background-warn>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-warn.mat-background-warn>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-warn.mat-background-warn>.mat-tab-link-container .mat-ink-bar{background-color:#fff}.mat-tab-group.mat-background-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(233,30,99,.3)}.mat-tab-group.mat-background-primary>.mat-tab-header,.mat-tab-group.mat-background-primary>.mat-tab-link-container,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination{background-color:#c2185b}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-tab-link{color:#fff}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.4)}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-primary>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-focus-indicator::before{border-color:#fff}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#fff;opacity:.4}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination .mat-ripple-element{background-color:#fff;opacity:.12}.mat-tab-group.mat-background-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(207,216,220,.3)}.mat-tab-group.mat-background-accent>.mat-tab-header,.mat-tab-group.mat-background-accent>.mat-tab-link-container,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination{background-color:#b0bec5}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-tab-link{color:rgba(0,0,0,.87)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(0,0,0,.4)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-accent>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-focus-indicator::before{border-color:rgba(0,0,0,.87)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#000;opacity:.4}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination .mat-ripple-element{background-color:#000;opacity:.12}.mat-tab-group.mat-background-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-background-warn>.mat-tab-header,.mat-tab-group.mat-background-warn>.mat-tab-link-container,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination{background-color:#f44336}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-tab-link{color:#fff}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.4)}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-warn>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-focus-indicator::before{border-color:#fff}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#fff;opacity:.4}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination .mat-ripple-element{background-color:#fff;opacity:.12}.mat-toolbar{background:#212121;color:#fff}.mat-toolbar.mat-primary{background:#c2185b;color:#fff}.mat-toolbar.mat-accent{background:#b0bec5;color:rgba(0,0,0,.87)}.mat-toolbar.mat-warn{background:#f44336;color:#fff}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar-multiple-rows{min-height:64px}.mat-toolbar-row,.mat-toolbar-single-row{height:64px}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:56px}.mat-toolbar-row,.mat-toolbar-single-row{height:56px}}.mat-tooltip{background:rgba(97,97,97,.9)}.mat-tree{background:#424242}.mat-tree-node,.mat-nested-tree-node{color:#fff}.mat-tree-node{min-height:48px}.mat-snack-bar-container{color:rgba(0,0,0,.87);background:#fafafa;box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-simple-snackbar-action{color:inherit}html {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  overscroll-behavior: none;
}*,
*:before,
*:after {
  box-sizing: inherit;
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  margin: 0;
  padding: 0;
  -webkit-backface-visibility: hidden;
  -webkit-touch-collout: none;
  -webkit-user-select: none;
  user-select: none;
}html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}body {
  line-height: 1;
  font-style: normal;
}ol,
ul {
  list-style: none;
}blockquote,
q {
  quotes: none;
}blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}table {
  border-collapse: collapse;
  border-spacing: 0;
}td,
th {
  padding: 0;
}input {
  outline: none;
}input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
}button,
html input[type=button],
input[type=reset],
input[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
  outline: none;
}button[disabled],
html input[disabled] {
  cursor: default;
}button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}input {
  line-height: normal;
}input[type=search] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}a {
  text-decoration: none;
}a:active, a:hover, a:focus {
  outline: 0;
}i {
  font-style: italic;
}b,
strong {
  font-weight: 700;
}.cdk-dialog-container:focus {
  outline: none;
}:root {
  --red-600: rgba(255, 103, 103, 0.75);
  --red-500: #ff6767;
  --red-100: #ffcbcb;
  --blue-900: #0c0c3a;
  --blue-800: #0c1243;
  --blue-700: #0f2055;
  --blue-500: #11316b;
  --blue-450: #144182;
  --blue-400: rgba(31, 143, 235, 0.3);
  --blue-300: rgba(31, 143, 235, 0.15);
  --orange-500: #ff6f00;
  --gray-900: rgba(255, 255, 255, 0.1);
  --gray-800: rgba(255, 255, 255, 0.2);
  --gray-700: rgba(255, 255, 255, 0.3);
  --gray-600: rgba(255, 255, 255, 0.4);
  --gray-500: rgba(255, 255, 255, 0.5);
  --gray-400: rgba(255, 255, 255, 0.75);
  --aqua-500: #16d1d6;
  --azure-600: #1c72b9;
  --azure-500: #1f8feb;
  --white-500: #ffffff;
  --black-300: rgba(0, 0, 0, 0.6);
  --amethyst-500: #9a69f7;
  --silver-500: #8898b5;
  --gradietAquaToBlue: radial-gradient(100% 188.88% at 0% 0%, #16d1d6 0%, #274cff 100%);
  --gradietLightAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, rgba(163, 102, 255, 0.5) 0%, rgba(96, 31, 255, 0.5) 100%);
  --gradietAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, #a366ff 0%, #601fff 100%);
  --shadow-gray: 0px 2.11765px 5.64706px rgba(0, 0, 0, 0.15), 0px 2.11765px 0.705882px rgba(0, 0, 0, 0.06);
  --shadow-black-300: 0 0 1rem var(--black-300);
  --chartOptionsBackgroundColor: #2b3644;
  --chartOptionsHoverColor: #556576;
}:root {
  --border-radius: 0.8rem;
}.light {
  --main-background: #f0f6fb;
  --page-content-background: #fcfcfc;
  --sidebar-background: #fcfcfc;
  --tab-content-background: #fcfcfc;
  --tab-preloader-background: #fcfcfc;
  --tab-preloader-text: var(--blue-900);
  --tab-header-background: #d9ebfa;
  --tab-header-active-background: #fcfcfc;
  --main-text: var(--blue-900);
  --button-color: var(--blue-900);
  --synchronization-status-color: var(--blue-900);
  --synchronization-progress-bar-container-background: rgba(0, 0, 0, 0.1);
  --dialog-background: #ffffff;
  --block-sync: #505274;
  --control-alias-wallet-background: #1f8feb1a;
  --control-alias-wallet-text: var(--main-text);
  --alias-history-count-text: #1f8feb;
  --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);
  --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);
  --auth-card-background: #fcfcfc;
  --auth-card-form-background: #ffffff;
  --auth-card-form-border: 1px solid #1f8feb33;
  --form-card-background: #f0f6fb;
  --ng-select-bg: #dbecfa;
  --ng-select-border: #1f8feb40;
  --ng-select-highlight: #1f8feb10;
  --ng-select-circle-border: 1px solid #1f8feb50;
  --ng-select-circle-background: #1f8feb;
  --input-background: #ffffff;
  --input-color: var(--blue-900);
  --input-placeholder: #0c0c3a4d;
  --border: 1px solid #1f8feb66;
  --border-not-empty: 1px solid #1f8feb99;
  --border-disabled: 1px solid #1f8feb66;
  --border-error: 1px solid var(--red-600);
  --hint-text: #0c0c3a99;
  --amount-ticker-text: #0c0c3a99;
  --amount-btn-revers-background: #1f8feb1a;
  --checkbox-border: 1px solid #1f8feb66;
  --checkbox-hover-border: 1px solid #1f8feb99;
  --checkbox-active-border: 1px solid #1f8feb99;
  --checkbox-checked-background: #1f8feb;
  --switch-on-background: var(--aqua-500);
  --switch-off-background: #0c0c3a1a;
  --switch-circle-background: var(--white-500);
  --wallet-background: #1f8feb26;
  --wallet-active-background: var(--gradietAquaToBlue);
  --wallet-border: 1px solid #1f8feb00;
  --wallet-border-color-hover: #1f8feb;
  --wallet-auditable-watch-only-background: radial-gradient(
      100% 246.57% at 0% 0%,
      rgba(163, 102, 255, 0.75) 0%,
      rgba(96, 31, 255, 0.75) 100%
  );
  --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);
  --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);
  --wallet-watch-only-after-background: #dbecf9;
  --wallet-watch-only-text: var(--blue-900);
  --wallet-active-text: #ffffff;
  --wallet-text: var(--blue-900);
  --table-thead-bg: #1f8feb4d;
  --table-row-bg: #dbecf9;
  --table-rounded-corners-border: 1px solid #d9dfe8;
  --table-info-border: 1px solid #d9dfe8;
  --table-info-label-background: #f0f6fb;
  --btn-icon-background: #1f8feb1a;
  --btn-icon-hover-background: #1f8feb4d;
  --list-background: #f9fcff;
  --list-border: 1px solid #1f8feb80;
  --list-item-hover-background: #1f8feb1a;
  --tooltip-background: #8dbee8;
  --details-background: #dbecfa;
}.dark {
  --main-background: var(--blue-900);
  --page-content-background: var(--blue-700);
  --sidebar-background: var(--blue-700);
  --tab-content-background: var(--blue-700);
  --tab-preloader-background: var(--blue-700);
  --tab-preloader-text: #ffffff;
  --tab-header-background: var(--blue-800);
  --tab-header-active-background: var(--blue-700);
  --main-text: var(--white-500);
  --button-color: var(--white-500);
  --synchronization-status-color: var(--white-500);
  --synchronization-progress-bar-container-background: var(--gray-900);
  --dialog-background: var(--blue-700);
  --block-sync: #a8abb5;
  --control-alias-wallet-background: #ffffff1a;
  --control-alias-wallet-text: var(--main-text);
  --alias-history-count-text: #1f8feb;
  --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);
  --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);
  --auth-card-background: var(--blue-700);
  --auth-card-form-background: var(--blue-500);
  --auth-card-form-border: 1px solid transparent;
  --form-card-background: var(--blue-500);
  --ng-select-bg: var(--blue-500);
  --ng-select-border: var(--gray-500);
  --ng-select-highlight: var(--gray-900);
  --ng-select-circle-border: 1px solid white;
  --ng-select-circle-background: var(--white-500);
  --input-background: transparent;
  --input-color: var(--white-500);
  --input-placeholder: var(--gray-800);
  --border: 1px solid var(--gray-800);
  --border-not-empty: 1px solid var(--gray-500);
  --border-disabled: 1px solid var(--gray-800);
  --border-error: 1px solid var(--red-600);
  --hint-text: #ffffff60;
  --amount-ticker-text: #ffffff60;
  --amount-btn-revers-background: #ffffff1a;
  --checkbox-border: 1px solid #ffffff33;
  --checkbox-hover-border: 1px solid #ffffff66;
  --checkbox-active-border: 1px solid #ffffff66;
  --checkbox-checked-background: #ffffff;
  --switch-on-background: var(--aqua-500);
  --switch-off-background: var(--gray-800);
  --switch-circle-background: var(--white-500);
  --wallet-background: var(--blue-500);
  --wallet-active-background: var(--gradietAquaToBlue);
  --wallet-border: 1px solid var(--blue-500);
  --wallet-border-color-hover: var(--gray-800);
  --wallet-auditable-watch-only-background: var(--gradietLightAmethystToPurpurle);
  --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);
  --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);
  --wallet-watch-only-after-background: var(--blue-500);
  --wallet-watch-only-text: #ffffff;
  --wallet-active-text: #ffffff;
  --wallet-text: #ffffff;
  --table-thead-bg: var(--blue-400);
  --table-row-bg: var(--blue-300);
  --table-rounded-corners-border: 1px solid #33426e;
  --table-info-border: 1px solid #33426e;
  --table-info-label-background: var(--blue-500);
  --btn-icon-background: var(--gray-900);
  --btn-icon-hover-background: var(--gray-700);
  --list-background: var(--blue-500);
  --list-border: var(--border);
  --list-item-hover-background: var(--gray-900);
  --tooltip-background: var(--blue-450);
  --details-background: var(--blue-500);
}@font-face {
  font-family: SF-Pro-Rounded;
  src: url('SF-Pro-Rounded-Regular.ttf');
  font-weight: 400;
}@font-face {
  font-family: SF-Pro-Rounded;
  src: url('SF-Pro-Rounded-Medium.ttf');
  font-weight: 500;
}@font-face {
  font-family: SF-Pro-Rounded;
  src: url('SF-Pro-Rounded-Semibold.ttf');
  font-weight: 600;
}html,
input,
textarea,
select,
button {
  color: var(--main-text);
  font-family: SF-Pro-Rounded, sans-serif;
  font-weight: 400;
}h1 {
  font-size: 3.6rem;
  line-height: 1.2;
}h2 {
  font-size: 2.8rem;
  line-height: 1.2;
}h3 {
  font-size: 2rem;
  line-height: 1.2;
}div,
span,
applet,
object,
iframe,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  font-size: 1.8rem;
  line-height: 1.2;
}::-webkit-scrollbar {
  background-color: transparent;
  cursor: default;
  width: 1rem;
  height: 1rem;
}::-webkit-scrollbar-track {
  background: transparent;
}::-webkit-scrollbar-thumb {
  background-color: var(--silver-500);
  background-clip: padding-box;
  border: 0.2rem solid transparent;
  border-radius: 1rem;
}::-webkit-scrollbar-thumb:hover {
  background-color: var(--silver-500);
}body::-webkit-scrollbar-corner {
  background-color: var(--blue-900);
}.scrolled-content {
  overflow-y: scroll;
  overflow-x: hidden;
  height: auto;
  margin-right: -2rem;
}.scrolled-content::-webkit-scrollbar {
  width: 2rem;
}.scrolled-content::-webkit-scrollbar-thumb {
  border: 0.8rem solid transparent;
}.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}html,
body {
  position: fixed;
  overflow: hidden;
  overscroll-behavior: none;
  width: 100%;
  min-width: 1200px;
  max-width: 100vw;
  height: 100vh;
  min-height: 700px;
}body {
  background: var(--main-background);
  color: var(--main-text);
}app-root {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
}.page-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}.page-container .toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;
  flex: 0 0 auto;
}.page-container .toolbar .left,
.page-container .toolbar .right {
  display: flex;
  align-items: center;
}.page-container .page-content {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  flex: auto;
  overflow: hidden;
  padding: 2rem;
  border-radius: 0.8rem;
  background-color: var(--page-content-background);
}.alias {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}.control-alias-wallet-container {
  position: relative;
  padding: 0.4rem 0.6rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  cursor: pointer;
}.control-alias-wallet-container .alias {
  border-radius: 0.8rem;
  padding: 7px 12px 7px;
  background: var(--control-alias-wallet-background);
  color: var(--control-alias-wallet-text);
  font-size: 1.8rem;
  display: inline-flex;
  align-items: center;
}.control-alias-wallet-container .alias mat-icon {
  margin-left: 0.6rem;
}.control-alias-wallet-container .alias-history-count {
  margin-left: 1rem;
  font-size: 1.8rem;
  color: var(--control-alias-wallet-text);
  opacity: 0.7;
}.control-alias-wallet-container.available:after {
  display: block;
  content: "";
  width: 1.4rem;
  height: 1.4rem;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
  background-image: url('crown.svg');
  background-repeat: no-repeat;
  background-size: contain;
}.alias-container {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}.alias-container.available {
  padding: 0.4rem 0.6rem;
}.alias-container.available .alias {
  padding: 0.4rem 1.6rem;
  min-height: 3.2rem;
  background: var(--gradietAquaToBlue);
  color: #ffffff;
  border-radius: 0.8rem;
  position: relative;
}.alias-container.available:after {
  display: block;
  content: "";
  width: 1.4rem;
  height: 1.4rem;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
  background-image: url('crown.svg');
  background-repeat: no-repeat;
  background-size: contain;
}.alias-history-count {
  margin-left: 1rem;
  color: var(--alias-history-count-text);
  font-size: 1.8rem;
}.alias-history-list {
  max-height: 8rem;
  overflow: auto;
}button,
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--button-color);
  transition: all 0.25s ease;
  border: none;
  outline: none;
  background-color: transparent;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 0.8rem;
  font-size: 1.8rem;
}button:not(:disabled),
.btn:not(:disabled) {
  cursor: pointer;
}button:not(:disabled):hover,
.btn:not(:disabled):hover {
  cursor: pointer;
}button:disabled,
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}button.primary, button.outline,
.btn.primary,
.btn.outline {
  width: 100%;
  padding: 0 2rem 0 2rem;
}button.primary.small, button.outline.small,
.btn.primary.small,
.btn.outline.small {
  min-height: 4.5rem;
}button.primary.big, button.outline.big,
.btn.primary.big,
.btn.outline.big {
  min-height: 5.3rem;
}button.primary,
.btn.primary {
  background-color: var(--azure-500);
  color: var(--white-500);
}button.primary:not(:disabled):focus, button.primary:not(:disabled):hover, button.primary:not(:disabled).active,
.btn.primary:not(:disabled):focus,
.btn.primary:not(:disabled):hover,
.btn.primary:not(:disabled).active {
  background-color: var(--azure-600);
}button.outline,
.btn.outline {
  border: 1px solid var(--azure-500);
}button.outline:not(:disabled):hover, button.outline:not(:disabled):focus, button.outline:not(:disabled).active,
.btn.outline:not(:disabled):hover,
.btn.outline:not(:disabled):focus,
.btn.outline:not(:disabled).active {
  background-color: var(--gray-900);
}button.btn-icon,
.btn.btn-icon {
  min-width: 2rem;
  min-height: 2rem;
}button.btn-icon.small,
.btn.btn-icon.small {
  min-width: 2.8rem;
  min-height: 2.8rem;
}button.btn-icon.big,
.btn.btn-icon.big {
  min-width: 4rem;
  min-height: 4rem;
}button.btn-icon,
.btn.btn-icon {
  background-color: var(--btn-icon-background);
  transition: background-color 0.2s ease-in-out;
}button.btn-icon.circle,
.btn.btn-icon.circle {
  border-radius: 50%;
}button.btn-icon:hover, button.btn-icon:focus,
.btn.btn-icon:hover,
.btn.btn-icon:focus {
  background-color: var(--btn-icon-hover-background);
}.btn-light-background {
  background-color: var(--btn-icon-background);
}.btn-light-background:not(:disabled):hover {
  background-color: var(--btn-icon-hover-background);
}.light button.outline:not(:disabled):hover, .light button.outline:not(:disabled):focus, .light button.outline:not(:disabled).active,
.light .btn.outline:not(:disabled):hover,
.light .btn.outline:not(:disabled):focus,
.light .btn.outline:not(:disabled).active {
  background-color: rgba(0, 0, 0, 0.05);
}.light button.btn-icon.row-options,
.light .btn.btn-icon.row-options {
  color: #1f8feb;
}.ngx-contextmenu--dropdown-menu {
  border: none;
  padding: 0;
  background-color: var(--chartOptionsBackgroundColor);
  box-shadow: var(--shadow-black-300);
}.ngx-contextmenu li {
  display: block;
  font-size: 1.3rem;
  text-transform: uppercase;
  text-align: center;
}.ngx-contextmenu button {
  display: block;
  padding: 0.5em 1em;
  color: var(--white-500);
  border-radius: 0;
  width: 100%;
}.ngx-contextmenu button:hover {
  background-color: var(--chartOptionsHoverColor);
  color: var(--white-500);
}.dropdown {
  position: relative;
}.dropdown .content-bottom-right {
  position: absolute;
  top: 5rem;
  right: 0;
  width: 19rem;
  z-index: 99;
}.dropdown .item {
  height: 3.9rem;
  display: flex;
  align-items: center;
  padding: 0.5rem;
}.dropdown .item:hover {
  background-color: var(--gray-900);
  cursor: pointer;
}.dropdown .item .alias {
  width: 100%;
  margin-right: 0.3rem;
  padding: 0.4rem 1rem;
}.list {
  border-radius: 0.8rem;
  background-color: var(--list-background);
  border: var(--list-border);
}.list .item:hover,
.list .item .active {
  background-color: var(--list-item-hover-background);
}.list .item button {
  display: inline-flex;
  white-space: nowrap;
  justify-content: flex-start;
  border-radius: 0;
}.list .item button span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}.zano-autocomplete-panel {
  border-radius: 0.8rem !important;
  background-color: var(--list-background);
  border: var(--list-border);
}.zano-autocomplete-panel .mat-option {
  height: 4rem;
  line-height: 1;
  padding: 0;
  cursor: pointer;
  color: var(--main-text);
}.zano-autocomplete-panel .mat-option:hover {
  background-color: var(--list-item-hover-background);
}.zano-autocomplete-panel .mat-option-text {
  padding: 0.4rem;
}.form {
  max-width: 50rem;
  width: 100%;
  border-radius: 0.8rem;
}.form .error {
  color: var(--red-500);
}.form__card {
  display: flex;
  flex-direction: column;
  padding: 2rem 2rem 0;
  margin-bottom: 2rem;
  border-radius: 8px;
  background-color: var(--form-card-background);
}.form__row {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(2, 1fr);
  justify-content: space-between;
  grid-gap: 20px;
}.form__field {
  position: relative;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  align-items: flex-start;
  padding-bottom: 2rem;
  width: 100%;
}.form__field--input {
  /* If input is not empty */
  /* If input is empty */
}.form__field--input:not(:placeholder-shown):not(.invalid):not(.ng-invalid) {
  /* You need to add a placeholder to your fields. For example: <input "placeholder=" "/> */
  border: var(--border-not-empty);
}.form__field--input:not(:placeholder-shown).invalid, .form__field--input:not(:placeholder-shown).ng-touched.ng-invalid {
  border: var(--border-error);
}.form__field--input:placeholder-shown {
  border: var(--border);
}.form__field label,
.form__field .label {
  margin-bottom: 0.8rem;
  color: var(--azure-500);
}.form__field--row {
  display: flex;
  flex: 0 0 auto;
}.form__field--row > div,
.form__field--row > fieldset {
  max-width: calc(50% - 1rem);
  width: 100%;
}.form__field--row > div:first-child,
.form__field--row > fieldset:first-child {
  margin-right: 1rem;
}.form__field--row > div:last-child,
.form__field--row > fieldset:last-child {
  margin-left: 1rem;
}.form__field--input, .form__field--select {
  border: var(--border);
  border-radius: 0.8rem;
  outline: none;
  padding: 0 1.2rem;
  width: 100%;
  height: 4rem;
  background-color: var(--input-background);
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--input-color);
  font-size: 1.8rem;
  line-height: 1.2;
  transition: border 0.2s ease-in-out;
}.form__field--input:disabled, .form__field--select:disabled {
  border: var(--border-disabled);
  cursor: not-allowed;
}.form__field--input:read-only, .form__field--select:read-only {
  cursor: default;
}.form__field--input:not(:disabled):not(:read-only):hover, .form__field--select:not(:disabled):not(:read-only):hover {
  cursor: pointer;
}.form__field--input::placeholder, .form__field--select::placeholder {
  color: var(--input-placeholder);
}.form__field--input.invalid, .form__field--input.ng-touched.ng-invalid, .form__field--select.invalid, .form__field--select.ng-touched.ng-invalid {
  border: var(--border-error);
}.form__field--input.invalid::placeholder, .form__field--input.ng-touched.ng-invalid::placeholder, .form__field--select.invalid::placeholder, .form__field--select.ng-touched.ng-invalid::placeholder {
  color: var(--red-500);
}.form__field--input:not(:disabled):not(:read-only):placeholder-shown:focus, .form__field--select:not(:disabled):not(:read-only):placeholder-shown:focus {
  border: var(--border-not-empty);
}.form__field--input:not(:disabled):not(:read-only):hover, .form__field--select:not(:disabled):not(:read-only):hover {
  border: var(--border-not-empty);
}.form__field--input:not(:disabled):not(:read-only):hover.invalid, .form__field--input:not(:disabled):not(:read-only):hover.ng-touched.ng-invalid, .form__field--select:not(:disabled):not(:read-only):hover.invalid, .form__field--select:not(:disabled):not(:read-only):hover.ng-touched.ng-invalid {
  border: var(--border-error);
}.form__field--input:not(:disabled):not(:read-only):hover.invalid::placeholder, .form__field--input:not(:disabled):not(:read-only):hover.ng-touched.ng-invalid::placeholder, .form__field--select:not(:disabled):not(:read-only):hover.invalid::placeholder, .form__field--select:not(:disabled):not(:read-only):hover.ng-touched.ng-invalid::placeholder {
  color: var(--red-500);
}.form__field.textarea {
  width: 100%;
  height: auto;
}.form__field.textarea textarea {
  border: var(--border);
  border-radius: 0.8rem;
  outline: none;
  padding: 1rem;
  width: 100%;
  min-width: 100%;
  height: 100%;
  min-height: 7.5rem;
  max-height: 7.5rem;
  overflow: auto;
  resize: none;
  background-color: transparent;
  color: var(--input-color);
  font-size: 1.8rem;
  line-height: 1.2;
  /* If input is not empty */
  /* If input is empty */
}.form__field.textarea textarea:disabled {
  border: var(--border-disabled);
  cursor: not-allowed;
}.form__field.textarea textarea:not(:disabled):hover {
  cursor: pointer;
}.form__field.textarea textarea::placeholder {
  color: var(--gray-800);
}.form__field.textarea textarea:not(:placeholder-shown) {
  /* You need to add a placeholder to your fields. For example: <input "placeholder=" "/> */
  border: var(--border-not-empty);
}.form__field.textarea textarea:placeholder-shown {
  border: var(--border);
}.form__field.textarea textarea .ng-touched .ng-invalid {
  border: var(--border-error);
}.form__field.textarea textarea.invalid {
  border: var(--border-error);
}.form__field.textarea textarea.invalid::placeholder {
  color: var(--red-500);
}.form__field .error,
.form__field .success,
.form__field .info {
  overflow: hidden;
  width: 100%;
  font-size: 1.6rem;
  margin-top: 1rem;
}.form__field .hint {
  margin-top: 5px;
  color: var(--hint-text);
  font-size: 1.4rem;
}.form__field.fixed {
  padding-bottom: 2.6rem;
}.form__field.fixed .error,
.form__field.fixed .success,
.form__field.fixed .info {
  margin-top: 0;
  position: absolute;
  bottom: 0.2rem;
}.form__field.fixed .error > *,
.form__field.fixed .success > *,
.form__field.fixed .info > * {
  font-size: 1.6rem;
}.form__field.fixed .hint {
  margin-top: 0;
  position: absolute;
  bottom: 0.2rem;
}.form__field .error {
  color: var(--red-500);
}.form__field .success {
  color: var(--aqua-500);
}.form__field-dropdown {
  position: relative;
}.form__field-dropdown .dropdown {
  overflow-y: auto;
  position: absolute;
  top: calc(100% + 1rem);
  left: 0;
  max-width: 100%;
  width: 100%;
  max-height: 15rem;
  border: var(--border);
}.form .details .header {
  padding: 1.2rem 2rem;
  width: 100%;
  max-width: 20rem;
  background-color: var(--details-background);
  border-radius: 0.8rem 0.8rem 0 0;
}.form .details .header.border-radius-all {
  border-radius: 0.8rem;
}.form .details .content {
  display: flex;
  flex-direction: column;
  padding: 2rem;
  background-color: var(--details-background);
  border-radius: 0 0.8rem 0.8rem 0.8rem;
}.checkbox {
  display: flex;
  align-items: center;
  min-height: 2.4rem;
  overflow: hidden;
  position: relative;
}.checkbox span {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  min-height: 2.4rem;
  line-height: 2.4rem;
  padding-left: 3.6rem;
  -webkit-user-select: none;
  user-select: none;
}.checkbox input[readonly] + span {
  pointer-events: none;
}.checkbox input[readonly] + span:before {
  pointer-events: none;
}.checkbox input[type=checkbox] {
  position: absolute;
  top: 0;
  left: 0;
  visibility: visible;
  width: 2.4rem;
  height: 2.4rem;
  opacity: 0;
  overflow: hidden;
}.checkbox input[type=checkbox] + span {
  position: relative;
}.checkbox input[type=checkbox] + span:before, .checkbox input[type=checkbox] + span:after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease-in-out;
}.checkbox input[type=checkbox] + span:before {
  left: 0;
  width: 2.4rem;
  height: 2.4rem;
  border: var(--checkbox-border);
  border-radius: 0.4rem;
}.checkbox input[type=checkbox]:not(:disabled) + span:hover:before {
  border: var(--checkbox-hover-border);
}.checkbox input[type=checkbox]:focus + span:before {
  border: var(--checkbox-active-border);
}.checkbox input[type=checkbox]:checked + span:before, .checkbox input[type=checkbox]:checked + span:hover:before {
  border: var(--checkbox-active-border);
}.checkbox input[type=checkbox]:checked + span:after {
  left: 0.4rem;
  width: 1.6rem;
  height: 1.6rem;
  border-radius: 0.2rem;
  background: var(--checkbox-checked-background);
}.checkbox input[type=checkbox]:disabled + span {
  cursor: not-allowed;
}.checkbox input[type=checkbox]:disabled + span:before {
  cursor: not-allowed;
}.switch {
  display: flex;
  align-items: center;
  border-radius: 1.1rem;
  cursor: pointer;
  padding: 0.2rem;
  width: 3.6rem;
  height: 2.2rem;
  transition: all 0.2s ease-in-out;
  outline: none;
}.switch.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}.switch.on {
  justify-content: flex-end;
  background-color: var(--switch-on-background);
}.switch.off {
  justify-content: flex-start;
  background-color: var(--switch-off-background);
}.switch .circle {
  border-radius: 50%;
  width: 1.8rem;
  height: 1.8rem;
  background-color: var(--switch-circle-background);
  box-shadow: var(--shadow-gray);
}.amount .form__field--input {
  padding-right: 10.8rem;
}.amount .ticker {
  position: absolute;
  top: 3.1rem;
  right: 4.8rem;
  width: 5rem;
  height: 3.6rem;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--amount-ticker-text);
}.amount .btn-reverse {
  width: 3.8rem;
  height: 3.6rem;
  position: absolute;
  border-radius: 0 0.4rem 0.4rem 0;
  right: 0.18rem;
  top: 3.1rem;
  display: flex;
  justify-items: center;
  align-items: center;
  background: var(--amount-btn-revers-background);
}.XSmall .form__field--row,
.Small .form__field--row {
  flex-direction: column;
}.XSmall .form__field--row > div,
.XSmall .form__field--row > fieldset,
.Small .form__field--row > div,
.Small .form__field--row > fieldset {
  max-width: 100%;
}.XSmall .form__field--row > div:first-child,
.XSmall .form__field--row > fieldset:first-child,
.Small .form__field--row > div:first-child,
.Small .form__field--row > fieldset:first-child {
  margin-right: 0;
}.XSmall .form__field--row > div:last-child,
.XSmall .form__field--row > fieldset:last-child,
.Small .form__field--row > div:last-child,
.Small .form__field--row > fieldset:last-child {
  margin-left: 0;
}body .mat-icon {
  width: 1.8rem;
  min-width: 1.8rem;
  height: 1.8rem;
  font-size: 1.8rem;
}body .mat-icon.small {
  width: 1.6rem;
  min-width: 1.6rem;
  height: 1.6rem;
  font-size: 1.6rem;
}i {
  display: inline-block;
}i svg {
  width: 100%;
  height: 100%;
}.icon {
  display: inline-flex;
  min-width: 1.8rem;
  min-height: 1.8rem;
  transition: all 0.25s ease;
}.icon.small {
  min-width: 1.4rem;
  min-height: 1.4rem;
}.icon.question-circle {
  background: center/contain no-repeat url('question-circle.svg');
}.icon.info-circle {
  background: center/contain no-repeat url('info-circle.svg');
}.icon.purchase-arrow-down {
  background: center/contain no-repeat url('purchase-arrow-down.svg');
}.icon.purchase-arrow-up {
  background: center/contain no-repeat url('purchase-arrow-up.svg');
}.icon.custom-asset {
  background: center/contain no-repeat url('custom-asset_icon.svg');
}.icon.show-balance {
  background: center/contain no-repeat url('show-balance_icon.svg');
}.icon.hide-balance {
  background: center/contain no-repeat url('hide-balance_ico.svg');
}.icon.emit {
  background: center/contain no-repeat url('emit_icon.svg');
}.icon.arrow-down-square {
  background: center/contain no-repeat url('arrow-down-square.svg');
}.icon.swap {
  background: center/contain no-repeat url('swap_icon.svg');
}.icon.add {
  background: center/contain no-repeat url('add.svg');
}.icon.regenerate {
  background: center/contain no-repeat url('regenerate.svg');
}.icon.balance-icon {
  background: center/contain no-repeat url('balance_icon.svg');
}.icon.info-icon {
  background: center/contain no-repeat url('info_icon.svg');
}.icon.arrow-left-stroke {
  background: center/contain no-repeat url('arrow-left-stroke.svg');
}.icon.arrow-left-slider {
  background: center/contain no-repeat url('arrow-left-slider.svg');
}.icon.arrow-right-stroke {
  background: center/contain no-repeat url('arrow-right-stroke.svg');
}.icon.arrow-right-slider {
  background: center/contain no-repeat url('arrow-right-slider.svg');
}.icon.arrow-up-square {
  background: center/contain no-repeat url('arrow-up-square.svg');
}.icon.close {
  background: center/contain no-repeat url('close.svg');
}.icon.close-square {
  background: center/contain no-repeat url('close-square.svg');
}.icon.check-shield {
  background: center/contain no-repeat url('check-shield.svg');
}.icon.contacts {
  background: center/contain no-repeat url('contacts.svg');
}.icon.copy {
  background: center/contain no-repeat url('copy.svg');
}.icon.check {
  background: center/contain no-repeat url('check.svg');
}.icon.check-circle {
  background: center/contain no-repeat url('check-circle.svg');
}.icon.delete {
  background: center/contain no-repeat url('delete.svg');
}.icon.options-vertical {
  background: center/contain no-repeat url('options-vertical.svg');
}.icon.temp {
  background: center/contain no-repeat url('temp.svg');
}.icon.document {
  background: center/contain no-repeat url('document.svg');
}.icon.dots {
  background: center/contain no-repeat url('dots.svg');
}.icon.dropdown-arrow-down {
  background: center/contain no-repeat url('dropdown-arrow-down.svg');
}.icon.dropdown-arrow-left {
  background: center/contain no-repeat url('dropdown-arrow-left.svg');
}.icon.dropdown-arrow-right {
  background: center/contain no-repeat url('dropdown-arrow-right.svg');
}.icon.dropdown-arrow-up {
  background: center/contain no-repeat url('dropdown-arrow-up.svg');
}.icon.edit-square {
  background: center/contain no-repeat url('edit-square.svg');
}.icon.export {
  background: center/contain no-repeat url('export.svg');
}.icon.logout {
  background: center/contain no-repeat url('logout.svg');
}.icon.plus {
  background: center/contain no-repeat url('plus.svg');
}.icon.settings {
  background: center/contain no-repeat url('settings.svg');
}.icon.staking {
  background: center/contain no-repeat url('staking.svg');
}.icon.time-circle {
  background: center/contain no-repeat url('time-circle.svg');
}.icon.wallet-options {
  background: center/contain no-repeat url('wallet-options.svg');
}.icon.update {
  background: center/contain no-repeat url('update.svg');
}.icon.update-with-dash {
  background: center/contain no-repeat url('update-with-dash_icon.svg');
}.icon.lock-transaction {
  background: center/contain no-repeat url('lock-transaction.svg');
}.icon.unlock-transaction {
  background: center/contain no-repeat url('unlock-transaction.svg');
}.icon.modal-info {
  background: center/contain no-repeat url('modal-info.svg');
}.icon.time-orange {
  background: center/contain no-repeat url('time.svg');
}.icon.unsecured {
  background: center/contain no-repeat url('unsecured.svg');
}.icon.new {
  background: center/contain no-repeat url('new.svg');
}.icon.alert {
  background: center/contain no-repeat url('alert.svg');
}.icon.error {
  background: center/contain no-repeat url('modal-alert.svg');
}.icon.secured {
  background: center/contain no-repeat url('secured.svg');
}.icon.success {
  background: center/contain no-repeat url('modal-success.svg');
}.icon.fire {
  background: center/contain no-repeat url('fire_ico.svg');
}.icon.block {
  background: center/contain no-repeat url('block_ico.svg');
}.loader {
  border: 2rem solid var(--azure-500);
  border-top: 2rem solid transparent;
  border-radius: 50%;
  min-width: 13rem;
  min-height: 13rem;
  animation: spin 2s linear infinite;
}@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}.migrate-alert .btn-migrate {
  background-color: rgba(31, 143, 235, 0.2);
  color: #1f8feb;
  font-size: 1.8rem;
  line-height: 1.2;
  padding: 0.8rem 2rem;
  border-radius: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}.migrate-alert .btn-migrate:hover {
  background-color: rgba(31, 143, 235, 0.3137254902);
}.migrate-alert .migration-details {
  font-size: 1.6rem;
}.modal-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--black-300);
  padding: 2rem;
  z-index: 100;
}.dialog-wrapper {
  padding: 2rem;
  border-radius: 0.8rem;
  max-height: 90vh;
  background: var(--dialog-background);
}.modal-overlay-transparent {
  background: transparent;
}.modal {
  position: relative;
  overflow: hidden;
}.modal .message-container {
  overflow: hidden;
  text-overflow: ellipsis;
}.modal .message-container .title,
.modal .message-container .message {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
}.modal button.close {
  position: absolute;
  top: 0.8rem;
  right: 0.8rem;
}.zano-mat-dialog .mat-dialog-title {
  font-size: 1.8rem;
  line-height: 1.2;
  font-weight: 400;
}.zano-mat-dialog .mat-dialog-container {
  padding: 2rem;
  border-radius: 0.8rem;
  color: var(--main-text);
  background: var(--dialog-background);
}.zano-mat-dialog .mat-dialog-content {
  margin: 0 -2rem;
  padding: 0 2rem;
}.zano-mat-dialog .mat-dialog-actions {
  padding: 2rem 0;
  margin-bottom: -2rem;
}.ng-select {
  width: 100%;
}.ng-select.ng-select-opened > .ng-select-container {
  background: var(--ng-select-bg);
  border-color: var(--ng-select-border);
}.ng-select.ng-select-opened > .ng-select-container:hover {
  box-shadow: none;
}.ng-select.ng-select-opened > .ng-select-container .ng-arrow {
  display: flex !important;
  align-items: center;
  justify-content: center;
  min-width: 0.8rem !important;
  min-height: 0.8rem !important;
  border-top: 1px solid;
  border-right: 1px solid;
  transform: rotate(-45deg);
}.ng-select.ng-select-opened.ng-select-top > .ng-select-container {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}.ng-select.ng-select-opened.ng-select-right > .ng-select-container {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}.ng-select.ng-select-opened.ng-select-bottom > .ng-select-container {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}.ng-select.ng-select-opened.ng-select-left > .ng-select-container {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}.ng-select.ng-select-focused:not(.ng-select-opened) > .ng-select-container {
  border-color: var(--ng-select-border);
  box-shadow: none;
}.ng-select.ng-select-disabled {
  cursor: not-allowed !important;
}.ng-select.ng-select-disabled > .ng-select-container {
  cursor: not-allowed !important;
}.ng-select.ng-select-disabled > .ng-select-container:hover {
  box-shadow: none;
  border: 1px solid var(--ng-select-bg);
}.ng-select .ng-has-value .ng-placeholder {
  display: none;
}.ng-select .ng-select-container {
  align-items: center;
  color: var(--main-text) !important;
  background-color: var(--ng-select-bg);
  border-radius: 0.8rem;
  border: 1px solid var(--ng-select-bg);
  min-height: 4rem;
  transition: border 0.2s ease-in-out;
  cursor: pointer !important;
}.ng-select .ng-select-container:hover {
  box-shadow: none;
  border: 1px solid var(--ng-select-border);
}.ng-select .ng-select-container .ng-value-container {
  align-items: center;
  padding-left: 1rem;
}[dir=rtl] .ng-select .ng-select-container .ng-value-container {
  padding-right: 1rem;
  padding-left: 0;
}.ng-select .ng-select-container .ng-value-container .ng-placeholder {
  color: var(--gray-700);
}.ng-select.ng-select-single .ng-select-container {
  height: 4rem;
}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  top: 0.8rem;
  left: 0;
  padding-left: 1rem;
  padding-right: 5rem;
}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input > input {
  color: var(--white-500);
  font-size: 1.8rem;
}[dir=rtl] .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  padding-right: 1rem;
  padding-left: 5rem;
}.ng-select.ng-select-multiple.ng-select-disabled {
  cursor: not-allowed;
}.ng-select.ng-select-multiple.ng-select-disabled > .ng-select-container .ng-value-container .ng-value {
  background-color: var(--main-text);
  border: 1px solid var(--ng-select-border);
}.ng-select.ng-select-multiple.ng-select-disabled > .ng-select-container .ng-value-container .ng-value .ng-value-label {
  padding: 0 0.5rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container {
  padding-top: 0.5rem;
  padding-left: 0.7rem;
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container {
  padding-right: 0.7rem;
  padding-left: 0;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--main-text) !important;
  background-color: var(--ng-select-highlight);
  border-radius: 0.2rem;
  margin-right: 0.5rem;
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
  margin-right: 0;
  margin-left: 0.5rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled {
  background-color: var(--main-text);
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-label {
  padding-left: 0.5rem;
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-label {
  padding-left: 0;
  padding-right: 0.5rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-label {
  display: inline-block;
  padding: 0.1rem 0.5rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon {
  display: inline-block;
  padding: 0.1rem 0.5rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon:hover {
  background-color: var(--ng-select-highlight), 5;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.left {
  border-right: 0.15rem solid var(--ng-select-highlight);
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.left {
  border-left: 0.15rem solid var(--ng-select-highlight);
  border-right: none;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right {
  border-left: 0.15rem solid var(--ng-select-highlight);
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right {
  border-left: 0;
  border-right: 0.15rem solid var(--ng-select-highlight);
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input {
  padding: 0 0 0.3rem 0.3rem;
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input {
  padding: 0 0.3rem 0.3rem 0;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input > input {
  color: var(--main-text);
  font-size: 1.8rem;
}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
  top: 0.8rem;
  padding-bottom: 0.5rem;
  padding-left: 0.3rem;
}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
  padding-right: 0.3rem;
  padding-left: 0;
}.ng-select .ng-clear-wrapper {
  color: var(--ng-select-border);
}.ng-select .ng-clear-wrapper:hover .ng-clear {
  color: #d0021b;
}.ng-select .ng-spinner-zone {
  padding: 0.5rem 0.5rem 0 0;
}[dir=rtl] .ng-select .ng-spinner-zone {
  padding: 0.5rem 0 0 0.5rem;
}.ng-select .ng-arrow-wrapper {
  width: 2.5rem;
  padding-right: 0.5rem;
}[dir=rtl] .ng-select .ng-arrow-wrapper {
  padding-left: 0.5rem;
  padding-right: 0;
}.ng-select .ng-arrow-wrapper:hover .ng-arrow {
  border-top-color: var(--ng-select-border);
}.ng-select .ng-arrow-wrapper .ng-arrow {
  display: flex !important;
  align-items: center;
  justify-content: center;
  min-width: 0.8rem !important;
  min-height: 0.8rem !important;
  border-top: 1px solid;
  border-right: 1px solid;
  transform: rotate(135deg);
}.ng-select.invalid > .ng-select-container,
.ng-select.invalid .ng-dropdown-panel, .ng-select.ng-touched.ng-invalid > .ng-select-container,
.ng-select.ng-touched.ng-invalid .ng-dropdown-panel {
  border: var(--border-error);
}.ng-dropdown-panel {
  background-color: var(--ng-select-bg);
  border: 2px solid var(--ng-select-border);
  box-shadow: none;
  left: 0;
}.ng-dropdown-panel.ng-select-top {
  bottom: 100%;
  border-top-right-radius: 0.8rem;
  border-top-left-radius: 0.8rem;
  border-bottom-color: var(--ng-select-border);
  margin-bottom: -0.1rem;
}.ng-dropdown-panel.ng-select-top .ng-dropdown-panel-items .ng-option:first-child {
  border-top-right-radius: 0.8rem;
  border-top-left-radius: 0.8rem;
}.ng-dropdown-panel.ng-select-right {
  left: 100%;
  top: 0;
  border-top-right-radius: 0.8rem;
  border-bottom-right-radius: 0.8rem;
  border-bottom-left-radius: 0.8rem;
  border-bottom-color: var(--ng-select-border);
  margin-bottom: -0.1rem;
}.ng-dropdown-panel.ng-select-right .ng-dropdown-panel-items .ng-option:first-child {
  border-top-right-radius: 0.8rem;
}.ng-dropdown-panel.ng-select-bottom {
  top: 100%;
  border-bottom-right-radius: 0.8rem;
  border-bottom-left-radius: 0.8rem;
  border-top-color: var(--ng-select-border);
  margin-top: -0.1rem;
}.ng-dropdown-panel.ng-select-bottom .ng-dropdown-panel-items .ng-option:last-child {
  border-bottom-right-radius: 0.8rem;
  border-bottom-left-radius: 0.8rem;
}.ng-dropdown-panel.ng-select-left {
  left: -100%;
  top: 0;
  border-top-left-radius: 0.8rem;
  border-bottom-right-radius: 0.8rem;
  border-bottom-left-radius: 0.8rem;
  border-bottom-color: var(--ng-select-border);
  margin-bottom: -0.1rem;
}.ng-dropdown-panel.ng-select-left .ng-dropdown-panel-items .ng-option:first-child {
  border-top-left-radius: 0.8rem;
}.ng-dropdown-panel .ng-dropdown-header {
  border-bottom: 0.15rem solid var(--ng-select-border);
  padding: 0.5rem 0.7rem;
}.ng-dropdown-panel .ng-dropdown-footer {
  border-top: 0.15rem solid var(--ng-select-border);
  padding: 0.5rem 0.7rem;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  -webkit-user-select: none;
          user-select: none;
  padding: 0.8rem 1rem;
  font-weight: 500;
  color: var(--main-text);
  cursor: pointer;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-disabled {
  cursor: not-allowed;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-marked {
  background-color: var(--ng-select-highlight);
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-selected, .ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-selected.ng-option-marked {
  color: var(--main-text);
  background-color: var(--ng-select-highlight);
  font-weight: 600;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  background-color: var(--ng-select-bg);
  color: var(--main-text);
  padding: 0.8rem 1rem;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected, .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
  color: var(--main-text) !important;
  background-color: var(--ng-select-highlight);
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected .ng-option-label, .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked .ng-option-label {
  font-weight: 600;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  background-color: var(--ng-select-highlight);
  color: var(--main-text) !important;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-disabled {
  color: rgba(255, 255, 255, 0.5) !important;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-child {
  padding-left: 2.2rem;
}[dir=rtl] .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-child {
  padding-right: 2.2rem;
  padding-left: 0;
}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-tag-label {
  font-size: 80%;
  font-weight: 400;
  padding-right: 0.5rem;
}[dir=rtl] .ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-tag-label {
  padding-left: 0.5rem;
  padding-right: 0;
}[dir=rtl] .ng-dropdown-panel {
  direction: rtl;
  text-align: right;
}.ng-select.with-circle .ng-dropdown-panel .ng-option {
  position: relative;
  padding: 0.8rem 3rem 0.8rem 0.8rem;
}.ng-select.with-circle .ng-dropdown-panel .ng-option:after {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  display: block;
  content: "";
  width: 1.8rem;
  height: 1.8rem;
  border: var(--ng-select-circle-border);
  border-radius: 50%;
}.ng-select.with-circle .ng-dropdown-panel .ng-option.ng-option-selected:before {
  position: absolute;
  top: 50%;
  right: 1.4rem;
  transform: translateY(-50%);
  display: block;
  content: "";
  width: 1rem;
  height: 1rem;
  background: var(--ng-select-circle-background);
  border-radius: 50%;
}.ngx-pagination a {
  min-width: 29px;
  cursor: pointer;
  color: var(--main-text) !important;
}.ngx-pagination a:hover {
  background: transparent !important;
}.ngx-pagination .current {
  background: transparent !important;
  color: var(--azure-500) !important;
}.ngx-pagination .pagination-next,
.ngx-pagination .pagination-previous {
  background-color: var(--btn-icon-background);
  transition: background-color 0.2s ease-in-out;
  border-radius: 999px;
}.ngx-pagination .pagination-next:hover,
.ngx-pagination .pagination-previous:hover {
  background-color: var(--btn-icon-hover-background);
}.ngx-pagination .pagination-next.disabled,
.ngx-pagination .pagination-previous.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}.custom-pagination {
  display: flex;
  align-items: center;
  min-height: 2.8rem;
}.wrapper-tab-preloader {
  display: flex;
  z-index: 999;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--tab-preloader-background);
}.preloader {
  align-self: center;
  color: var(--tab-preloader-text);
  font-size: 2rem;
  margin: 0 auto;
  text-align: center;
  width: 50%;
}.preloader .loading-bar {
  display: block;
  animation: move 5s linear infinite;
  background-color: var(--azure-500);
  background-image: -webkit-gradient(linear, 0 0, 100% 100%, color-stop(0.125, rgba(0, 0, 0, 0.15)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.1)), color-stop(0.375, rgba(0, 0, 0, 0.1)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.15)), color-stop(0.625, rgba(0, 0, 0, 0.15)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.1)), color-stop(0.875, rgba(0, 0, 0, 0.1)), color-stop(0.875, transparent), to(transparent)), -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.125, rgba(0, 0, 0, 0.3)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.25)), color-stop(0.375, rgba(0, 0, 0, 0.25)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.3)), color-stop(0.625, rgba(0, 0, 0, 0.3)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.25)), color-stop(0.875, rgba(0, 0, 0, 0.25)), color-stop(0.875, transparent), to(transparent));
  background-size: 10rem 10rem;
  width: 100%;
  height: 1rem;
}@keyframes move {
  0% {
    background-position: 100% -10rem;
  }
  100% {
    background-position: 100% 10rem;
  }
}app-progress-container .progress-bar-container {
  color: var(--white-500);
}app-progress-container .progress-bar-container .progress-bar {
  background-color: var(--blue-300);
}app-progress-container .progress-bar-container .progress-bar .progress-bar-full {
  background-color: var(--azure-500);
}.seed-phrase .content .item .number {
  width: 1.8rem;
  height: 1.8rem;
  border-radius: 50%;
  font-size: 1rem;
}.seed-phrase .content .item .word {
  white-space: nowrap;
}.light .seed-phrase .content .item {
  border: var(--border);
  background-color: rgba(31, 143, 235, 0.1019607843);
  color: #1f8feb;
}.light .seed-phrase .content .item .number {
  background-color: rgba(31, 143, 235, 0.1490196078);
  color: #1f8feb;
}.dark .seed-phrase .content .item {
  border: var(--border);
  background-color: var(--gray-900);
}.dark .seed-phrase .content .item .number {
  background-color: var(--gray-600);
}app-synchronization-status {
  width: 100%;
}.synchronization-status {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--synchronization-status-color);
  width: 100%;
}.synchronization-status .status-container {
  position: relative;
  width: 100%;
  margin-bottom: 0.5rem;
}.synchronization-status .status-container .offline,
.synchronization-status .status-container .online {
  display: flex;
  width: 100%;
}.synchronization-status .status-container .offline span,
.synchronization-status .status-container .online span {
  position: relative;
  padding-left: 2.2rem;
}.synchronization-status .status-container .offline span:before,
.synchronization-status .status-container .online span:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
}.synchronization-status .status-container .offline > span:before {
  background-color: var(--red-500);
}.synchronization-status .status-container .online > span:before {
  background-color: var(--aqua-500);
}.synchronization-status .status-container .syncing,
.synchronization-status .status-container .loading {
  font-size: 1.4rem;
  line-height: 1.2;
}.synchronization-status .status-container .progress-bar-container {
  width: 100%;
  height: 0.6rem;
}.synchronization-status .status-container .progress-bar-container .syncing {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 0.4rem;
}.synchronization-status .status-container .progress-bar-container .syncing .progress-bar {
  border-radius: 0.2rem;
  height: 0.6rem;
  width: 100%;
  overflow: hidden;
  background-color: var(--synchronization-progress-bar-container-background);
}.synchronization-status .status-container .progress-bar-container .syncing .progress-bar .fill {
  border-radius: 0.2rem;
  height: 100%;
  background-color: var(--aqua-500);
}.synchronization-status .status-container .progress-bar-container .syncing .progress-percent {
  color: var(--aqua-500);
  font-size: 1.4rem;
  line-height: 1.2;
  padding-left: 1rem;
}.synchronization-status .status-container .progress-bar-container .loading {
  background-color: var(--aqua-500);
  animation: move 5s linear infinite;
  background-image: -webkit-gradient(linear, 0 0, 100% 100%, color-stop(0.125, rgba(0, 0, 0, 0.15)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.1)), color-stop(0.375, rgba(0, 0, 0, 0.1)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.15)), color-stop(0.625, rgba(0, 0, 0, 0.15)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.1)), color-stop(0.875, rgba(0, 0, 0, 0.1)), color-stop(0.875, transparent), to(transparent)), -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.125, rgba(0, 0, 0, 0.3)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.25)), color-stop(0.375, rgba(0, 0, 0, 0.25)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.3)), color-stop(0.625, rgba(0, 0, 0, 0.3)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.25)), color-stop(0.875, rgba(0, 0, 0, 0.25)), color-stop(0.875, transparent), to(transparent));
  background-size: 7rem 7rem;
  height: 100%;
}.synchronization-status .status-container .blocks {
  margin-top: 5px;
  font-size: 1.4rem;
  line-height: 1.4;
  font-weight: 400;
  word-break: break-all;
}.synchronization-status .status-container .blocks i {
  min-width: 1rem;
  min-height: 1rem;
}.synchronization-status .status-container .blocks span {
  font-size: 1.4rem;
  line-height: 1.4;
  font-weight: 400;
  color: #a8abb5;
}.synchronization-status .update-container {
  display: flex;
  align-items: center;
  text-align: right;
}.synchronization-status .update-container .update-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-items: center;
  font-size: 1.4rem;
  line-height: 1.2;
  text-align: left;
}.synchronization-status .update-container .update-text.time {
  font-size: 1.1rem;
}.synchronization-status .update-container .icon {
  flex: 1 0 auto;
  margin: 0.3rem 0 0 0.6rem;
  width: 1.2rem;
  height: 1.2rem;
}.synchronization-status .update-container .standard {
  color: var(--aqua-500);
}.synchronization-status .update-container .important {
  color: var(--orange-500);
}.synchronization-status .update-container .critical {
  color: var(--red-500);
}.synchronization-status .update-container .time-orange {
  color: var(--orange-500);
}.synchronization-status .update-container .icon.standard .st0 {
  fill: var(--aqua-500);
}.synchronization-status .update-container .icon.important .st0 {
  fill: var(--orange-500);
}.synchronization-status .update-container .icon.critical .st0 {
  fill: var(--red-500);
}@keyframes move {
  0% {
    background-position: 100% -7rem;
  }
  100% {
    background-position: 100% 7rem;
  }
}table.zano-table {
  width: 100%;
  table-layout: fixed;
}table.zano-table .row-divider {
  height: 1rem;
  transition: 0.2s height linear, 0s font-size;
  transition-delay: 0s, 0.2s;
}table.zano-table .row-divider.hide {
  height: 0;
}table.zano-table > thead {
  text-align: left;
  border-radius: 0.8rem;
  overflow: auto;
}table.zano-table > thead > tr {
  /** Sticky header */
}table.zano-table > thead > tr > th {
  background-color: var(--tab-content-background);
  z-index: 5;
  max-width: 10rem;
  overflow: hidden;
  text-overflow: ellipsis;
}table.zano-table > thead > tr > th .bg {
  background-color: var(--table-thead-bg);
}table.zano-table > thead > tr > th .title {
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2rem;
  width: 100%;
  white-space: nowrap;
}table.zano-table > thead > tr > th > table.zano-table > thead > tr > th:first-child .title {
  border-radius: 0.8rem 0 0 0.8rem;
}table.zano-table > thead > tr > th > table.zano-table > thead > tr > th:last-child .title {
  border-radius: 0 0.8rem 0.8rem 0;
}table.zano-table > thead > tr > th {
  position: sticky;
  top: 0;
}table.zano-table > tbody {
  text-align: left;
}table.zano-table > tbody > tr {
  background-color: var(--table-row-bg);
  transition: 0.5s height linear, 0s font-size;
  transition-delay: 0s, 0.5s;
  height: auto;
}table.zano-table > tbody > tr > td {
  padding: 2rem;
  vertical-align: middle;
  white-space: nowrap;
  max-width: 30rem;
  overflow: hidden;
  text-overflow: ellipsis;
}table.zano-table > tbody > tr > td > table.zano-table > tbody > tr > td:first-child {
  border-radius: 0.8rem 0 0 0.8rem;
}table.zano-table > tbody > tr > td > table.zano-table > tbody > tr > td:last-child {
  border-radius: 0 0.8rem 0.8rem 0;
}table.zano-table > tbody > tr > table.zano-table > tbody > tr:not(.details) {
  cursor: pointer;
}.table-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: var(--table-info-border);
  border-radius: 0.8rem;
  overflow: hidden;
}.table-info .separator {
  border: none;
  border-bottom: var(--table-info-border);
}.table-info .row {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  min-height: 6rem;
}.table-info .row .label,
.table-info .row .text {
  overflow: hidden;
  padding: 2rem;
}.table-info .row .label {
  color: var(--azure-500);
  background: var(--table-info-label-background);
  overflow: hidden;
  text-overflow: ellipsis;
}.table-info .row .text {
  width: 100%;
  word-break: break-word;
}table.rounded-corners {
  border-spacing: 0;
  border-collapse: separate;
  border-radius: 1rem;
  border: var(--table-rounded-corners-border);
}table.rounded-corners th:not(:last-child),
table.rounded-corners td:not(:last-child) {
  border-right: var(--table-rounded-corners-border);
}table.rounded-corners > tbody > tr:first-child > td:first-child {
  border-top-left-radius: 0.8rem;
}table.rounded-corners > tbody > tr:first-child > td:last-child {
  border-top-right-radius: 0.8rem;
}table.rounded-corners > tbody > tr:last-child > td:first-child {
  border-bottom-left-radius: 0.8rem;
}table.rounded-corners > tbody > tr:last-child > td:last-child {
  border-bottom-right-radius: 0.8rem;
}table.rounded-corners > thead > tr:not(:last-child) > th,
table.rounded-corners > thead > tr:not(:last-child) > td,
table.rounded-corners > tbody > tr:not(:last-child) > th,
table.rounded-corners > tbody > tr:not(:last-child) > td,
table.rounded-corners > tfoot > tr:not(:last-child) > th,
table.rounded-corners > tfoot > tr:not(:last-child) > td,
table.rounded-corners > tr:not(:last-child) > td,
table.rounded-corners > tr:not(:last-child) > th,
table.rounded-corners > thead:not(:last-child),
table.rounded-corners > tbody:not(:last-child),
table.rounded-corners > tfoot:not(:last-child) {
  border-bottom: var(--table-rounded-corners-border);
}.tabs {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: hidden;
}.tabs .tabs-header {
  display: flex;
  justify-content: space-between;
  min-height: 5.8rem;
}.tabs .tabs-header .tab-header {
  background-color: var(--tab-header-background);
  border-radius: 0.8rem 0.8rem 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  min-height: 5.8rem;
  flex: 1 1 auto;
  transition: background-color 0.25s ease-in-out;
}.tabs .tabs-header .tab-header i,
.tabs .tabs-header .tab-header span {
  opacity: 0.75;
  transition: opacity 0.25s ease-in-out;
}.tabs .tabs-header .tab-header.active, .tabs .tabs-header .tab-header:hover:not(.active):not(.disabled) {
  background-color: var(--tab-header-active-background);
}.tabs .tabs-header .tab-header.active i,
.tabs .tabs-header .tab-header.active span, .tabs .tabs-header .tab-header:hover:not(.active):not(.disabled) i,
.tabs .tabs-header .tab-header:hover:not(.active):not(.disabled) span {
  opacity: 1;
}.tabs .tabs-header .tab-header.hide {
  display: none;
}.tabs .tabs-header .tab-header .indicator {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2rem;
  font-size: 1.2rem;
  line-height: 1.4rem;
  min-width: 2.4rem;
  height: 1.6rem;
}.tabs .tabs-header .tab-header:disabled {
  cursor: not-allowed;
}.tabs .tabs-header .tab-header:not(:last-child) {
  margin-right: 0.5rem;
}.tabs .tabs-content {
  display: flex;
  flex: auto;
  overflow: hidden;
  border-radius: 0 0 0.8rem 0.8rem;
  background-color: var(--tab-content-background);
  padding: 2rem;
  position: relative;
}.table-tooltip {
  z-index: 9999;
  padding: 1rem;
  border-radius: 0.8rem;
  background: var(--tooltip-background);
}.table-tooltip .tooltip-inner {
  font-size: 1.4rem;
  line-height: 1.2;
  white-space: pre-wrap;
}.table-tooltip.ng-tooltip-top {
  margin-top: -1rem;
}.table-tooltip.ng-tooltip-top:after {
  content: "";
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
  position: absolute;
  bottom: -0.5rem;
  left: calc(50% - 0.5rem);
}.table-tooltip .ng-tooltip-bottom-left {
  margin-top: 1rem;
}.table-tooltip .ng-tooltip-bottom-left::before {
  content: "";
  position: absolute;
  top: -0.5rem;
  left: 3rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background-color: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-top-left {
  margin-top: -1rem;
}.table-tooltip.ng-tooltip-top-left:after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-top-right {
  margin-top: -1rem;
}.table-tooltip.ng-tooltip-top-right:after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  right: 0.7rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-bottom {
  margin-top: 1rem;
}.table-tooltip.ng-tooltip-bottom:before {
  content: "";
  position: absolute;
  top: -0.5rem;
  left: calc(50% - 0.5rem);
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-bottom-left {
  margin-top: 1rem;
}.table-tooltip.ng-tooltip-bottom-left::before {
  content: "";
  position: absolute;
  top: -0.5rem;
  left: 3rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background-color: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-bottom-right {
  position: relative;
  margin-top: 1rem;
}.table-tooltip.ng-tooltip-bottom-right:before {
  content: "";
  position: absolute;
  top: -0.5rem;
  right: 0.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-left {
  margin-left: -1rem;
}.table-tooltip.ng-tooltip-left:after {
  content: "";
  position: absolute;
  top: calc(50% - 0.5rem);
  right: -0.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip.ng-tooltip-right {
  margin-left: 1rem;
}.table-tooltip.ng-tooltip-right:before {
  content: "";
  position: absolute;
  top: calc(50% - 0.5rem);
  left: -0.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: var(--tooltip-background);
  transform: rotate(45deg);
}.table-tooltip-dimensions .tooltip-inner {
  overflow: auto;
  max-width: 20rem;
  max-height: 10rem;
}.tooltip {
  z-index: 999;
  padding: 1rem;
  border-radius: 0.6rem;
  background-color: var(--tooltip-background);
  font-size: 1.2rem;
}.balance-tooltip {
  z-index: 999;
  padding: 1rem;
  border-radius: 1rem;
  background-color: var(--tooltip-background);
}.balance-tooltip .tooltip-inner {
  display: flex;
  flex-direction: column;
  font-size: 1.3rem;
}.balance-tooltip .tooltip-inner .available {
  margin-bottom: 0.7rem;
}.balance-tooltip .tooltip-inner .available b {
  font-weight: 600;
}.balance-tooltip .tooltip-inner .locked {
  margin-bottom: 0.7rem;
}.balance-tooltip .tooltip-inner .locked b {
  font-weight: 600;
}.balance-tooltip .tooltip-inner .link {
  cursor: pointer;
  color: var(--azure-500);
}.balance-tooltip .balance-scroll-list {
  display: flex;
  flex-direction: column;
  max-height: 20rem;
  overflow-y: auto;
}.balance-tooltip.ng-tooltip-top {
  margin-top: -1rem;
}.balance-tooltip.ng-tooltip-bottom {
  margin-top: 1rem;
}.balance-tooltip.ng-tooltip-left {
  margin-left: -1rem;
}.balance-tooltip.ng-tooltip-right {
  margin-left: 1rem;
}.account-tooltip {
  z-index: 9999;
  background-color: var(--tooltip-background);
}.account-tooltip .tooltip-inner {
  word-break: break-word;
  max-width: 18rem;
}.comment-tooltip {
  z-index: 999;
  background-color: var(--tooltip-background);
}.comment-tooltip .tooltip-inner {
  word-break: break-word;
  max-width: 50rem;
  max-height: 25rem;
}.update-tooltip {
  z-index: 999;
  padding: 1rem;
  background-color: var(--tooltip-background);
}.update-tooltip.important {
  background: var(--red-500);
}.update-tooltip.important.ng-tooltip-left-bottom:after {
  border-color: transparent transparent var(--red-500) var(--red-500);
}.update-tooltip.important.ng-tooltip-right-bottom:before {
  border-color: transparent var(--red-500) var(--red-500) transparent;
}.update-tooltip.critical {
  padding: 2.5rem;
  background: var(--red-500);
}.update-tooltip.critical .tooltip-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}.update-tooltip.critical.ng-tooltip-left-bottom:after {
  border-color: transparent transparent var(--red-500) var(--red-500);
}.update-tooltip.critical.ng-tooltip-right-bottom:before {
  border-color: transparent var(--red-500) var(--red-500) transparent;
}.update-tooltip .tooltip-inner {
  font-size: 1.3rem;
  line-height: 1.2;
  white-space: pre-wrap;
}.update-tooltip .tooltip-inner .standard-update {
  font-size: 1.5rem;
  line-height: 1.2;
  color: var(--azure-500);
}.update-tooltip .tooltip-inner .important-update {
  font-size: 1.5rem;
  line-height: 1.2;
  color: var(--orange-500);
}.update-tooltip .tooltip-inner .critical-update {
  font-size: 1.5rem;
  line-height: 1.2;
  text-align: center;
}.update-tooltip .tooltip-inner .wrong-time {
  font-size: 1.5rem;
  line-height: 1.2;
  color: var(--orange-500);
}.update-tooltip .tooltip-inner .icon {
  margin: 1.5rem 0;
  width: 5rem;
  height: 5rem;
}.update-tooltip.ng-tooltip-left-bottom {
  margin-left: -1.5rem;
}.update-tooltip.ng-tooltip-left-bottom:after {
  content: "";
  position: absolute;
  bottom: 0.6rem;
  right: -1rem;
  border-width: 0.5rem;
  border-style: solid;
  border-color: transparent transparent var(--tooltip-background) var(--tooltip-background);
}.update-tooltip.ng-tooltip-right-bottom {
  margin-left: 1.5rem;
}.update-tooltip.ng-tooltip-right-bottom:before {
  content: "";
  position: absolute;
  bottom: 0.6rem;
  left: -1rem;
  border-width: 0.5rem;
  border-style: solid;
  border-color: transparent var(--tooltip-background) var(--tooltip-background) transparent;
}.update-tooltip {
  z-index: 999;
  background-color: var(--tooltip-background);
}.update-tooltip .tooltip-inner .icon {
  background: center/contain no-repeat url('update-alert.svg');
}.wallet {
  border-radius: 0.8rem;
  position: relative;
  display: flex;
  flex-direction: column;
  max-width: 19rem;
  min-width: 19rem;
  width: 100%;
  padding: 1.2rem;
  background-color: var(--wallet-background);
  border: var(--wallet-border);
  cursor: pointer;
  color: var(--wallet-text);
}.wallet.offset-testnet {
  margin: 1rem 0.5rem 0 0;
}.wallet .testnet {
  background-color: var(--red-500);
  color: var(--white);
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 999px;
  z-index: 10;
  position: absolute;
  right: -0.5rem;
  top: -0.4rem;
}.wallet .content {
  z-index: 10;
}.wallet .header {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}.wallet .header .left {
  overflow: hidden;
}.wallet .header .left .name .indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 1.8rem;
  height: 1.8rem;
  padding: 0.5rem;
  margin-right: 0.8rem;
  font-size: 1.2rem;
  line-height: 1;
  background-color: var(--white-500);
  color: var(--azure-500);
}.wallet .balance {
  display: flex;
  align-items: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
}.wallet .price {
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.2;
  display: flex;
  flex-wrap: nowrap;
  align-items: baseline;
}.wallet .price .percent,
.wallet .price .currency {
  font-size: 1.4rem;
  font-weight: 400;
}.wallet .staking {
  margin-top: 0.8rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}.wallet .account-synchronization {
  display: flex;
  align-items: center;
  width: 100%;
}.wallet .account-synchronization .progress-bar {
  border-radius: 1rem;
  flex: 1 0 auto;
  height: 0.4rem;
  overflow: hidden;
}.wallet .account-synchronization .progress-bar .fill {
  height: 100%;
}.wallet .account-synchronization .progress-percent {
  flex: 0 0 auto;
  font-size: 1.4rem;
  line-height: 1.2;
  padding-left: 1rem;
}.wallet.active {
  color: var(--wallet-active-text);
  border: none;
  padding: 1.35rem;
  background: var(--wallet-active-background);
}.wallet.auditable, .wallet.watch-only {
  border-width: 0;
  padding: 1.35rem;
  background: var(--wallet-auditable-watch-only-background);
}.wallet.auditable:hover:not(.active) {
  padding: 1.2rem;
  border-width: 0.15rem;
}.wallet.auditable.active {
  padding: 1.35rem;
  border: none;
  background: var(--wallet-auditable-active-background);
}.wallet.watch-only {
  color: var(--wallet-watch-only-text);
}.wallet.watch-only:after {
  content: "";
  display: block;
  background: var(--wallet-watch-only-after-background);
  position: absolute;
  border-radius: 0.6rem;
  left: 0.25rem;
  right: 0.25rem;
  top: 0.25rem;
  bottom: 0.25rem;
  z-index: 1;
}.wallet.watch-only.active, .wallet.watch-only:hover {
  background: var(--wallet-watch-only-active-background);
}.wallet:hover:not(.active):not(.watch-only) {
  border-color: var(--wallet-border-color-hover);
}.wallet:focus {
  outline: none;
}.wallet:focus-visible {
  outline: 2px solid;
  outline-offset: 4px;
}.wallet .progress-bar {
  background-color: var(--gray-800);
}.wallet .progress-bar .fill {
  background-color: var(--white-500);
}app-wallet-card:last-child .wallet {
  margin-bottom: 0 !important;
}.light .wallet.active .header .close {
  color: #ffffff;
}.light .wallet.active.watch-only .header .close {
  color: var(--wallet-watch-only-text);
}.light .wallet .progress-bar {
  background-color: rgba(12, 12, 58, 0.1019607843);
}.light .wallet .progress-bar .fill {
  background-color: var(--aqua-500);
}.light .wallet.active .progress-bar .fill {
  background-color: var(--white-500);
}.mat-tooltip {
  font-size: 1.6rem;
  background: var(--tooltip-background);
}.mat-tooltip-address {
  max-width: 90vw !important;
}.zano-mat-menu.mat-menu-panel {
  background-color: var(--list-background);
  border: var(--list-border);
  border-radius: var(--border-radius);
  min-height: 5.7rem;
}.zano-mat-menu .mat-menu-item {
  height: 4.1rem;
  line-height: 4.1rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: var(--main-text);
}.zano-mat-menu .mat-menu-item .mat-icon-no-color,
.zano-mat-menu .mat-menu-submenu-icon {
  color: var(--main-text);
}.light .zano-mat-menu .mat-menu-item:hover:not([disabled]),
.light .zano-mat-menu .mat-menu-item.cdk-program-focused:not([disabled]),
.light .zano-mat-menu .mat-menu-item.cdk-keyboard-focused:not([disabled]),
.light .zano-mat-menu .mat-menu-item-highlighted:not([disabled]) {
  background-color: rgba(0, 0, 0, 0.04);
}app-send-details-modal .status .image,
app-success-sweep-bare-outs .status .image,
app-transaction-details-for-custom-assets .status .image,
app-swap-details .status .image {
  max-width: 13rem;
  max-height: 13rem;
  width: 100%;
  height: 100%;
}app-send-details-modal .status .image img,
app-success-sweep-bare-outs .status .image img,
app-transaction-details-for-custom-assets .status .image img,
app-swap-details .status .image img {
  width: 100%;
  height: 100%;
}app-send-details-modal .details .header,
app-success-sweep-bare-outs .details .header,
app-transaction-details-for-custom-assets .details .header,
app-swap-details .details .header {
  min-height: 4rem;
  max-height: 4rem;
  background-color: var(--details-background);
}app-send-details-modal .details-wrapper,
app-success-sweep-bare-outs .details-wrapper,
app-transaction-details-for-custom-assets .details-wrapper,
app-swap-details .details-wrapper {
  max-height: 35rem;
  background-color: var(--details-background);
  scroll-behavior: smooth;
}app-send-details-modal .details-list,
app-success-sweep-bare-outs .details-list,
app-transaction-details-for-custom-assets .details-list,
app-swap-details .details-list {
  width: 100%;
}app-send-details-modal .details-list .item .image,
app-success-sweep-bare-outs .details-list .item .image,
app-transaction-details-for-custom-assets .details-list .item .image,
app-swap-details .details-list .item .image {
  max-width: 1.5rem;
  max-height: 1.5rem;
  width: 100%;
  height: 100%;
}app-send-details-modal .details-list .item .image img,
app-success-sweep-bare-outs .details-list .item .image img,
app-transaction-details-for-custom-assets .details-list .item .image img,
app-swap-details .details-list .item .image img {
  width: 100%;
  height: 100%;
}app-assign-alias .assign-alias-tooltip {
  z-index: 999;
  max-width: 46rem;
  background-color: var(--blue-450);
  color: var(--white-500);
}app-assign-alias .has-no-edit-symbol {
  position: relative;
  width: 100%;
}app-assign-alias .has-no-edit-symbol input {
  padding-left: 2.35rem;
}app-assign-alias .has-no-edit-symbol:after {
  content: "@";
  position: absolute;
  display: inline-block;
  top: 50%;
  left: 1rem;
  transform: translateY(-50%);
}.assets-table {
  table-layout: auto !important;
}.assets-table .token-logo {
  background: var(--blue-300);
  min-width: 5.5rem;
  min-height: 5.5rem;
  max-width: 5.5rem;
  max-height: 5.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  overflow: hidden;
}.assets-table .token-logo img,
.assets-table .token-logo svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}app-contracts .container .wrap-table table.contracts-table tbody tr {
  cursor: pointer;
  outline: none !important;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract {
  position: relative;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract .icon {
  flex-shrink: 0;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract .icon.new {
  width: 1.7rem;
  height: 1.7rem;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract .icon.alert {
  width: 1.7rem;
  height: 1.2rem;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract .icon.purchase, app-contracts .container .wrap-table table.contracts-table tbody tr .contract .icon.sell {
  width: 1.5rem;
  height: 1.5rem;
}app-contracts .container .wrap-table table.contracts-table tbody tr .contract span {
  text-overflow: ellipsis;
  overflow: hidden;
}app-contracts .container .wrap-table table.contracts-table tbody tr .status,
app-contracts .container .wrap-table table.contracts-table tbody tr .comment {
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
}app-history .wrap-table table.history-table tbody tr .status {
  position: relative;
}app-history .wrap-table table.history-table tbody tr .status .confirmation {
  width: 1.7rem;
  height: 1.7rem;
}app-history .wrap-table table.history-table tbody tr .status img.status-transaction {
  width: 1.5rem;
  height: 1.5rem;
}app-purchase .container {
  position: relative;
}app-purchase .container .form {
  max-width: 100%;
}app-purchase .container .details .content .form__field--row > div {
  flex: 0 1 22rem;
  margin-right: 2rem;
}app-purchase .container .details .content .form__field--row > div:last-child {
  margin-right: 0;
}app-purchase .container .purchase-buttons button {
  flex: 0 1 33%;
  margin-right: 0.5rem;
}app-purchase .container .purchase-buttons button:last-child {
  margin-right: 0;
}app-purchase .container .nullify-block-row .nullify-block-buttons button {
  flex: 0 1 25%;
  margin: 0 0.5rem;
}app-purchase .container .time-cancel-block-row .form__field {
  width: 25%;
}app-purchase .container .time-cancel-block-row .time-cancel-block-buttons button {
  flex: 0 1 25%;
  margin: 0 0.5rem;
}app-receive .container .wrap-qr {
  max-width: 30rem;
  max-height: 30rem;
}app-receive .container .wrap-qr img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0.8rem;
}app-receive .container .address {
  width: 27.1rem;
  height: 4rem;
  border: var(--border);
}.light app-receive .container .wrap-qr img {
  border: var(--border);
}app-staking .chart-header .selected-group {
  min-width: 19rem;
}app-staking .chart-header .items .item {
  min-width: 18rem;
  max-width: 25rem;
  min-height: 4rem;
  border: var(--border);
}app-staking .chart-header .items .item .left {
  min-width: fit-content;
  width: auto;
}app-staking .chart {
  position: relative;
  border: var(--border);
  min-height: 29rem;
}app-staking .chart > div {
  position: absolute;
  width: 100%;
  height: 100%;
}.light app-staking .chart-header .items .item {
  border: var(--table-info-border);
}app-wallet {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}app-no-wallet .no-wallet-wrapper {
  position: relative;
}app-no-wallet .no-wallet-wrapper app-synchronization-status {
  position: absolute;
  left: 0;
  bottom: 0;
}app-contacts .wrap-table table.contacts-table thead tr th:last-child {
  max-width: 14rem;
}app-contacts .wrap-table table.contacts-table tbody tr td:last-child {
  width: 14rem;
  min-width: fit-content;
}app-contacts .wrap-table table.contacts-table tbody tr td .button-wrapper button {
  margin-right: 0.8rem;
}app-contacts .wrap-table table.contacts-table tbody tr td .button-wrapper button:last-child {
  margin-right: 0;
}:focus {
  outline: none;
}.user-is-tabbing :focus {
  outline: 2px solid;
  outline-offset: -2px;
  transition: outline 0.1s ease-in;
}.light .user-is-tabbing :focus {
  outline-color: black;
}.dark .user-is-tabbing :focus {
  outline-color: white;
}.color-red {
  color: var(--red-500);
}.color-primary {
  color: var(--azure-500);
}.color-aqua {
  color: var(--aqua-500);
}.border-radius-0_8-rem {
  border-radius: 0.8rem;
}.cursor-pointer {
  cursor: pointer;
}.cursor-default {
  cursor: default !important;
}.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}.text-align-center {
  text-align: center;
}.text-align-end {
  text-align: end;
}.word-break-break-all {
  word-break: break-all;
}.word-break-break-word {
  word-break: break-word;
}.bg-light-gray {
  background-color: var(--gray-900);
}.bg-light-blue {
  background-color: var(--blue-700);
}.bg-light-blue-details {
  background-color: var(--blue-500);
}.background-none {
  background: none;
}.overflow-hidden {
  overflow: hidden;
}.overflow-auto {
  overflow: auto;
}.overflow-x-hidden {
  overflow-x: hidden;
}.overflow-y-hidden {
  overflow-y: hidden;
}.overflow-x-auto {
  overflow-x: auto;
}.overflow-y-auto {
  overflow-y: auto;
}.no-scroll {
  overflow: hidden;
}.rotate-90 {
  transform: rotate(90deg);
}.rotate-180 {
  transform: rotate(180deg);
}.rotate-270 {
  transform: rotate(270deg);
}.rotate-360 {
  transform: rotate(360deg);
}.opacity-0 {
  opacity: 0;
}.opacity-1 {
  opacity: 1;
}.ml-auto {
  margin-left: auto;
}.mr-auto {
  margin-right: auto;
}.mt-auto {
  margin-top: auto;
}.mb-auto {
  margin-bottom: auto;
}.mx-0 {
  margin-left: 0;
  margin-right: 0;
}.mx-0_5 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}.mx-1 {
  margin-left: 1rem;
  margin-right: 1rem;
}.mx-2 {
  margin-left: 2rem;
  margin-right: 2rem;
}.mx-3 {
  margin-left: 3rem;
  margin-right: 3rem;
}.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}.my-0_5 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}.my-1 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}.my-2 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}.my-3 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}.ml-0 {
  margin-left: 0;
}.ml-0_5 {
  margin-left: 0.5rem;
}.ml-1 {
  margin-left: 1rem;
}.ml-2 {
  margin-left: 2rem;
}.ml-3 {
  margin-left: 3rem;
}.mb-0 {
  margin-bottom: 0;
}.mb-0_5 {
  margin-bottom: 0.5rem;
}.mb-1 {
  margin-bottom: 1rem;
}.mb-2 {
  margin-bottom: 2rem;
}.mb-3 {
  margin-bottom: 3rem;
}.mr-0 {
  margin-right: 0;
}.mr-0_5 {
  margin-right: 0.5rem;
}.mr-1 {
  margin-right: 1rem;
}.mr-2 {
  margin-right: 2rem;
}.mr-3 {
  margin-right: 3rem;
}.mt-0 {
  margin-top: 0;
}.mt-0_5 {
  margin-top: 0.5rem;
}.mt-1 {
  margin-top: 1rem;
}.mt-2 {
  margin-top: 2rem;
}.mt-3 {
  margin-top: 3rem;
}.m-0 {
  margin: 0;
}.m-1 {
  margin: 0.5rem;
}.m-1 {
  margin: 1rem;
}.m-2 {
  margin: 2rem;
}.m-3 {
  margin: 3rem;
}.m-auto {
  margin: auto;
}.mx-auto {
  margin-left: auto;
  margin-right: auto;
}.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}.px-0 {
  padding-left: 0;
  padding-right: 0;
}.px-0_5 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.px-1 {
  padding-left: 1rem;
  padding-right: 1rem;
}.px-2 {
  padding-left: 2rem;
  padding-right: 2rem;
}.px-3 {
  padding-left: 3rem;
  padding-right: 3rem;
}.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}.py-0_5 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.py-0_8 {
  padding-top: 0.8rem;
  padding-bottom: 0.8rem;
}.py-1 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.py-2 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}.py-3 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.pl-0 {
  padding-left: 0;
}.pl-0_5 {
  padding-left: 0.5rem;
}.pl-1 {
  padding-left: 1rem;
}.pl-2 {
  padding-left: 2rem;
}.pl-3 {
  padding-left: 3rem;
}.pb-0 {
  padding-bottom: 0;
}.pb-0_5 {
  padding-bottom: 0.5rem;
}.pb-1 {
  padding-bottom: 1rem;
}.pb-2 {
  padding-bottom: 2rem;
}.pb-3 {
  padding-bottom: 3rem;
}.pr-0 {
  padding-right: 0;
}.pr-0_5 {
  padding-right: 0.5rem;
}.pr-1 {
  padding-right: 1rem;
}.pr-2 {
  padding-right: 2rem;
}.pr-3 {
  padding-right: 3rem;
}.pt-0 {
  padding-top: 0;
}.pt-0_5 {
  padding-top: 0.5rem;
}.pt-1 {
  padding-top: 1rem;
}.pt-2 {
  padding-top: 2rem;
}.pt-3 {
  padding-top: 3rem;
}.p-0 {
  padding: 0;
}.p-0_5 {
  padding: 0.5rem;
}.p-1 {
  padding: 1rem;
}.p-2 {
  padding: 2rem;
}.p-3 {
  padding: 3rem;
}.w-100 {
  width: 100%;
}.max-w-100 {
  max-width: 100%;
}.h-100 {
  height: 100%;
}.max-h-90-vh {
  max-height: 90vh;
}.max-h-100 {
  max-height: 100%;
}.min-width-4_4-rem {
  min-width: 4.4rem;
}.min-w-19-rem {
  min-width: 19rem;
}.min-height-4_4-rem {
  min-height: 4.4rem;
}.max-w-19-rem {
  max-width: 19rem;
}.max-w-34-rem {
  max-width: 34rem;
}.max-w-38-rem {
  max-width: 38rem;
}.max-w-42-rem {
  max-width: 42rem;
}.max-w-50-rem {
  max-width: 50rem;
}.max-w-54-rem {
  max-width: 54rem;
}.token-logo {
  background: var(--blue-300);
  width: 1.5rem;
  height: 1.5rem;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  overflow: hidden;
}.token-logo img,
.token-logo svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/*# sourceMappingURL=styles.css.map*/