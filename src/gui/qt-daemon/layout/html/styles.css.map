{"version": 3, "file": "styles.css", "mappings": ";;;AAAA,mBAAmB,eAAe,CAAC,cAAc,CAAC,gDAAgD,CAAC,oCAAoC,aAAa,CAAC,oCAAoC,cAAc,CAAC,+FAA+F,uDAAuD,CAAC,qBAAqB,CAAC,eAAe,CAAC,yFAAyF,uDAAuD,CAAC,qBAAqB,CAAC,eAAe,CAAC,uGAAuG,uDAAuD,CAAC,qBAAqB,CAAC,eAAe,CAAC,uGAAuG,uDAAuD,CAAC,qBAAqB,CAAC,eAAe,CAAC,mDAAmD,oEAAoE,CAAC,eAAe,CAAC,mDAAmD,oEAAoE,CAAC,eAAe,CAAC,0FAA0F,uDAAuD,CAAC,qBAAqB,CAAC,4FAA4F,uDAAuD,CAAC,qBAAqB,CAAC,sGAAsG,eAAe,CAAC,gFAAgF,uDAAuD,CAAC,qBAAqB,CAAC,8CAA8C,yDAAyD,CAAC,sBAAsB,CAAC,eAAe,CAAC,8CAA8C,uDAAuD,CAAC,sBAAsB,CAAC,eAAe,CAAC,8CAA8C,uDAAuD,CAAC,uBAAuB,CAAC,eAAe,CAAC,8CAA8C,uDAAuD,CAAC,qBAAqB,CAAC,eAAe,CAAC,4BAA4B,uDAAuD,CAAC,qBAAqB,CAAC,4GAA4G,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,mBAAmB,gDAAgD,CAAC,UAAU,gDAAgD,CAAC,gBAAgB,cAAc,CAAC,eAAe,CAAC,iCAAiC,cAAc,CAAC,qCAAqC,cAAc,CAAC,cAAc,gDAAgD,CAAC,yCAAyC,gBAAgB,CAAC,UAAU,cAAc,CAAC,eAAe,CAAC,+EAA+E,cAAc,CAAC,WAAW,gDAAgD,CAAC,iBAAiB,cAAc,CAAC,eAAe,CAAC,2BAA2B,cAAc,CAAC,cAAc,gDAAgD,CAAC,mBAAmB,cAAc,CAAC,qDAAqD,cAAc,CAAC,eAAe,CAAC,8BAA8B,cAAc,CAAC,eAAe,CAAC,kBAAkB,uDAAuD,CAAC,qBAAqB,CAAC,4BAA4B,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,6BAA6B,uDAAuD,CAAC,qBAAqB,CAAC,gBAAgB,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,gDAAgD,CAAC,qBAAqB,CAAC,wBAAwB,wBAAwB,CAAC,kEAAkE,cAAc,CAAC,iBAAiB,CAAC,gFAAgF,YAAY,CAAC,WAAW,CAAC,oGAAoG,cAAc,CAAC,iBAAiB,CAAC,sBAAsB,cAAc,CAAC,uCAAuC,CAAC,kLAAkL,4CAA4C,CAAC,qBAAqB,CAAC,yHAAyH,4CAA4C,CAAC,qBAAqB,CAAC,8BAA8B,cAAc,CAAC,oBAAoB,CAAC,sBAAsB,aAAa,CAAC,0BAA0B,gBAAgB,CAAC,kCAAkC,aAAa,CAAC,wBAAwB,CAAC,+BAA+B,CAAC,0DAA0D,qBAAqB,CAAC,wDAAwD,iBAAiB,CAAC,oPAAoP,mFAAmF,CAAC,qBAAqB,CAAC,iKAAiK,qFAAqF,CAAC,qBAAqB,CAAC,0JAA0J,qFAAqF,CAAC,qBAAqB,CAAC,wDAAwD,aAAa,CAAC,4DAA4D,aAAa,CAAC,oEAAoE,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,oPAAoP,4CAA4C,CAAC,iKAAiK,4CAA4C,CAAC,0JAA0J,2CAA2C,CAAC,CAAC,sDAAsD,uBAAuB,CAAC,sDAAsD,aAAa,CAAC,iBAAiB,CAAC,gPAAgP,4CAA4C,CAAC,qBAAqB,CAAC,wJAAwJ,4CAA4C,CAAC,qBAAqB,CAAC,yDAAyD,mBAAmB,CAAC,yDAAyD,aAAa,CAAC,kBAAkB,CAAC,sPAAsP,4CAA4C,CAAC,qBAAqB,CAAC,2JAA2J,4CAA4C,CAAC,qBAAqB,CAAC,4CAA4C,cAAc,CAAC,gEAAgE,kBAAkB,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,qBAAqB,CAAC,8FAA8F,cAAc,CAAC,wBAAwB,oBAAoB,CAAC,eAAe,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,4DAA4D,gDAAgD,CAAC,cAAc,CAAC,kBAAkB,gDAAgD,CAAC,YAAY,gDAAgD,CAAC,oBAAoB,cAAc,CAAC,0BAA0B,gDAAgD,CAAC,6BAA6B,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,8CAA8C,gDAAgD,CAAC,gBAAgB,cAAc,CAAC,eAAe,CAAC,0BAA0B,kBAAkB,CAAC,sBAAsB,cAAc,CAAC,yBAAyB,cAAc,CAAC,eAAe,CAAC,eAAe,gDAAgD,CAAC,6BAA6B,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,6GAA6G,uDAAuD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,aAAa,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,kBAAkB,CAAC,qBAAqB,cAAc,CAAC,eAAe,CAAC,kBAAkB,CAAC,eAAe,gDAAgD,CAAC,iBAAiB,gDAAgD,CAAC,8BAA8B,cAAc,CAAC,wCAAwC,kBAAkB,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,qBAAqB,CAAC,uDAAuD,cAAc,CAAC,gCAAgC,cAAc,CAAC,0CAA0C,kBAAkB,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,qBAAqB,CAAC,yDAAyD,cAAc,CAAC,8BAA8B,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,qCAAqC,cAAc,CAAC,+CAA+C,kBAAkB,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,qBAAqB,CAAC,8DAA8D,cAAc,CAAC,uCAAuC,cAAc,CAAC,iDAAiD,kBAAkB,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,qBAAqB,CAAC,gEAAgE,cAAc,CAAC,qCAAqC,gDAAgD,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,gDAAgD,CAAC,cAAc,CAAC,oBAAoB,uDAAuD,CAAC,qBAAqB,CAAC,qBAAqB,gDAAgD,CAAC,cAAc,CAAC,4BAA4B,aAAa,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,UAAU,gDAAgD,CAAC,qCAAqC,eAAe,CAAC,cAAc,CAAC,YAAY,eAAe,CAAC,iBAAiB,CAAC,wBAAwB,uBAAuB,CAAC,iCAAiC,gBAAgB,CAAC,oBAAoB,iBAAiB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,2DAA2D,CAAC,0BAA0B,CAAC,8CAA8C,YAAY,CAAC,qBAAqB,QAAQ,CAAC,kBAAkB,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,MAAM,CAAC,+BAA+B,SAAS,CAAC,OAAO,CAAC,mDAAmD,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,uBAAuB,cAAc,CAAC,YAAY,CAAC,6BAA6B,YAAY,CAAC,4BAA4B,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,iBAAiB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,CAAC,sBAAsB,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,yCAAyC,CAAC,yDAAyD,CAAC,SAAS,CAAC,mDAAmD,SAAS,CAAC,6EAA6E,UAAU,CAAC,2BAA2B,0BAA0B,CAAC,kCAAkC,mDAAmD,CAAC,iBAAiB,CAAC,SAAS,CAAC,+DAA+D,SAAS,CAAC,kBAAkB,CAAC,qCAAqC,eAAe,CAAC,6CAA6C,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,aAAa,CAAC,cAAc,CAAC,wBAAwB,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,+BAA+B,WAAW,CAAC,yCAAyC,wBAAwB,CAAC,iCAAiC,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,iDAAiD,wBAAwB,CAAC,iCAAiC,CAAC,mBAAmB,CAAC,yCAAyC,IAAI,CAAC,CAAC,uCAAuC,IAAI,CAAC,CAAC,oDAAoD,8CAA8C,CAAC,0DAA0D,4CAA4C,CAAC,qBAAqB,iBAAiB,CAAC,6BAA6B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,gDAAgD,CAAC,uJAAuJ,CAAC,2DAA2D,CAAC,mCAAmC,UAAU,CAAC,0BAA0B,oCAAoC,CAAC,yBAAyB,iBAAiB,CAAC,iCAAiC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,oDAAoD,CAAC,mKAAmK,CAAC,+DAA+D,CAAC,uCAAuC,UAAU,CAAC,0BAA0B,wCAAwC,CAAC,oBAAoB,qCAAqC,CAAC,YAAY,UAAU,CAAC,wFAAwF,gCAAgC,CAAC,6EAA6E,gCAAgC,CAAC,uBAAuB,gCAAgC,CAAC,UAAU,CAAC,gCAAgC,0BAA0B,CAAC,gEAAgE,aAAa,CAAC,+DAA+D,aAAa,CAAC,6DAA6D,aAAa,CAAC,oBAAoB,0BAA0B,CAAC,2CAA2C,0BAA0B,CAAC,qBAAqB,0BAA0B,CAAC,4BAA4B,aAAa,CAAC,8BAA8B,aAAa,CAAC,0FAA0F,kBAAkB,CAAC,wJAAwJ,kBAAkB,CAAC,oFAAoF,kBAAkB,CAAC,0HAA0H,kBAAkB,CAAC,oBAAoB,wBAAwB,CAAC,UAAU,CAAC,kBAAkB,qHAAqH,CAAC,kBAAkB,sHAAsH,CAAC,kBAAkB,sHAAsH,CAAC,kBAAkB,sHAAsH,CAAC,kBAAkB,uHAAuH,CAAC,kBAAkB,uHAAuH,CAAC,kBAAkB,wHAAwH,CAAC,kBAAkB,wHAAwH,CAAC,kBAAkB,wHAAwH,CAAC,kBAAkB,wHAAwH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,yHAAyH,CAAC,mBAAmB,0HAA0H,CAAC,mBAAmB,0HAA0H,CAAC,mBAAmB,0HAA0H,CAAC,mBAAmB,0HAA0H,CAAC,mBAAmB,2HAA2H,CAAC,mBAAmB,2HAA2H,CAAC,mBAAmB,2HAA2H,CAAC,mBAAmB,2HAA2H,CAAC,mBAAmB,2HAA2H,CAAC,yBAAyB,YAAY,CAAC,wBAAwB,kBAAkB,CAAC,UAAU,CAAC,sDAAsD,uHAAuH,CAAC,8EAA8E,kBAAkB,CAAC,wGAAwG,UAAU,CAAC,WAAW,iBAAiB,CAAC,qBAAqB,gBAAgB,CAAC,qCAAqC,YAAY,CAAC,mBAAmB,iBAAiB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,oBAAoB,CAAC,eAAe,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,mFAAmF,eAAe,CAAC,oCAAoC,cAAc,CAAC,oCAAoC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,oDAAoD,QAAQ,CAAC,oDAAoD,WAAW,CAAC,qDAAqD,UAAU,CAAC,+DAA+D,SAAS,CAAC,WAAW,CAAC,oDAAoD,WAAW,CAAC,8DAA8D,UAAU,CAAC,UAAU,CAAC,uEAAuE,SAAS,CAAC,iFAAiF,SAAS,CAAC,UAAU,CAAC,sEAAsE,UAAU,CAAC,gFAAgF,UAAU,CAAC,SAAS,CAAC,qCAAqC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,qDAAqD,SAAS,CAAC,qDAAqD,YAAY,CAAC,sDAAsD,UAAU,CAAC,gEAAgE,SAAS,CAAC,WAAW,CAAC,qDAAqD,WAAW,CAAC,+DAA+D,UAAU,CAAC,UAAU,CAAC,wEAAwE,UAAU,CAAC,kFAAkF,SAAS,CAAC,WAAW,CAAC,uEAAuE,WAAW,CAAC,iFAAiF,UAAU,CAAC,UAAU,CAAC,oCAAoC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,oDAAoD,SAAS,CAAC,oDAAoD,YAAY,CAAC,qDAAqD,UAAU,CAAC,+DAA+D,SAAS,CAAC,WAAW,CAAC,oDAAoD,WAAW,CAAC,8DAA8D,UAAU,CAAC,UAAU,CAAC,uEAAuE,UAAU,CAAC,iFAAiF,SAAS,CAAC,WAAW,CAAC,sEAAsE,WAAW,CAAC,gFAAgF,UAAU,CAAC,UAAU,CAAC,mBAAmB,UAAU,CAAC,kBAAkB,CAAC,6CAA6C,iBAAiB,CAAC,eAAe,CAAC,qCAAqC,kBAAkB,CAAC,qBAAqB,CAAC,mCAAmC,UAAU,CAAC,kBAAkB,CAAC,uCAAuC,kBAAkB,CAAC,0BAA0B,CAAC,4BAA4B,0HAA0H,CAAC,kBAAkB,CAAC,UAAU,CAAC,iDAAiD,aAAa,CAAC,wBAAwB,CAAC,qFAAqF,aAAa,CAAC,kFAAkF,aAAa,CAAC,4EAA4E,aAAa,CAAC,glBAAglB,0BAA0B,CAAC,mKAAmK,wBAAwB,CAAC,gKAAgK,wBAAwB,CAAC,0JAA0J,wBAAwB,CAAC,2LAA2L,8BAA8B,CAAC,6GAA6G,UAAU,CAAC,6BAA6B,CAAC,0BAA0B,eAAe,CAAC,8CAA8C,kCAAkC,CAAC,2DAA2D,UAAU,CAAC,wBAAwB,CAAC,2GAA2G,UAAU,CAAC,uGAAuG,qBAAqB,CAAC,+FAA+F,UAAU,CAAC,4vBAA4vB,0BAA0B,CAAC,2GAA2G,wBAAwB,CAAC,uGAAuG,wBAAwB,CAAC,+FAA+F,wBAAwB,CAAC,4vBAA4vB,sCAAsC,CAAC,2LAA2L,qCAAqC,CAAC,uLAAuL,+BAA+B,CAAC,+KAA+K,qCAAqC,CAAC,iGAAiG,qHAAqH,CAAC,iDAAiD,sHAAsH,CAAC,kFAAkF,wHAAwH,CAAC,qEAAqE,qHAAqH,CAAC,mFAAmF,wHAAwH,CAAC,qJAAqJ,yHAAyH,CAAC,2HAA2H,qHAAqH,CAAC,mHAAmH,sHAAsH,CAAC,6KAA6K,eAAe,CAAC,mBAAmB,0BAA0B,CAAC,oDAAoD,sCAAsC,CAAC,uCAAuC,UAAU,CAAC,kBAAkB,CAAC,wEAAwE,qBAAqB,CAAC,mFAAmF,6BAA6B,CAAC,6FAA6F,gBAAgB,CAAC,8BAA8B,CAAC,8GAA8G,gBAAgB,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,2BAA2B,wBAAwB,CAAC,0BAA0B,CAAC,iEAAiE,UAAU,CAAC,4BAA4B,0BAA0B,CAAC,qBAAqB,CAAC,kEAAkE,kBAAkB,CAAC,sDAAsD,wBAAwB,CAAC,iHAAiH,wBAAwB,CAAC,wEAAwE,gBAAgB,CAAC,UAAU,kBAAkB,CAAC,UAAU,CAAC,wCAAwC,sHAAsH,CAAC,sDAAsD,qHAAqH,CAAC,mBAAmB,0BAA0B,CAAC,oBAAoB,iCAAiC,CAAC,wBAAwB,YAAY,CAAC,6BAA6B,yBAAyB,CAAC,wBAAwB,wBAAwB,CAAC,4HAA4H,wBAAwB,CAAC,0HAA0H,wBAAwB,CAAC,sHAAsH,wBAAwB,CAAC,gJAAgJ,wBAAwB,CAAC,sEAAsE,oBAAoB,CAAC,2CAA2C,0BAA0B,CAAC,kCAAkC,qBAAqB,CAAC,mKAAmK,kBAAkB,CAAC,iKAAiK,kBAAkB,CAAC,6JAA6J,kBAAkB,CAAC,4BAA4B,wBAAwB,CAAC,UAAU,CAAC,6CAA6C,UAAU,CAAC,UAAU,CAAC,2DAA2D,sHAAsH,CAAC,2EAA2E,WAAW,CAAC,8CAA8C,UAAU,CAAC,mCAAmC,eAAe,CAAC,0DAA0D,wBAAwB,CAAC,UAAU,CAAC,2EAA2E,UAAU,CAAC,UAAU,CAAC,8EAA8E,qCAAqC,CAAC,uDAAuD,wBAAwB,CAAC,UAAU,CAAC,wEAAwE,UAAU,CAAC,UAAU,CAAC,2EAA2E,qCAAqC,CAAC,yDAAyD,wBAAwB,CAAC,qBAAqB,CAAC,0EAA0E,qBAAqB,CAAC,UAAU,CAAC,6EAA6E,+BAA+B,CAAC,WAAW,kBAAkB,CAAC,uJAAuJ,kBAAkB,CAAC,wFAAwF,yCAAyC,CAAC,iBAAiB,0BAA0B,CAAC,2BAA2B,UAAU,CAAC,oBAAoB,SAAS,CAAC,+HAA+H,UAAU,CAAC,0CAA0C,gCAAgC,CAAC,oDAAoD,0BAA0B,CAAC,gEAAgE,UAAU,CAAC,0BAA0B,CAAC,0IAA0I,0BAA0B,CAAC,yDAAyD,0BAA0B,CAAC,8BAA8B,2BAA2B,CAAC,uGAAuG,iCAAiC,CAAC,mIAAmI,iCAAiC,CAAC,oCAAoC,6BAA6B,CAAC,uFAAuF,6BAA6B,CAAC,8GAA8G,4FAA4F,CAAC,8GAA8G,2FAA2F,CAAC,6IAA6I,kBAAkB,CAAC,sIAAsI,kBAAkB,CAAC,4BAA4B,wBAAwB,CAAC,UAAU,CAAC,wDAAwD,mCAAmC,CAAC,oDAAoD,+BAA+B,CAAC,2TAA2T,mCAAmC,CAAC,qBAAqB,6KAA6K,mCAAmC,CAAC,CAAC,wBAAwB,uHAAuH,CAAC,wBAAwB,CAAC,UAAU,CAAC,uEAAuE,+BAA+B,CAAC,6JAA6J,6BAA6B,CAAC,oLAAoL,8FAA8F,CAAC,oLAAoL,6FAA6F,CAAC,mNAAmN,kBAAkB,CAAC,4MAA4M,kBAAkB,CAAC,+DAA+D,wBAAwB,CAAC,qBAAqB,CAAC,2FAA2F,qCAAqC,CAAC,uFAAuF,0CAA0C,CAAC,iYAAiY,qCAAqC,CAAC,qBAAqB,gNAAgN,qCAAqC,CAAC,CAAC,qEAAqE,6BAA6B,CAAC,yJAAyJ,6BAA6B,CAAC,gLAAgL,4FAA4F,CAAC,gLAAgL,2FAA2F,CAAC,+MAA+M,kBAAkB,CAAC,wMAAwM,kBAAkB,CAAC,6DAA6D,wBAAwB,CAAC,UAAU,CAAC,yFAAyF,mCAAmC,CAAC,qFAAqF,+BAA+B,CAAC,6XAA6X,mCAAmC,CAAC,qBAAqB,8MAA8M,mCAAmC,CAAC,CAAC,8BAA8B,2HAA2H,CAAC,8BAA8B,aAAa,CAAC,yCAAyC,aAAa,CAAC,uCAAuC,aAAa,CAAC,sCAAsC,0BAA0B,CAAC,sBAAsB,2HAA2H,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,sCAAsC,CAAC,sBAAsB,wCAAwC,CAAC,qBAAqB,kBAAkB,CAAC,UAAU,CAAC,mDAAmD,sHAAsH,CAAC,gBAAgB,sCAAsC,CAAC,mSAAmS,gCAAgC,CAAC,oBAAoB,oGAAoG,kBAAkB,CAAC,CAAC,kCAAkC,UAAU,CAAC,wEAAwE,0BAA0B,CAAC,gDAAgD,0BAA0B,CAAC,0KAA0K,aAAa,CAAC,4BAA4B,WAAW,CAAC,yCAAyC,WAAW,CAAC,sBAAsB,0BAA0B,CAAC,UAAU,0BAA0B,CAAC,kDAAkD,aAAa,CAAC,6DAA6D,aAAa,CAAC,2DAA2D,aAAa,CAAC,6CAA6C,aAAa,CAAC,uBAAuB,qBAAqB,CAAC,mDAAmD,wBAAwB,CAAC,8DAA8D,wBAAwB,CAAC,4DAA4D,wBAAwB,CAAC,6GAA6G,aAAa,CAAC,wHAAwH,aAAa,CAAC,sHAAsH,aAAa,CAAC,6DAA6D,aAAa,CAAC,qKAAqK,aAAa,CAAC,uIAAuI,wBAAwB,CAAC,WAAW,aAAa,CAAC,wDAAwD,0BAA0B,CAAC,4CAA4C,0BAA0B,CAAC,4DAA4D,qCAAqC,CAAC,oFAAoF,qHAAqH,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,8DAA8D,qCAAqC,CAAC,sFAAsF,qHAAqH,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,qDAAqD,qCAAqC,CAAC,6EAA6E,sCAAsC,CAAC,kEAAkE,qCAAqC,CAAC,8EAA8E,0BAA0B,CAAC,0FAA0F,8BAA8B,CAAC,2DAA2D,0BAA0B,CAAC,iEAAiE,UAAU,CAAC,6EAA6E,aAAa,CAAC,wFAAwF,aAAa,CAAC,sFAAsF,aAAa,CAAC,+GAA+G,aAAa,CAAC,iFAAiF,0BAA0B,CAAC,mFAAmF,2BAA2B,CAAC,sBAAsB,aAAa,CAAC,qBAAqB,aAAa,CAAC,mBAAmB,aAAa,CAAC,oEAAoE,0BAA0B,CAAC,wHAAwH,0BAA0B,CAAC,mBAAmB,mBAAmB,CAAC,gCAAgC,0BAA0B,CAAC,qCAAqC,0BAA0B,CAAC,8CAA8C,0BAA0B,CAAC,yCAAyC,0BAA0B,CAAC,yDAAyD,qBAAqB,CAAC,kEAAkE,qBAAqB,CAAC,8CAA8C,mBAAmB,CAAC,uFAAuF,mBAAmB,CAAC,2FAA2F,aAAa,CAAC,8BAA8B,UAAU,CAAC,gCAAgC,UAAU,CAAC,8BAA8B,0BAA0B,CAAC,uCAAuC,sCAAsC,CAAC,0BAA0B,CAAC,gMAAgM,gCAAgC,CAAC,+GAA+G,gCAAgC,CAAC,gBAAgB,kBAAkB,CAAC,8CAA8C,uHAAuH,CAAC,eAAe,wBAAwB,CAAC,UAAU,CAAC,qHAAqH,0BAA0B,CAAC,yDAAyD,UAAU,CAAC,uLAAuL,gCAAgC,CAAC,eAAe,kBAAkB,CAAC,4DAA4D,0BAA0B,CAAC,kDAAkD,yBAAyB,CAAC,2BAA2B,CAAC,yCAAyC,yBAAyB,CAAC,uMAAuM,iCAAiC,CAAC,yBAAyB,eAAe,CAAC,6BAA6B,YAAY,CAAC,yBAAyB,wBAAwB,CAAC,8BAA8B,wBAAwB,CAAC,0DAA0D,YAAY,CAAC,sDAAsD,wBAAwB,CAAC,2DAA2D,wBAAwB,CAAC,wDAAwD,YAAY,CAAC,oDAAoD,wBAAwB,CAAC,yDAAyD,wBAAwB,CAAC,iDAAiD,cAAc,CAAC,uEAAuE,cAAc,CAAC,mEAAmE,cAAc,CAAC,wBAAwB,iCAAiC,CAAC,wEAAwE,oBAAoB,CAAC,2SAA2S,wBAAwB,CAAC,uEAAuE,oBAAoB,CAAC,uSAAuS,wBAAwB,CAAC,qEAAqE,oBAAoB,CAAC,+RAA+R,wBAAwB,CAAC,4IAA4I,iCAAiC,CAAC,wIAAwI,qCAAqC,CAAC,8DAA8D,0BAA0B,CAAC,sCAAsC,qBAAqB,CAAC,kBAAkB,UAAU,CAAC,wBAAwB,0BAA0B,CAAC,uCAAuC,0BAA0B,CAAC,kBAAkB,0BAA0B,CAAC,kBAAkB,kBAAkB,CAAC,gDAAgD,uHAAuH,CAAC,qEAAqE,gCAAgC,CAAC,0DAA0D,aAAa,CAAC,yDAAyD,aAAa,CAAC,uDAAuD,aAAa,CAAC,iEAAiE,aAAa,CAAC,kEAAkE,0BAA0B,CAAC,sBAAsB,wBAAwB,CAAC,UAAU,CAAC,YAAY,wBAAwB,CAAC,UAAU,CAAC,4BAA4B,wBAAwB,CAAC,kCAAkC,0HAA0H,CAAC,iBAAiB,4CAA4C,CAAC,gCAAgC,2CAA2C,CAAC,iBAAiB,CAAC,2BAA2B,2CAA2C,CAAC,iBAAiB,CAAC,0CAA0C,gBAAgB,CAAC,4CAA4C,CAAC,sCAAsC,qCAAqC,CAAC,sDAAsD,wBAAwB,CAAC,oDAAoD,sCAAsC,CAAC,kDAAkD,wBAAwB,CAAC,kEAAkE,wBAAwB,CAAC,gEAAgE,oCAAoC,CAAC,8DAA8D,wBAAwB,CAAC,+DAA+D,wBAAwB,CAAC,6DAA6D,oCAAoC,CAAC,2DAA2D,wBAAwB,CAAC,wDAAwD,qBAAqB,CAAC,wBAAwB,sHAAsH,CAAC,wBAAwB,CAAC,sBAAsB,qCAAqC,CAAC,6BAA6B,qCAAqC,CAAC,yIAAyI,wBAAwB,CAAC,qDAAqD,UAAU,CAAC,+CAA+C,mCAAmC,CAAC,sIAAsI,wBAAwB,CAAC,oDAAoD,qBAAqB,CAAC,8CAA8C,qCAAqC,CAAC,gIAAgI,wBAAwB,CAAC,kDAAkD,UAAU,CAAC,4CAA4C,mCAAmC,CAAC,oGAAoG,qCAAqC,CAAC,sKAAsK,qCAAqC,CAAC,mEAAmE,qCAAqC,CAAC,wDAAwD,sCAAsC,CAAC,0KAA0K,qBAAqB,CAAC,kMAAkM,qCAAqC,CAAC,wFAAwF,iCAAiC,CAAC,8BAA8B,CAAC,kMAAkM,iCAAiC,CAAC,0OAA0O,iCAAiC,CAAC,iDAAiD,iCAAiC,CAAC,yCAAyC,wIAAwI,CAAC,8IAA8I,CAAC,uCAAuC,yIAAyI,CAAC,mKAAmK,sCAAsC,CAAC,2CAA2C,cAAc,CAAC,oBAAoB,uBAAuB,eAAe,CAAC,CAAC,qEAAqE,0BAA0B,CAAC,gCAAgC,qCAAqC,CAAC,UAAU,CAAC,+HAA+H,wBAAwB,CAAC,UAAU,CAAC,2CAA2C,qBAAqB,CAAC,gKAAgK,wBAAwB,CAAC,qBAAqB,CAAC,yCAAyC,UAAU,CAAC,0JAA0J,wBAAwB,CAAC,UAAU,CAAC,4CAA4C,8BAA8B,CAAC,aAAa,CAAC,uDAAuD,UAAU,CAAC,sDAAsD,aAAa,CAAC,8CAA8C,wBAAwB,CAAC,mCAAmC,uCAAuC,CAAC,0GAA0G,sCAAsC,CAAC,+BAA+B,WAAW,CAAC,+FAA+F,iBAAiB,CAAC,mCAAmC,SAAS,CAAC,YAAY,CAAC,mJAAmJ,QAAQ,CAAC,gEAAgE,QAAQ,CAAC,uBAAuB,aAAa,CAAC,iCAAiC,6CAA6C,CAAC,+FAA+F,0CAA0C,CAAC,kBAAkB,CAAC,6BAA6B,UAAU,CAAC,+DAA+D,0BAA0B,CAAC,mCAAmC,iBAAiB,CAAC,uEAAuE,iCAAiC,CAAC,gGAAgG,kBAAkB,CAAC,eAAe,CAAC,grBAAgrB,mCAAmC,CAAC,kFAAkF,wBAAwB,CAAC,gVAAgV,qBAAqB,CAAC,wqBAAwqB,qCAAqC,CAAC,gFAAgF,wBAAwB,CAAC,wUAAwU,gCAAgC,CAAC,wpBAAwpB,qCAAqC,CAAC,4EAA4E,wBAAwB,CAAC,wTAAwT,qBAAqB,CAAC,wwBAAwwB,mCAAmC,CAAC,gXAAgX,wBAAwB,CAAC,sSAAsS,UAAU,CAAC,0WAA0W,0BAA0B,CAAC,8tBAA8tB,iBAAiB,CAAC,wdAAwd,iBAAiB,CAAC,UAAU,CAAC,weAAwe,qBAAqB,CAAC,WAAW,CAAC,gwBAAgwB,qCAAqC,CAAC,0WAA0W,wBAAwB,CAAC,kSAAkS,qBAAqB,CAAC,sWAAsW,oBAAoB,CAAC,stBAAstB,4BAA4B,CAAC,odAAod,iBAAiB,CAAC,UAAU,CAAC,keAAke,qBAAqB,CAAC,WAAW,CAAC,gvBAAgvB,qCAAqC,CAAC,8VAA8V,wBAAwB,CAAC,0RAA0R,UAAU,CAAC,8VAA8V,0BAA0B,CAAC,ssBAAssB,iBAAiB,CAAC,4cAA4c,iBAAiB,CAAC,UAAU,CAAC,sdAAsd,qBAAqB,CAAC,WAAW,CAAC,aAAa,kBAAkB,CAAC,UAAU,CAAC,yBAAyB,kBAAkB,CAAC,UAAU,CAAC,wBAAwB,kBAAkB,CAAC,qBAAqB,CAAC,sBAAsB,kBAAkB,CAAC,UAAU,CAAC,4HAA4H,6BAA6B,CAAC,4MAA4M,aAAa,CAAC,gCAAgC,wBAAwB,CAAC,2BAA2B,eAAe,CAAC,yCAAyC,WAAW,CAAC,yBAAyB,2BAA2B,eAAe,CAAC,yCAAyC,WAAW,CAAC,CAAC,aAAa,4BAA4B,CAAC,UAAU,kBAAkB,CAAC,qCAAqC,UAAU,CAAC,eAAe,eAAe,CAAC,yBAAyB,qBAAqB,CAAC,kBAAkB,CAAC,wHAAwH,CAAC,4BAA4B,aAAa,CCA778E;EACI;EACA;EACA;EACA;ACEJ,CDCA;;;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAIA;ACEJ,CDCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiFI;EACA;EACA;EACA;ACEJ,CDCA;;;;;;;;;;;EAWI;ACEJ,CDCA;EACI;EACA;ACEJ,CDCA;;EAEI;ACEJ,CDCA;;EAEI;ACEJ,CDCA;;;;EAII;ACEJ,CDCA;EACI;EACA;ACEJ,CDCA;;EAEI;ACEJ,CDCA;EACI;ACEJ,CDAI;EACI;ACER,CDEA;;;;EAII;EACA;EACA;ACCJ,CDEA;;EAEI;ACCJ,CDEA;;EAEI;EACA;ACCJ,CDEA;EACI;ACCJ,CDEA;EACI;EACA;ACCJ,CDEA;;EAEI;ACCJ,CDEA;EACI;ACCJ,CDCI;EAGI;ACDR,CDKA;EACI;ACFJ,CDKA;;EAEI;ACFJ,CCpNI;EACI;ADuNR,CEzNA;EAEI;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EAGA;EACA;EACA;EACA;EACA;EACA;EAGA;EAGA;EACA;EAGA;EAGA;EAGA;EAGA;EAGA;EAEA;EACA;EAGA;EACA;EAGA;EACA;AFkMJ,CG3PA;EACI;AH8PJ,CG3PA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA;EAGA;EAGA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;;;;GAAA;EAKA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EAEA;EACA;EAGA;EACA;EAGA;EACA;EACA;EAGA;EAGA;AHiOJ,CG/NA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA;EAGA;EAGA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EAEA;EACA;EAGA;EACA;EAGA;EACA;EACA;EAGA;EAGA;AHqMJ,CIjZA;EACI;EACA;EACA;AJoZJ,CIjZA;EACI;EACA;EACA;AJmZJ,CIhZA;EACI;EACA;EACA;AJkZJ,CKjaA;;;;;EAKI;EACA;EACA;ALmaJ,CKhaA;EACI;EACA;ALmaJ,CKhaA;EACI;EACA;ALmaJ,CKhaA;EACI;EACA;ALmaJ,CKhaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4EI;EACA;ALmaJ,CMzgBA;EACI;EACA;EACA;EACA;AN4gBJ,CMzgBA;EACI;AN4gBJ,CMzgBA;EACI;EACA;EACA;EACA;AN4gBJ,CMzgBA;EACI;AN4gBJ,CMxgBI;EACI;AN2gBR,CMvgBA;EACI;EACA;EACA;EACA;AN0gBJ,CMxgBI;EACI;AN0gBR,CMvgBI;EACI;ANygBR,COhjBA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;APmjBJ,CQ5jBA;;EAEI;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;AR6jBJ,CQ1jBA;EACI;EACA;AR6jBJ,CQ1jBA;EACI;EACA;EAEA;EACA;AR4jBJ,CQzjBA;EACI;EACA;EACA;EACA;EACA;AR4jBJ,CQ1jBI;EACI;EACA;EACA;EACA;EACA;AR4jBR,CQ1jBQ;;EAEI;EACA;AR4jBZ,CQxjBI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AR0jBR,CSnnBA;EACI;EACA;EACA;ATsnBJ,CSnnBA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;ATsnBJ,CSpnBI;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;ATqnBR,CSnnBQ;EACI;ATqnBZ,CSjnBI;EACI;EACA;EACA;EACA;ATmnBR,CS/mBQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ATinBZ,CS5mBA;EACI;EACA;EACA;EACA;EACA;AT+mBJ,CS7mBI;EACI;AT+mBR,CS9mBQ;EACI;EACA;EACA;EACA;EACA;EACA;ATgnBZ,CS7mBQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AT+mBZ,CS1mBA;EACI;EACA;EACA;AT6mBJ,CS1mBA;EACI;EACA;AT6mBJ,CU7sBA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVgtBJ,CU9sBI;;EACI;AVitBR,CU9sBI;;EACI;AVitBR,CU9sBI;;EACI;EACA;AVitBR,CU9sBI;;;EAEI;EACA;AVitBR,CU/sBQ;;;EACI;AVmtBZ,CUhtBQ;;;EACI;AVotBZ,CUhtBI;;EACI;EACA;AVmtBR,CUhtBY;;;;EAGI;AVmtBhB,CU9sBI;;EACI;AVitBR,CU9sBY;;;;EAGI;AVitBhB,CU5sBI;;EACI;EACA;AV+sBR,CU7sBQ;;EACI;EACA;AVgtBZ,CU7sBQ;;EACI;EACA;AVgtBZ,CU5sBI;;EACI;EACA;AV+sBR,CU7sBQ;;EACI;AVgtBZ,CU1sBQ;;;EAEI;AV6sBZ,CUxsBA;EACI;AV2sBJ,CUzsBI;EACI;AV2sBR,CUlsBgB;;;;EAGI;AVssBpB,CUhsBY;;EACI;AVmsBhB,CW7zBI;EACI;EACA;EACA;EACA;AXg0BR,CW7zBI;EACI;EACA;EACA;EACA;AX+zBR,CW5zBI;EACI;EACA;EACA;EACA;EACA;AX8zBR,CW5zBQ;EACI;EACA;AX8zBZ,CYt1BA;EACI;AZy1BJ,CYv1BI;EACI;EACA;EACA;EACA;EACA;AZy1BR,CYt1BI;EACI;EACA;EACA;EACA;AZw1BR,CYv1BQ;EACI;EACA;AZy1BZ,CYt1BQ;EACI;EACA;EACA;AZw1BZ,CYn1BA;EACI;EACA;EACA;AZs1BJ,CYp1BQ;;EAEI;AZs1BZ,CYn1BQ;EACI;EACA;EACA;EACA;AZq1BZ,CYp1BY;EACI;EACA;EACA;AZs1BhB,CYh1BA;EACI;EACA;EACA;AZm1BJ,CYj1BI;EACI;EACA;EACA;EACA;EACA;AZm1BR,CYl1BQ;EACI;AZo1BZ,CYh1BI;EACI;AZk1BR,Cax5BA;EACI;EACA;EACA;Ab25BJ,Caz5BI;EACI;Ab25BR,Cax5BI;EACI;EACA;EACA;EACA;EACA;EACA;Ab05BR,Cav5BI;EACI;EACA;EACA;EACA;EACA;Aby5BR,Cat5BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;Abw5BR,Cat5BQ;EACI;EAaA;Ab44BZ,Cax5BY;EACI;EACA;Ab05BhB,Cat5BgB;EAEI;Abu5BpB,Cal5BY;EACI;Abo5BhB,Cah5BQ;;EAEI;EACA;Abk5BZ,Ca/4BQ;EACI;EACA;Abi5BZ,Ca/4BY;;EAEI;EACA;Abi5BhB,Ca/4BgB;;EACI;Abk5BpB,Ca/4BgB;;EACI;Abk5BpB,Ca74BQ;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Ab84BZ,Ca54BY;EACI;EACA;Ab84BhB,Ca34BY;EACI;Ab64BhB,Caz4BgB;EACI;Ab24BpB,Cav4BY;EACI;Aby4BhB,Cat4BY;EAEI;Abu4BhB,Car4BgB;EACI;Abu4BpB,Caj4BoB;EACI;Abm4BxB,Ca/3BgB;EACI;Abi4BpB,Ca/3BoB;EAEI;Abg4BxB,Ca93BwB;EACI;Abg4B5B,Caz3BQ;EACI;EACA;Ab23BZ,Caz3BY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAiBA;EAMA;Abs2BhB,Ca33BgB;EACI;EACA;Ab63BpB,Caz3BoB;EACI;Ab23BxB,Cav3BgB;EACI;Aby3BpB,Car3BgB;EACI;EACA;Abu3BpB,Can3BgB;EACI;Abq3BpB,Caj3BoB;EACI;Abm3BxB,Ca/2BgB;EACI;Abi3BpB,Ca/2BoB;EACI;Abi3BxB,Ca32BQ;;;EAGI;EACA;EACA;EACA;Ab62BZ,Ca12BQ;EACI;EACA;EACA;Ab42BZ,Caz2BQ;EACI;Ab22BZ,Caz2BY;;;EAGI;EACA;EACA;Ab22BhB,Caz2BgB;;;EACI;Ab62BpB,Caz2BY;EACI;EACA;EACA;Ab22BhB,Cav2BQ;EACI;Aby2BZ,Cat2BQ;EACI;Abw2BZ,Car2BQ;EACI;Abu2BZ,Car2BY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Abu2BhB,Caj2BQ;EACI;EACA;EACA;EACA;EACA;Abm2BZ,Caj2BY;EACI;Abm2BhB,Ca/1BQ;EACI;EACA;EACA;EACA;EACA;Abi2BZ,Ca51BA;EACI;EACA;EACA;EACA;EACA;Ab+1BJ,Ca71BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;Ab+1BR,Ca31BQ;EACI;Ab61BZ,Ca11BQ;EACI;Ab41BZ,Cax1BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Ab01BR,Cax1BQ;EACI;Ab01BZ,Cav1BQ;EAEI;EACA;EACA;EACA;EACA;Abw1BZ,Car1BQ;EACI;EACA;EACA;EACA;EACA;Abu1BZ,Cap1BQ;EACI;Abs1BZ,Can1BQ;EACI;Abq1BZ,Caj1BY;EAEI;Abk1BhB,Ca/0BY;EACI;EACA;EACA;EACA;EACA;Abi1BhB,Ca50BY;EACI;Ab80BhB,Ca30BY;EACI;Ab60BhB,Cav0BA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Ab00BJ,Cax0BI;EACI;EACA;Ab00BR,Cav0BI;EACI;EACA;Aby0BR,Cat0BI;EACI;EACA;Abw0BR,Car0BI;EACI;EACA;EACA;EACA;EACA;Abu0BR,Cal0BI;EACI;Abq0BR,Cal0BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Abo0BR,Caj0BI;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;Abk0BR,Ca1zBY;;EACI;Ab8zBhB,Ca5zBgB;;;;EAEI;Abg0BpB,Ca9zBoB;;;;EACI;Abm0BxB,Cah0BoB;;;;EACI;Abq0BxB,Cc/xCI;EACI;EACA;EACA;EACA;AdkyCR,CchyCQ;EACI;EACA;EACA;EACA;AdkyCZ,Cc5xCA;EACI;Ad+xCJ,Cc7xCI;EACI;EACA;Ad+xCR,Cc3xCA;EACI;EACA;EACA;EACA;Ad8xCJ,Cc5xCI;EACI;EACA;Ad8xCR,Cc1xCI;EACI;Ad4xCR,CczxCI;EACI;Ad2xCR,CcxxCI;EACI;Ad0xCR,CcvxCI;EACI;AdyxCR,CcrxCI;EACI;AduxCR,CcpxCI;EACI;AdsxCR,CcnxCI;EACI;AdqxCR,CclxCI;EACI;AdoxCR,CcjxCI;EACI;AdmxCR,CchxCI;EACI;AdkxCR,Cc/wCI;EACI;AdixCR,Cc9wCI;EACI;AdgxCR,Cc7wCI;EACI;Ad+wCR,Cc5wCI;EACI;Ad8wCR,Cc3wCI;EACI;Ad6wCR,Cc1wCI;EACI;Ad4wCR,CczwCI;EACI;Ad2wCR,CcxwCI;EACI;Ad0wCR,CcvwCI;EACI;AdywCR,CctwCI;EACI;AdwwCR,CcrwCI;EACI;AduwCR,CcpwCI;EACI;AdswCR,CcnwCI;EACI;AdqwCR,CclwCI;EACI;AdowCR,CcjwCI;EACI;AdmwCR,CchwCI;EACI;AdkwCR,Cc/vCI;EACI;AdiwCR,Cc9vCI;EACI;AdgwCR,Cc7vCI;EACI;Ad+vCR,Cc5vCI;EACI;Ad8vCR,Cc3vCI;EACI;Ad6vCR,Cc1vCI;EACI;Ad4vCR,CczvCI;EACI;Ad2vCR,CcxvCI;EACI;Ad0vCR,CcvvCI;EACI;AdyvCR,CctvCI;EACI;AdwvCR,CcrvCI;EACI;AduvCR,CcpvCI;EACI;AdsvCR,CcnvCI;EACI;AdqvCR,CclvCI;EACI;AdovCR,CcjvCI;EACI;AdmvCR,CchvCI;EACI;AdkvCR,Cc/uCI;EACI;AdivCR,Cc9uCI;EACI;AdgvCR,Cc7uCI;EACI;Ad+uCR,Cc5uCI;EACI;Ad8uCR,Cc3uCI;EACI;Ad6uCR,Cc1uCI;EACI;Ad4uCR,CcxuCI;EACI;Ad0uCR,CctuCI;EACI;AdwuCR,CcruCI;EACI;AduuCR,CcpuCI;EACI;AdsuCR,CcnuCI;EACI;AdquCR,CcjuCI;EACI;AdmuCR,CchuCI;EACI;AdkuCR,Cc9tCI;EACI;AdguCR,Cc7tCI;EACI;Ad+tCR,Ce3+CA;EACI;EACA;EACA;EACA;EACA;EACA;Af8+CJ,Ce3+CA;EACI;IACI;Ef8+CN;Ee5+CE;IACI;Ef8+CN;AACF,CgB5/CI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AhB8/CR,CgB5/CQ;EACI;AhB8/CZ,CgB1/CI;EACI;AhB4/CR,CiB7gDA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AjBghDJ,CiB7gDA;EACI;EACA;EACA;EAEA;AjB+gDJ,CiB5gDA;EACI;AjB+gDJ,CiB5gDA;EACI;EACA;AjB+gDJ,CiB7gDI;EACI;EACA;AjB+gDR,CiB7gDQ;;EAEI;EACA;EACA;EACA;AjB+gDZ,CiB3gDI;EACI;EACA;EACA;AjB6gDR,CkB1jDI;EACI;EACA;EACA;AlB6jDR,CkB1jDI;EACI;EACA;EAEA;EACA;AlB2jDR,CkBxjDI;EACI;EACA;AlB0jDR,CkBvjDI;EACI;EACA;AlByjDR,CmBziDA;EACI;AnB4iDJ,CmBziDQ;EACI,+BAhCG;EAiCH,qCAnCO;AnB8kDnB,CmBziDY;EACI;AnB2iDhB,CmBxiDY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AnB0iDhB,CmBriDY;EACI;EACA;AnBuiDhB,CmBliDY;EACI;EACA;AnBoiDhB,CmB/hDY;EACI;EACA;AnBiiDhB,CmB5hDY;EACI;EACA;AnB8hDhB,CmBxhDQ;EACI,qCApFO;EAqFP,gBA5EW;AnBsmDvB,CmBthDI;EACI;AnBwhDR,CmBthDQ;EACI;AnBwhDZ,CmBthDY;EACI;EACA;AnBwhDhB,CmBnhDI;EACI;AnBqhDR,CmBlhDI;EACI;EACA,kCA9GiB;EA+GjB,qCA3GO;EA4GP,qBA7GkB;EA8GlB;EACA,gBArGW;EAsGX;EACA;AnBohDR,CmBjhDQ;EACI;EACA;AnBmhDZ,CmBhhDQ;EACI;EACA,kBAhHmB;AnBkoD/B,CmBtpDa;EAsIG,mBAlHe;EAmHf;AnBmhDhB,CmBhhDY;EACI,sBAzHQ;AnB4oDxB,CmB7gDQ;EACI,YA/HO;AnB8oDnB,CmB5gDgB;EACI;EACA;EACA,kBApIW;EAqIX;AnB8gDpB,CmB5gDoB;EACI;EACA;AnB8gDxB,CmB3qDa;EAiKW,mBA7IO;EA8IP;AnB6gDxB,CmBrgDQ;EACI;AnBwgDZ,CmBtgDY;EACI,kCAvKU;EAwKV;AnBwgDhB,CmBtgDgB;EACI;AnBwgDpB,CmBlgDY;EACI;EACA;AnBogDhB,CmB9rDa;EA4LO;EACA;AnBqgDpB,CmBlgDgB;EACI,iBA5KQ;EA6KR;EACA,kCA7LK;EA8LL,4CA/LE;EAgMF;EACA;AnBqgDpB,CmB3sDa;EAwMW;EACA;AnBsgDxB,CmBngDoB;EACI,kCAtME;AnB4sD1B,CmBpgDwB;EACI;AnBsgD5B,CmBttDa;EAkNmB;EACA;AnBugDhC,CmBlgDoB;EACI;EACA;AnBqgDxB,CmBlgDoB;EACI;EACA;AnBogDxB,CmBlgDwB;EACI;AnBogD5B,CmBjgDwB;EACI;AnBmgD5B,CmBzuDa;EAwOmB;EACA;AnBogDhC,CmBhgDwB;EACI;AnBmgD5B,CmBjvDa;EAgPmB;EACA;AnBogDhC,CmB9/CgB;EACI;AnBigDpB,CmBzvDa;EA0PW;AnBkgDxB,CmB//CoB;EACI,uBA9ND;EA+NC;AnBkgDxB,CmB9/CgB;EACI;EACA;EACA;AnBggDpB,CmBtwDa;EAwQW;EACA;AnBigDxB,CmB1/CI;EACI,8BAzQW;AnBswDnB,CmB3/CQ;EACI;AnB6/CZ,CmBz/CI;EACI;AnB2/CR,CmBpxDa;EA4RD;AnB2/CZ,CmBv/CI;EACI;EACA;AnB0/CR,CmB5xDa;EAoSD;EACA;AnB2/CZ,CmBv/CY;EACI,yCAlSG;AnB4xDnB,CmBt/CQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AnBw/CZ,CmBl/CQ;;;EAEI;AnBq/CZ,CmBh/CA;EACI,qCA1TW;EA2TX;EACA;EACA;AnBm/CJ,CmBj/CI;EACI;EACA,+BAlUkB;EAmUlB,8BAnUkB;EAoUlB,4CArUW;EAsUX;AnBm/CR,CmB/+CgB;EACI,+BA1UM;EA2UN,8BA3UM;AnB4zD1B,CmB3+CI;EACI;EACA;EACA,+BApVkB;EAqVlB,kCArVkB;EAsVlB,iCAtVkB;EAuVlB,4CAxVW;EAyVX;AnB6+CR,CmBz+CgB;EACI,+BA7VM;AnBw0D1B,CmBr+CI;EACI;EACA,kCArWkB;EAsWlB,iCAtWkB;EAuWlB,yCAxWW;EAyWX;AnBu+CR,CmBn+CgB;EACI,kCA7WM;EA8WN,iCA9WM;AnBm1D1B,CmB/9CI;EACI;EACA;EACA,8BAvXkB;EAwXlB,kCAxXkB;EAyXlB,iCAzXkB;EA0XlB,4CA3XW;EA4XX;AnBi+CR,CmB79CgB;EACI,8BAhYM;AnB+1D1B,CmBz9CI;EACI;EACA;AnB29CR,CmBx9CI;EACI;EACA;AnB09CR,CmBt9CQ;EACI;UAAA;EACA;EACA;EACA,uBApYuB;EAqYvB;AnBw9CZ,CmBt9CY;EACI;AnBw9ChB,CmBr9CY;EACI,4CAjaM;AnBw3DtB,CmBp9CY;EAEI,uBAjZmB;EAkZnB,4CAvaM;EAwaN;AnBq9ChB,CmBj9CQ;EACI,qCAxaG;EAyaH,uBAtZqB;EAuZrB;AnBm9CZ,CmBj9CY;EAEI,kCAlbS;EAmbT,4CApbM;AnBs4DtB,CmBh9CgB;EACI;AnBk9CpB,CmB98CY;EACI,4CA5bM;EA6bN,kCA5bS;AnB44DzB,CmB78CY;EACI,0CAxaqB;AnBu3DrC,CmB58CY;EACI;AnB88ChB,CmBx5Da;EA4cO;EACA;AnB+8CpB,CmB38CY;EACI;EACA;EACA;AnB88ChB,CmBl6Da;EAsdO;EACA;AnB+8CpB,CmBt6Da;EA8dL;EACA;AnB48CR,CmBr8CY;EACI;EACA;AnBw8ChB,CmBt8CgB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,sCAjdJ;EAkdI;AnBw8CpB,CmBp8CoB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,8CA9dJ;EA+dI;AnBs8CxB,CoBx8DI;EACI;EACA;EACA;ApB28DR,CoB18DQ;EACI;ApB48DZ,CoBx8DI;EACI;EACA;ApB08DR,CoBv8DI;;EAEI;EACA;EACA;ApBy8DR,CoBv8DQ;;EACI;ApB08DZ,CoBv8DQ;;EACI;EACA;ApB08DZ,CoBr8DA;EACI;EACA;EACA;ApBw8DJ,CqB3+DA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ArB8+DJ,CqB3+DA;EACI;EACA;EACA;EACA;EACA;EACA;ArB8+DJ,CqB5+DI;EACI;EACA;EACA;EACA;EAqBA;EACA;EACA;ArB09DR,CqBt9DA;EACI;IACI;ErBy9DN;EqBv9DE;IACI;ErBy9DN;AACF,CsBhhEI;EACI;AtBkhER,CsBhhEQ;EACI;AtBkhEZ,CsBhhEY;EACI;AtBkhEhB,CuBvhEY;EACI;EACA;EACA;EACA;AvB0hEhB,CuBvhEY;EACI;AvByhEhB,CuBhhEY;EACI;EACA;EACA;AvBmhEhB,CuBjhEgB;EACI;EACA;AvBmhEpB,CuBzgEY;EACI;EACA;AvB4gEhB,CuB1gEgB;EACI;AvB4gEpB,CwBtjEA;EACI;AxByjEJ,CwBtjEA;EACI;EACA;EACA;EACA;EACA;AxByjEJ,CwBvjEI;EACI;EACA;EACA;AxByjER,CwBvjEQ;;EAEI;EACA;AxByjEZ,CwBvjEY;;EACI;EACA;AxB0jEhB,CwBxjEgB;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AxB2jEpB,CwBtjEQ;EACI;AxBwjEZ,CwBrjEQ;EACI;AxBujEZ,CwBpjEQ;;EAEI;EACA;AxBsjEZ,CwBnjEQ;EACI;EACA;AxBqjEZ,CwBnjEY;EACI;EACA;EACA;EACA;AxBqjEhB,CwBnjEgB;EACI;EACA;EACA;EACA;EACA;AxBqjEpB,CwBnjEoB;EACI;EACA;EACA;AxBqjExB,CwBjjEgB;EACI;EACA;EACA;EACA;AxBmjEpB,CwB/iEY;EACI;EACA;EACA;EAqBA;EACA;AxB6hEhB,CwBzhEQ;EACI;EACA;EACA;EACA;EACA;AxB2hEZ,CwBzhEY;EACI;EACA;AxB2hEhB,CwBxhEY;EACI;EACA;EACA;EACA;AxB0hEhB,CwBrhEI;EACI;EACA;EACA;AxBuhER,CwBrhEQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AxBuhEZ,CwBrhEY;EACI;AxBuhEhB,CwBnhEQ;EACI;EACA;EACA;EACA;AxBqhEZ,CwBlhEQ;EACI;AxBohEZ,CwBjhEQ;EACI;AxBmhEZ,CwBhhEQ;EACI;AxBkhEZ,CwB/gEQ;EACI;AxBihEZ,CwB5gEgB;EACI;AxB8gEpB,CwBzgEgB;EACI;AxB2gEpB,CwBtgEgB;EACI;AxBwgEpB,CwBjgEA;EACI;IACI;ExBogEN;EwBlgEE;IACI;ExBogEN;AACF,CyBhtEA;EACI;EACA;AzBktEJ,CyBhtEI;EACI;EAEA;EACA;AzBktER,CyBhtEQ;EACI;AzBktEZ,CyB9sEI;EACI;EACA;EACA;AzBgtER,CyB9sEQ;EAiCI;AzBgrEZ,CyBhtEY;EACI;EACA;EACA;EACA;EACA;AzBktEhB,CyBhtEgB;EACI;AzBktEpB,CyB/sEgB;EACI;EACA;EACA;EACA;EACA;AzBitEpB,CyB7sEoB;EACI;AzB+sExB,CyB1sEoB;EACI;AzB4sExB,CyBtsEY;EACI;EACA;AzBwsEhB,CyBnsEI;EACI;AzBqsER,CyBnsEQ;EACI;EAEA;EACA;EACA;AzBqsEZ,CyBnsEY;EACI;EACA;EACA;EACA;EACA;EACA;AzBqsEhB,CyBnsEgB;EACI;AzBqsEpB,CyBlsEgB;EACI;AzBosEpB,CyBhsEY;EACI;AzBksEhB,CyB5rEA;EACI;EACA;EACA;EACA;EACA;EACA;AzB+rEJ,CyB7rEI;EACI;EACA;AzB+rER,CyB5rEI;EACI;EACA;EACA;EACA;AzB8rER,CyB5rEQ;;EAEI;EACA;AzB8rEZ,CyB3rEQ;EACI;EACA;EACA;EACA;AzB6rEZ,CyB1rEQ;EACI;EACA;AzB4rEZ,CyBtrEA;EACI;EACA;EACA;EACA;AzByrEJ,CyBtrEA;;EAEI;AzByrEJ,CyBtrEA;EACI;AzByrEJ,CyBvrEA;EACI;AzB0rEJ,CyBvrEA;EACI;AzB0rEJ,CyBvrEA;EACI;AzB0rEJ,CyBvrEA;;;;;;;;;;;EAWI;AzB0rEJ,C0Bv2EA;EACI;EACA;EACA;EACA;A1B02EJ,C0Bx2EI;EACI;EACA;EACA;A1B02ER,C0Bx2EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A1B02EZ,C0Bx2EY;;EAEI;EACA;A1B02EhB,C0Bv2EY;EAEI;A1Bw2EhB,C0Bt2EgB;;;EAEI;A1By2EpB,C0Br2EY;EACI;A1Bu2EhB,C0Bp2EY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A1Bs2EhB,C0Bn2EY;EACI;A1Bq2EhB,C0Bl2EY;EACI;A1Bo2EhB,C0B/1EI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;A1Bi2ER,C2Bx6EA;EACI;EACA;EACA;EACA;A3B26EJ,C2Bz6EI;EACI;EACA;EACA;A3B26ER,C2Bx6EI;EACI;A3B06ER,C2Bx6EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3B06EZ,C2Bt6EI;EACI;A3Bw6ER,C2Bt6EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3Bw6EZ,C2Bp6EI;EACI;A3Bs6ER,C2Bp6EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3Bs6EZ,C2Bl6EI;EACI;A3Bo6ER,C2Bl6EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3Bo6EZ,C2Bh6EI;EACI;A3Bk6ER,C2Bh6EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3Bk6EZ,C2B95EI;EACI;A3Bg6ER,C2B95EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3Bg6EZ,C2B55EI;EACI;EACA;A3B85ER,C2B55EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3B85EZ,C2B15EI;EACI;A3B45ER,C2B15EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3B45EZ,C2Bx5EI;EACI;A3B05ER,C2Bx5EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A3B05EZ,C2Bp5EI;EACI;EACA;EACA;A3Bu5ER,C2Bn5EA;EACI;EACA;EACA;EACA;EACA;A3Bs5EJ,C2Bn5EA;EACI;EACA;EACA;EACA;A3Bs5EJ,C2Bp5EI;EACI;EACA;EACA;A3Bs5ER,C2Bp5EQ;EACI;A3Bs5EZ,C2Bp5EY;EACI;A3Bs5EhB,C2Bl5EQ;EACI;A3Bo5EZ,C2Bl5EY;EACI;A3Bo5EhB,C2Bh5EQ;EACI;EACA;A3Bk5EZ,C2B94EI;EACI;EACA;EACA;EACA;A3Bg5ER,C2B74EI;EACI;A3B+4ER,C2B54EI;EACI;A3B84ER,C2B34EI;EACI;A3B64ER,C2B14EI;EACI;A3B44ER,C2Bx4EA;EACI;EACA;A3B24EJ,C2B14EI;EACI;EACA;A3B44ER,C2Bx4EA;EACI;EACA;A3B24EJ,C2B14EI;EACI;EACA;EACA;A3B44ER,C2Bx4EA;EACI;EACA;EACA;A3B24EJ,C2Bz4EI;EACI;A3B24ER,C2Bx4EY;EACI;A3B04EhB,C2Br4EY;EACI;A3Bu4EhB,C2Bl4EI;EACI;EACA;A3Bo4ER,C2Bl4EQ;EACI;EACA;EACA;A3Bo4EZ,C2Bh4EY;EACI;A3Bk4EhB,C2B73EY;EACI;A3B+3EhB,C2B13EI;EACI;EACA;EACA;A3B43ER,C2B13EQ;EACI;EACA;EACA;A3B43EZ,C2Bz3EQ;EACI;EACA;EACA;A3B23EZ,C2Bx3EQ;EACI;EACA;EACA;A3B03EZ,C2Bv3EQ;EACI;EACA;EACA;A3By3EZ,C2Bt3EQ;EACI;EACA;EACA;A3Bw3EZ,C2Bp3EI;EACI;A3Bs3ER,C2Bp3EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;A3Bs3EZ,C2Bl3EI;EACI;A3Bo3ER,C2Bl3EQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;A3Bo3EZ,C2B/2EA;EACI;EACA;A3Bk3EJ,C2Bh3EQ;EACI;A3Bk3EZ,C4B9tFA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A5BiuFJ,C4B/tFI;EACI;A5BiuFR,C4B9tFI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;A5B+tFR,C4B5tFI;EACI;A5B8tFR,C4B3tFI;EACI;EACA;EACA;EACA;EACA;A5B6tFR,C4B3tFQ;EACI;A5B6tFZ,C4B1tFgB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A5B4tFpB,C4BttFI;EACI;EACA;EACA;EACA;A5BwtFR,C4BrtFI;EACI;EACA;EACA;EAEA;EACA;EACA;A5BstFR,C4BptFQ;;EAEI;EACA;A5BstFZ,C4BltFI;EACI;EACA;EACA;EACA;A5BotFR,C4BjtFI;EACI;EACA;EACA;A5BmtFR,C4BjtFQ;EACI;EACA;EACA;EACA;A5BmtFZ,C4BjtFY;EACI;A5BmtFhB,C4B/sFQ;EACI;EACA;EACA;EACA;A5BitFZ,C4B7sFI;EACI;EACA;EACA;EACA;A5B+sFR,C4B5sFI;EAEI;EACA;EACA;A5B6sFR,C4BzsFQ;EACI;EACA;A5B2sFZ,C4BxsFQ;EACI;EACA;EACA;A5B0sFZ,C4BtsFI;EACI;A5BwsFR,C4BtsFQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A5BwsFZ,C4BrsFQ;EAEI;A5BssFZ,C4BlsFI;EACI;A5BosFR,C4BjsFI;EACI;A5BmsFR,C4BhsFI;EACI;EACA;A5BksFR,C4B/rFI;EACI;A5BisFR,C4B/rFQ;EACI;A5BisFZ,C4B1rFQ;EACI;A5B6rFZ,C4BprFgB;EACI;A5BurFpB,C4BjrFoB;EACI;A5BmrFxB,C4B7qFQ;EACI;A5B+qFZ,C4B7qFY;EACI;A5B+qFhB,C4B1qFY;EACI;A5B4qFhB,C6B/4FA;EACI;EACA;A7Bk5FJ,C6B/4FA;EACI;A7Bk5FJ,C8Bv5FI;EACI;EACA;EACA;EACA;A9B05FR,C8Bv5FI;EACI;EACA;EAEA;EACA;EACA;EAEA;A9Bu5FR,C8Bp5FI;;EAEI;A9Bs5FR,C8Bh5FQ;;;;EAII;A9Bm5FZ,C+B76FQ;;;;EACI;EACA;EACA;EACA;A/Bm7FZ,C+Bj7FY;;;;EACI;EACA;A/Bs7FhB,C+Bh7FQ;;;;EACI;EACA;EACA;A/Bq7FZ,C+Bl7FQ;;;;EACI;EACA;EACA;A/Bu7FZ,C+Bp7FQ;;;;EACI;A/By7FZ,C+Bv7FgB;;;;EACI;EACA;EACA;EACA;A/B47FpB,C+B17FoB;;;;EACI;EACA;A/B+7FxB,CgCx+FI;EACI;EACA;EACA;EACA;AhC2+FR,CgCx+FI;EACI;EACA;AhC0+FR,CgCx+FQ;EACI;AhC0+FZ,CgCv+FQ;EACI;EACA;EACA;EACA;EACA;EACA;AhCy+FZ,CiC//FA;EACI;AjCkgGJ,CiChgGI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AjCkgGR,CiChgGQ;;EAEI;EACA;EACA;AjCkgGZ,CkChhGoB;EACI;EACA;AlCmhGxB,CkCjhGwB;EACI;AlCmhG5B,CkCjhG4B;EACI;AlCmhGhC,CkCjhGgC;EACI;EACA;AlCmhGpC,CkChhGgC;EACI;EACA;AlCkhGpC,CkC/gGgC;EAEI;EACA;AlCghGpC,CkC5gG4B;EACI;EACA;AlC8gGhC,CkC1gGwB;;EAEI;EACA;EACA;AlC4gG5B,CmCjjGoB;EACI;AnCojGxB,CmCljGwB;EACI;EACA;AnCojG5B,CmCjjGwB;EACI;EACA;AnCmjG5B,CoCjkGI;EACI;ApCokGR,CoClkGQ;EACI;ApCokGZ,CoC9jGoB;EACI;EACA;ApCgkGxB,CoC9jGwB;EACI;ApCgkG5B,CoCxjGY;EACI;EACA;ApC0jGhB,CoCxjGgB;EACI;ApC0jGpB,CoCnjGgB;EACI;EACA;ApCqjGpB,CoC/iGY;EACI;ApCijGhB,CoC7iGgB;EACI;EACA;ApC+iGpB,CqChmGQ;EACI;EACA;ArCmmGZ,CqCjmGY;EACI;EACA;EACA;EACA;ArCmmGhB,CqC/lGQ;EACI;EACA;EACA;ArCimGZ,CqCxlGgB;EACI;ArC2lGpB,CsCnnGY;EACI;AtCsnGhB,CsClnGgB;EACI;EACA;EACA;EACA;AtConGpB,CsClnGoB;EACI;EACA;AtConGxB,CsC9mGQ;EACI;EACA;EACA;AtCgnGZ,CsC9mGY;EACI;EACA;EACA;AtCgnGhB,CsCrmGoB;EACI;AtCwmGxB,CuClpGA;EACI;EACA;EACA;EACA;EACA;AvCqpGJ,CwCzpGI;EACI;AxC4pGR,CwC1pGQ;EACI;EACA;EACA;AxC4pGZ,CyC9pGoB;EACI;AzCiqGxB,CyCzpGwB;EACI;EACA;AzC2pG5B,CyCvpG4B;EACI;AzCypGhC,CyCvpGgC;EACI;AzCypGpC,C0CjrGA;EACI;A1CorGJ,C0CjrGA;EACI;EACA;EACA;A1CorGJ,C0ChrGI;EACI;A1CmrGR,C0C/qGI;EACI;A1CkrGR,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;EACA;EACA;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CnsGA;EACI;A3CssGJ,C2CjsGA;EACI;A3CosGJ,C2CjsGA;EACI;A3CosGJ,C2CjsGA;EACI;A3CosGJ,C2CjsGA;EACI;A3CosGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C2ChsGA;EACI;A3CmsGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C4CrzGA;EACI;EACA;A5CwzGJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;EACA;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C6Cz+GA;EACI;A7C4+GJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C8CpoHA;EACI;A9CuoHJ,C+C5rHA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;A/C+rHJ,C+C7rHI;;EAEI;EACA;EACA;A/C+rHR,C", "sources": ["./node_modules/@angular/material/prebuilt-themes/pink-bluegrey.css", "./src/assets/scss/base/_reset.scss", "./src/styles.scss", "./src/assets/scss/base/_cdk.scss", "./src/assets/scss/base/_colors.scss", "./src/assets/scss/base/_theme-variables.scss", "./src/assets/scss/base/_fonts.scss", "./src/assets/scss/base/_typography.scss", "./src/assets/scss/base/_scroll.scss", "./src/assets/scss/base/_sr-only.scss", "./src/assets/scss/layout/_main.scss", "./src/assets/scss/components/_alias.scss", "./src/assets/scss/components/_buttons.scss", "./src/assets/scss/components/_contextmenu.scss", "./src/assets/scss/components/_dropdown.scss", "./src/assets/scss/components/_forms.scss", "./src/assets/scss/components/_icons.scss", "./src/assets/scss/components/_loader.scss", "./src/assets/scss/components/_migrate-alert.scss", "./src/assets/scss/components/_modals.scss", "./src/assets/scss/components/_mat-dialog.scss", "./src/assets/scss/components/_ng-select.scss", "./src/assets/scss/components/_ngx-pagination.scss", "./src/assets/scss/components/_preloader.scss", "./src/assets/scss/components/_progress.scss", "./src/assets/scss/components/_seed-phrase.scss", "./src/assets/scss/components/_status.scss", "./src/assets/scss/components/_table.scss", "./src/assets/scss/components/_tabs.scss", "./src/assets/scss/components/_tooltips.scss", "./src/assets/scss/components/_wallet.scss", "./src/assets/scss/components/_mat-tooltip.scss", "./src/assets/scss/components/_mat-menu.scss", "./src/assets/scss/modals/_send-details.scss", "./src/assets/scss/pages/_assign-alias.scss", "./src/assets/scss/pages/wallet/tabs/_assets.scss", "./src/assets/scss/pages/wallet/tabs/_contracts.scss", "./src/assets/scss/pages/wallet/tabs/_history.scss", "./src/assets/scss/pages/wallet/tabs/_purchase.scss", "./src/assets/scss/pages/wallet/tabs/_receive.scss", "./src/assets/scss/pages/wallet/tabs/_staking.scss", "./src/assets/scss/pages/wallet/_wallet.scss", "./src/assets/scss/pages/_auth.scss", "./src/assets/scss/pages/_contacts.scss", "./src/assets/scss/helpers/_focus-visible-fallback.scss", "./src/assets/scss/helpers/_helper-classes.scss", "./src/assets/scss/helpers/_margin.scss", "./src/assets/scss/helpers/_padding.scss", "./src/assets/scss/helpers/_width.scss", "./src/assets/scss/helpers/_token-logo.scss"], "sourcesContent": [".mat-badge-content{font-weight:600;font-size:12px;font-family:<PERSON><PERSON>, \"Helvetica Neue\", sans-serif}.mat-badge-small .mat-badge-content{font-size:9px}.mat-badge-large .mat-badge-content{font-size:24px}.mat-h1,.mat-headline,.mat-typography .mat-h1,.mat-typography .mat-headline,.mat-typography h1{font:400 24px/32px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2,.mat-title,.mat-typography .mat-h2,.mat-typography .mat-title,.mat-typography h2{font:500 20px/32px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h3,.mat-subheading-2,.mat-typography .mat-h3,.mat-typography .mat-subheading-2,.mat-typography h3{font:400 16px/28px <PERSON>o, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h4,.mat-subheading-1,.mat-typography .mat-h4,.mat-typography .mat-subheading-1,.mat-typography h4{font:400 15px/24px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h5,.mat-typography .mat-h5,.mat-typography h5{font:400 calc(14px * 0.83)/20px Roboto, \"Helvetica Neue\", sans-serif;margin:0 0 12px}.mat-h6,.mat-typography .mat-h6,.mat-typography h6{font:400 calc(14px * 0.67)/20px Roboto, \"Helvetica Neue\", sans-serif;margin:0 0 12px}.mat-body-strong,.mat-body-2,.mat-typography .mat-body-strong,.mat-typography .mat-body-2{font:500 14px/24px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-body,.mat-body-1,.mat-typography .mat-body,.mat-typography .mat-body-1,.mat-typography{font:400 14px/20px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-body p,.mat-body-1 p,.mat-typography .mat-body p,.mat-typography .mat-body-1 p,.mat-typography p{margin:0 0 12px}.mat-small,.mat-caption,.mat-typography .mat-small,.mat-typography .mat-caption{font:400 12px/20px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-display-4,.mat-typography .mat-display-4{font:300 112px/112px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:-0.05em;margin:0 0 56px}.mat-display-3,.mat-typography .mat-display-3{font:400 56px/56px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:-0.02em;margin:0 0 64px}.mat-display-2,.mat-typography .mat-display-2{font:400 45px/48px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:-0.005em;margin:0 0 64px}.mat-display-1,.mat-typography .mat-display-1{font:400 34px/40px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-bottom-sheet-container{font:400 14px/20px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-button,.mat-raised-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button,.mat-fab,.mat-mini-fab{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:14px;font-weight:500}.mat-button-toggle{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-card{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-card-title{font-size:24px;font-weight:500}.mat-card-header .mat-card-title{font-size:20px}.mat-card-subtitle,.mat-card-content{font-size:14px}.mat-checkbox{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-checkbox-layout .mat-checkbox-label{line-height:24px}.mat-chip{font-size:14px;font-weight:500}.mat-chip .mat-chip-trailing-icon.mat-icon,.mat-chip .mat-chip-remove.mat-icon{font-size:18px}.mat-table{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-header-cell{font-size:12px;font-weight:500}.mat-cell,.mat-footer-cell{font-size:14px}.mat-calendar{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-calendar-body{font-size:13px}.mat-calendar-body-label,.mat-calendar-period-button{font-size:14px;font-weight:500}.mat-calendar-table-header th{font-size:11px;font-weight:400}.mat-dialog-title{font:500 20px/32px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-expansion-panel-header{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:15px;font-weight:400}.mat-expansion-panel-content{font:400 14px/20px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-form-field{font-size:inherit;font-weight:400;line-height:1.125;font-family:Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-form-field-wrapper{padding-bottom:1.34375em}.mat-form-field-prefix .mat-icon,.mat-form-field-suffix .mat-icon{font-size:150%;line-height:1.125}.mat-form-field-prefix .mat-icon-button,.mat-form-field-suffix .mat-icon-button{height:1.5em;width:1.5em}.mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-suffix .mat-icon-button .mat-icon{height:1.125em;line-height:1.125}.mat-form-field-infix{padding:.5em 0;border-top:.84375em solid rgba(0,0,0,0)}.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.34375em) scale(0.75);width:133.3333333333%}.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.34374em) scale(0.75);width:133.3333433333%}.mat-form-field-label-wrapper{top:-0.84375em;padding-top:.84375em}.mat-form-field-label{top:1.34375em}.mat-form-field-underline{bottom:1.34375em}.mat-form-field-subscript-wrapper{font-size:75%;margin-top:.6666666667em;top:calc(100% - 1.7916666667em)}.mat-form-field-appearance-legacy .mat-form-field-wrapper{padding-bottom:1.25em}.mat-form-field-appearance-legacy .mat-form-field-infix{padding:.4375em 0}.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.001px);width:133.3333333333%}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.00101px);width:133.3333433333%}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.00102px);width:133.3333533333%}.mat-form-field-appearance-legacy .mat-form-field-label{top:1.28125em}.mat-form-field-appearance-legacy .mat-form-field-underline{bottom:1.25em}.mat-form-field-appearance-legacy .mat-form-field-subscript-wrapper{margin-top:.5416666667em;top:calc(100% - 1.6666666667em)}@media print{.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28122em) scale(0.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.28121em) scale(0.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.2812em) scale(0.75)}}.mat-form-field-appearance-fill .mat-form-field-infix{padding:.25em 0 .75em 0}.mat-form-field-appearance-fill .mat-form-field-label{top:1.09375em;margin-top:-0.5em}.mat-form-field-appearance-fill.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-fill.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-0.59375em) scale(0.75);width:133.3333333333%}.mat-form-field-appearance-fill.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-0.59374em) scale(0.75);width:133.3333433333%}.mat-form-field-appearance-outline .mat-form-field-infix{padding:1em 0 1em 0}.mat-form-field-appearance-outline .mat-form-field-label{top:1.84375em;margin-top:-0.25em}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.59375em) scale(0.75);width:133.3333333333%}.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server[label]:not(:label-shown)+.mat-form-field-label-wrapper .mat-form-field-label{transform:translateY(-1.59374em) scale(0.75);width:133.3333433333%}.mat-grid-tile-header,.mat-grid-tile-footer{font-size:14px}.mat-grid-tile-header .mat-line,.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2),.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:12px}input.mat-input-element{margin-top:-0.0625em}.mat-menu-item{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:14px;font-weight:400}.mat-paginator,.mat-paginator-page-size .mat-select-trigger{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:12px}.mat-radio-button{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-select{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-select-trigger{height:1.125em}.mat-slide-toggle-content{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-slider-thumb-label-text{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:12px;font-weight:500}.mat-stepper-vertical,.mat-stepper-horizontal{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-step-label{font-size:14px;font-weight:400}.mat-step-sub-label-error{font-weight:normal}.mat-step-label-error{font-size:14px}.mat-step-label-selected{font-size:14px;font-weight:500}.mat-tab-group{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-tab-label,.mat-tab-link{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:14px;font-weight:500}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font:500 20px/32px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal;margin:0}.mat-tooltip{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:10px;padding-top:6px;padding-bottom:6px}.mat-tooltip-handset{font-size:14px;padding-top:8px;padding-bottom:8px}.mat-list-item{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-list-option{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-list-base .mat-list-item{font-size:16px}.mat-list-base .mat-list-item .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base .mat-list-item .mat-line:nth-child(n+2){font-size:14px}.mat-list-base .mat-list-option{font-size:16px}.mat-list-base .mat-list-option .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base .mat-list-option .mat-line:nth-child(n+2){font-size:14px}.mat-list-base .mat-subheader{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:14px;font-weight:500}.mat-list-base[dense] .mat-list-item{font-size:12px}.mat-list-base[dense] .mat-list-item .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense] .mat-list-item .mat-line:nth-child(n+2){font-size:12px}.mat-list-base[dense] .mat-list-option{font-size:12px}.mat-list-base[dense] .mat-list-option .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense] .mat-list-option .mat-line:nth-child(n+2){font-size:12px}.mat-list-base[dense] .mat-subheader{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:12px;font-weight:500}.mat-option{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:16px}.mat-optgroup-label{font:500 14px/24px Roboto, \"Helvetica Neue\", sans-serif;letter-spacing:normal}.mat-simple-snackbar{font-family:Roboto, \"Helvetica Neue\", sans-serif;font-size:14px}.mat-simple-snackbar-action{line-height:1;font-family:inherit;font-size:inherit;font-weight:500}.mat-tree{font-family:Roboto, \"Helvetica Neue\", sans-serif}.mat-tree-node,.mat-nested-tree-node{font-weight:400;font-size:14px}.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0)}.cdk-high-contrast-active .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\"\"}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus::before{content:\"\"}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-option{color:#fff}.mat-option:hover:not(.mat-option-disabled),.mat-option:focus:not(.mat-option-disabled){background:rgba(255,255,255,.04)}.mat-option.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled){background:rgba(255,255,255,.04)}.mat-option.mat-active{background:rgba(255,255,255,.04);color:#fff}.mat-option.mat-option-disabled{color:rgba(255,255,255,.5)}.mat-primary .mat-option.mat-selected:not(.mat-option-disabled){color:#c2185b}.mat-accent .mat-option.mat-selected:not(.mat-option-disabled){color:#b0bec5}.mat-warn .mat-option.mat-selected:not(.mat-option-disabled){color:#f44336}.mat-optgroup-label{color:rgba(255,255,255,.7)}.mat-optgroup-disabled .mat-optgroup-label{color:rgba(255,255,255,.5)}.mat-pseudo-checkbox{color:rgba(255,255,255,.7)}.mat-pseudo-checkbox::after{color:#303030}.mat-pseudo-checkbox-disabled{color:#686868}.mat-primary .mat-pseudo-checkbox-checked,.mat-primary .mat-pseudo-checkbox-indeterminate{background:#c2185b}.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-indeterminate,.mat-accent .mat-pseudo-checkbox-checked,.mat-accent .mat-pseudo-checkbox-indeterminate{background:#b0bec5}.mat-warn .mat-pseudo-checkbox-checked,.mat-warn .mat-pseudo-checkbox-indeterminate{background:#f44336}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background:#686868}.mat-app-background{background-color:#303030;color:#fff}.mat-elevation-z0{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z1{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z2{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z3{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z4{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z5{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 5px 8px 0px rgba(0, 0, 0, 0.14),0px 1px 14px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z6{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z7{box-shadow:0px 4px 5px -2px rgba(0, 0, 0, 0.2),0px 7px 10px 1px rgba(0, 0, 0, 0.14),0px 2px 16px 1px rgba(0, 0, 0, 0.12)}.mat-elevation-z8{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z9{box-shadow:0px 5px 6px -3px rgba(0, 0, 0, 0.2),0px 9px 12px 1px rgba(0, 0, 0, 0.14),0px 3px 16px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z10{box-shadow:0px 6px 6px -3px rgba(0, 0, 0, 0.2),0px 10px 14px 1px rgba(0, 0, 0, 0.14),0px 4px 18px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z11{box-shadow:0px 6px 7px -4px rgba(0, 0, 0, 0.2),0px 11px 15px 1px rgba(0, 0, 0, 0.14),0px 4px 20px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z12{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 12px 17px 2px rgba(0, 0, 0, 0.14),0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z13{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 13px 19px 2px rgba(0, 0, 0, 0.14),0px 5px 24px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z14{box-shadow:0px 7px 9px -4px rgba(0, 0, 0, 0.2),0px 14px 21px 2px rgba(0, 0, 0, 0.14),0px 5px 26px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z15{box-shadow:0px 8px 9px -5px rgba(0, 0, 0, 0.2),0px 15px 22px 2px rgba(0, 0, 0, 0.14),0px 6px 28px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z16{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z17{box-shadow:0px 8px 11px -5px rgba(0, 0, 0, 0.2),0px 17px 26px 2px rgba(0, 0, 0, 0.14),0px 6px 32px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z18{box-shadow:0px 9px 11px -5px rgba(0, 0, 0, 0.2),0px 18px 28px 2px rgba(0, 0, 0, 0.14),0px 7px 34px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z19{box-shadow:0px 9px 12px -6px rgba(0, 0, 0, 0.2),0px 19px 29px 2px rgba(0, 0, 0, 0.14),0px 7px 36px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z20{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 20px 31px 3px rgba(0, 0, 0, 0.14),0px 8px 38px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z21{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 21px 33px 3px rgba(0, 0, 0, 0.14),0px 8px 40px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z22{box-shadow:0px 10px 14px -6px rgba(0, 0, 0, 0.2),0px 22px 35px 3px rgba(0, 0, 0, 0.14),0px 8px 42px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z23{box-shadow:0px 11px 14px -7px rgba(0, 0, 0, 0.2),0px 23px 36px 3px rgba(0, 0, 0, 0.14),0px 9px 44px 8px rgba(0, 0, 0, 0.12)}.mat-elevation-z24{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-theme-loaded-marker{display:none}.mat-autocomplete-panel{background:#424242;color:#fff}.mat-autocomplete-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-autocomplete-panel .mat-option.mat-selected:not(.mat-active):not(:hover){background:#424242}.mat-autocomplete-panel .mat-option.mat-selected:not(.mat-active):not(:hover):not(.mat-option-disabled){color:#fff}.mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-hidden .mat-badge-content{display:none}.mat-badge-content{position:absolute;text-align:center;display:inline-block;border-radius:50%;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;pointer-events:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:16px;height:16px;line-height:16px}.mat-badge-small.mat-badge-above .mat-badge-content{top:-8px}.mat-badge-small.mat-badge-below .mat-badge-content{bottom:-8px}.mat-badge-small.mat-badge-before .mat-badge-content{left:-16px}[dir=rtl] .mat-badge-small.mat-badge-before .mat-badge-content{left:auto;right:-16px}.mat-badge-small.mat-badge-after .mat-badge-content{right:-16px}[dir=rtl] .mat-badge-small.mat-badge-after .mat-badge-content{right:auto;left:-16px}.mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-8px}.mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-8px}.mat-badge-medium .mat-badge-content{width:22px;height:22px;line-height:22px}.mat-badge-medium.mat-badge-above .mat-badge-content{top:-11px}.mat-badge-medium.mat-badge-below .mat-badge-content{bottom:-11px}.mat-badge-medium.mat-badge-before .mat-badge-content{left:-22px}[dir=rtl] .mat-badge-medium.mat-badge-before .mat-badge-content{left:auto;right:-22px}.mat-badge-medium.mat-badge-after .mat-badge-content{right:-22px}[dir=rtl] .mat-badge-medium.mat-badge-after .mat-badge-content{right:auto;left:-22px}.mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-11px}.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-11px}.mat-badge-large .mat-badge-content{width:28px;height:28px;line-height:28px}.mat-badge-large.mat-badge-above .mat-badge-content{top:-14px}.mat-badge-large.mat-badge-below .mat-badge-content{bottom:-14px}.mat-badge-large.mat-badge-before .mat-badge-content{left:-28px}[dir=rtl] .mat-badge-large.mat-badge-before .mat-badge-content{left:auto;right:-28px}.mat-badge-large.mat-badge-after .mat-badge-content{right:-28px}[dir=rtl] .mat-badge-large.mat-badge-after .mat-badge-content{right:auto;left:-28px}.mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-14px}.mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-14px}.mat-badge-content{color:#fff;background:#c2185b}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-accent .mat-badge-content{background:#b0bec5;color:rgba(0,0,0,.87)}.mat-badge-warn .mat-badge-content{color:#fff;background:#f44336}.mat-badge-disabled .mat-badge-content{background:#6e6e6e;color:rgba(255,255,255,.5)}.mat-bottom-sheet-container{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12);background:#424242;color:#fff}.mat-button,.mat-icon-button,.mat-stroked-button{color:inherit;background:rgba(0,0,0,0)}.mat-button.mat-primary,.mat-icon-button.mat-primary,.mat-stroked-button.mat-primary{color:#c2185b}.mat-button.mat-accent,.mat-icon-button.mat-accent,.mat-stroked-button.mat-accent{color:#b0bec5}.mat-button.mat-warn,.mat-icon-button.mat-warn,.mat-stroked-button.mat-warn{color:#f44336}.mat-button.mat-primary.mat-button-disabled,.mat-button.mat-accent.mat-button-disabled,.mat-button.mat-warn.mat-button-disabled,.mat-button.mat-button-disabled.mat-button-disabled,.mat-icon-button.mat-primary.mat-button-disabled,.mat-icon-button.mat-accent.mat-button-disabled,.mat-icon-button.mat-warn.mat-button-disabled,.mat-icon-button.mat-button-disabled.mat-button-disabled,.mat-stroked-button.mat-primary.mat-button-disabled,.mat-stroked-button.mat-accent.mat-button-disabled,.mat-stroked-button.mat-warn.mat-button-disabled,.mat-stroked-button.mat-button-disabled.mat-button-disabled{color:rgba(255,255,255,.3)}.mat-button.mat-primary .mat-button-focus-overlay,.mat-icon-button.mat-primary .mat-button-focus-overlay,.mat-stroked-button.mat-primary .mat-button-focus-overlay{background-color:#c2185b}.mat-button.mat-accent .mat-button-focus-overlay,.mat-icon-button.mat-accent .mat-button-focus-overlay,.mat-stroked-button.mat-accent .mat-button-focus-overlay{background-color:#b0bec5}.mat-button.mat-warn .mat-button-focus-overlay,.mat-icon-button.mat-warn .mat-button-focus-overlay,.mat-stroked-button.mat-warn .mat-button-focus-overlay{background-color:#f44336}.mat-button.mat-button-disabled .mat-button-focus-overlay,.mat-icon-button.mat-button-disabled .mat-button-focus-overlay,.mat-stroked-button.mat-button-disabled .mat-button-focus-overlay{background-color:rgba(0,0,0,0)}.mat-button .mat-ripple-element,.mat-icon-button .mat-ripple-element,.mat-stroked-button .mat-ripple-element{opacity:.1;background-color:currentColor}.mat-button-focus-overlay{background:#fff}.mat-stroked-button:not(.mat-button-disabled){border-color:rgba(255,255,255,.12)}.mat-flat-button,.mat-raised-button,.mat-fab,.mat-mini-fab{color:#fff;background-color:#424242}.mat-flat-button.mat-primary,.mat-raised-button.mat-primary,.mat-fab.mat-primary,.mat-mini-fab.mat-primary{color:#fff}.mat-flat-button.mat-accent,.mat-raised-button.mat-accent,.mat-fab.mat-accent,.mat-mini-fab.mat-accent{color:rgba(0,0,0,.87)}.mat-flat-button.mat-warn,.mat-raised-button.mat-warn,.mat-fab.mat-warn,.mat-mini-fab.mat-warn{color:#fff}.mat-flat-button.mat-primary.mat-button-disabled,.mat-flat-button.mat-accent.mat-button-disabled,.mat-flat-button.mat-warn.mat-button-disabled,.mat-flat-button.mat-button-disabled.mat-button-disabled,.mat-raised-button.mat-primary.mat-button-disabled,.mat-raised-button.mat-accent.mat-button-disabled,.mat-raised-button.mat-warn.mat-button-disabled,.mat-raised-button.mat-button-disabled.mat-button-disabled,.mat-fab.mat-primary.mat-button-disabled,.mat-fab.mat-accent.mat-button-disabled,.mat-fab.mat-warn.mat-button-disabled,.mat-fab.mat-button-disabled.mat-button-disabled,.mat-mini-fab.mat-primary.mat-button-disabled,.mat-mini-fab.mat-accent.mat-button-disabled,.mat-mini-fab.mat-warn.mat-button-disabled,.mat-mini-fab.mat-button-disabled.mat-button-disabled{color:rgba(255,255,255,.3)}.mat-flat-button.mat-primary,.mat-raised-button.mat-primary,.mat-fab.mat-primary,.mat-mini-fab.mat-primary{background-color:#c2185b}.mat-flat-button.mat-accent,.mat-raised-button.mat-accent,.mat-fab.mat-accent,.mat-mini-fab.mat-accent{background-color:#b0bec5}.mat-flat-button.mat-warn,.mat-raised-button.mat-warn,.mat-fab.mat-warn,.mat-mini-fab.mat-warn{background-color:#f44336}.mat-flat-button.mat-primary.mat-button-disabled,.mat-flat-button.mat-accent.mat-button-disabled,.mat-flat-button.mat-warn.mat-button-disabled,.mat-flat-button.mat-button-disabled.mat-button-disabled,.mat-raised-button.mat-primary.mat-button-disabled,.mat-raised-button.mat-accent.mat-button-disabled,.mat-raised-button.mat-warn.mat-button-disabled,.mat-raised-button.mat-button-disabled.mat-button-disabled,.mat-fab.mat-primary.mat-button-disabled,.mat-fab.mat-accent.mat-button-disabled,.mat-fab.mat-warn.mat-button-disabled,.mat-fab.mat-button-disabled.mat-button-disabled,.mat-mini-fab.mat-primary.mat-button-disabled,.mat-mini-fab.mat-accent.mat-button-disabled,.mat-mini-fab.mat-warn.mat-button-disabled,.mat-mini-fab.mat-button-disabled.mat-button-disabled{background-color:rgba(255,255,255,.12)}.mat-flat-button.mat-primary .mat-ripple-element,.mat-raised-button.mat-primary .mat-ripple-element,.mat-fab.mat-primary .mat-ripple-element,.mat-mini-fab.mat-primary .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-flat-button.mat-accent .mat-ripple-element,.mat-raised-button.mat-accent .mat-ripple-element,.mat-fab.mat-accent .mat-ripple-element,.mat-mini-fab.mat-accent .mat-ripple-element{background-color:rgba(0,0,0,.1)}.mat-flat-button.mat-warn .mat-ripple-element,.mat-raised-button.mat-warn .mat-ripple-element,.mat-fab.mat-warn .mat-ripple-element,.mat-mini-fab.mat-warn .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-stroked-button:not([class*=mat-elevation-z]),.mat-flat-button:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-raised-button:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-raised-button:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-raised-button.mat-button-disabled:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-fab:not([class*=mat-elevation-z]),.mat-mini-fab:not([class*=mat-elevation-z]){box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-fab:not(.mat-button-disabled):active:not([class*=mat-elevation-z]),.mat-mini-fab:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 12px 17px 2px rgba(0, 0, 0, 0.14),0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-fab.mat-button-disabled:not([class*=mat-elevation-z]),.mat-mini-fab.mat-button-disabled:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.mat-button-toggle{color:rgba(255,255,255,.5)}.mat-button-toggle .mat-button-toggle-focus-overlay{background-color:rgba(255,255,255,.12)}.mat-button-toggle-appearance-standard{color:#fff;background:#424242}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:#fff}.mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle{border-left:solid 1px #595959}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle{border-left:none;border-right:solid 1px #595959}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle+.mat-button-toggle{border-left:none;border-right:none;border-top:solid 1px #595959}.mat-button-toggle-checked{background-color:#212121;color:rgba(255,255,255,.7)}.mat-button-toggle-checked.mat-button-toggle-appearance-standard{color:#fff}.mat-button-toggle-disabled{color:rgba(255,255,255,.3);background-color:#000}.mat-button-toggle-disabled.mat-button-toggle-appearance-standard{background:#424242}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:#424242}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border:solid 1px #595959}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{line-height:48px}.mat-card{background:#424242;color:#fff}.mat-card:not([class*=mat-elevation-z]){box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-card.mat-card-flat:not([class*=mat-elevation-z]){box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-card-subtitle{color:rgba(255,255,255,.7)}.mat-checkbox-frame{border-color:rgba(255,255,255,.7)}.mat-checkbox-checkmark{fill:#303030}.mat-checkbox-checkmark-path{stroke:#303030 !important}.mat-checkbox-mixedmark{background-color:#303030}.mat-checkbox-indeterminate.mat-primary .mat-checkbox-background,.mat-checkbox-checked.mat-primary .mat-checkbox-background{background-color:#c2185b}.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,.mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:#b0bec5}.mat-checkbox-indeterminate.mat-warn .mat-checkbox-background,.mat-checkbox-checked.mat-warn .mat-checkbox-background{background-color:#f44336}.mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background,.mat-checkbox-disabled.mat-checkbox-indeterminate .mat-checkbox-background{background-color:#686868}.mat-checkbox-disabled:not(.mat-checkbox-checked) .mat-checkbox-frame{border-color:#686868}.mat-checkbox-disabled .mat-checkbox-label{color:rgba(255,255,255,.5)}.mat-checkbox .mat-ripple-element{background-color:#fff}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-primary .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-primary .mat-ripple-element{background:#c2185b}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element{background:#b0bec5}.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-warn .mat-ripple-element,.mat-checkbox:active:not(.mat-checkbox-disabled).mat-warn .mat-ripple-element{background:#f44336}.mat-chip.mat-standard-chip{background-color:#616161;color:#fff}.mat-chip.mat-standard-chip .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip:not(.mat-chip-disabled):active{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-chip.mat-standard-chip:not(.mat-chip-disabled) .mat-chip-remove:hover{opacity:.54}.mat-chip.mat-standard-chip.mat-chip-disabled{opacity:.4}.mat-chip.mat-standard-chip::after{background:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary{background-color:#c2185b;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn{background-color:#f44336;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn .mat-chip-remove{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn .mat-ripple-element{background-color:rgba(255,255,255,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent .mat-chip-remove{color:rgba(0,0,0,.87);opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent .mat-ripple-element{background-color:rgba(0,0,0,.1)}.mat-table{background:#424242}.mat-table thead,.mat-table tbody,.mat-table tfoot,mat-header-row,mat-row,mat-footer-row,[mat-header-row],[mat-row],[mat-footer-row],.mat-table-sticky{background:inherit}mat-row,mat-header-row,mat-footer-row,th.mat-header-cell,td.mat-cell,td.mat-footer-cell{border-bottom-color:rgba(255,255,255,.12)}.mat-header-cell{color:rgba(255,255,255,.7)}.mat-cell,.mat-footer-cell{color:#fff}.mat-calendar-arrow{fill:#fff}.mat-datepicker-toggle,.mat-datepicker-content .mat-calendar-next-button,.mat-datepicker-content .mat-calendar-previous-button{color:#fff}.mat-calendar-table-header-divider::after{background:rgba(255,255,255,.12)}.mat-calendar-table-header,.mat-calendar-body-label{color:rgba(255,255,255,.7)}.mat-calendar-body-cell-content,.mat-date-range-input-separator{color:#fff;border-color:rgba(0,0,0,0)}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:rgba(255,255,255,.5)}.mat-form-field-disabled .mat-date-range-input-separator{color:rgba(255,255,255,.5)}.mat-calendar-body-in-preview{color:rgba(255,255,255,.24)}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(255,255,255,.5)}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(255,255,255,.3)}.mat-calendar-body-in-range::before{background:rgba(194,24,91,.2)}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(194, 24, 91, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(194, 24, 91, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-calendar-body-selected{background-color:#c2185b;color:#fff}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(194,24,91,.4)}.mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px #fff}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(194,24,91,.3)}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(194,24,91,.3)}}.mat-datepicker-content{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12);background-color:#424242;color:#fff}.mat-datepicker-content.mat-accent .mat-calendar-body-in-range::before{background:rgba(176,190,197,.2)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-bridge-start::before,.mat-datepicker-content.mat-accent [dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(176, 190, 197, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-bridge-end::before,.mat-datepicker-content.mat-accent [dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(176, 190, 197, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-accent .mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-datepicker-content.mat-accent .mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-datepicker-content.mat-accent .mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-datepicker-content.mat-accent .mat-calendar-body-selected{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-datepicker-content.mat-accent .mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(176,190,197,.4)}.mat-datepicker-content.mat-accent .mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px rgba(0,0,0,.87)}.mat-datepicker-content.mat-accent .cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.mat-datepicker-content.mat-accent .cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(176,190,197,.3)}@media(hover: hover){.mat-datepicker-content.mat-accent .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(176,190,197,.3)}}.mat-datepicker-content.mat-warn .mat-calendar-body-in-range::before{background:rgba(244,67,54,.2)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range::before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-bridge-start::before,.mat-datepicker-content.mat-warn [dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, rgba(244, 67, 54, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-bridge-end::before,.mat-datepicker-content.mat-warn [dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, rgba(244, 67, 54, 0.2) 50%, rgba(249, 171, 0, 0.2) 50%)}.mat-datepicker-content.mat-warn .mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:#a8dab5}.mat-datepicker-content.mat-warn .mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-datepicker-content.mat-warn .mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:#46a35e}.mat-datepicker-content.mat-warn .mat-calendar-body-selected{background-color:#f44336;color:#fff}.mat-datepicker-content.mat-warn .mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:rgba(244,67,54,.4)}.mat-datepicker-content.mat-warn .mat-calendar-body-today.mat-calendar-body-selected{box-shadow:inset 0 0 0 1px #fff}.mat-datepicker-content.mat-warn .cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.mat-datepicker-content.mat-warn .cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(244,67,54,.3)}@media(hover: hover){.mat-datepicker-content.mat-warn .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(244,67,54,.3)}}.mat-datepicker-content-touch{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-datepicker-toggle-active{color:#c2185b}.mat-datepicker-toggle-active.mat-accent{color:#b0bec5}.mat-datepicker-toggle-active.mat-warn{color:#f44336}.mat-date-range-input-inner[disabled]{color:rgba(255,255,255,.5)}.mat-dialog-container{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12);background:#424242;color:#fff}.mat-divider{border-top-color:rgba(255,255,255,.12)}.mat-divider-vertical{border-right-color:rgba(255,255,255,.12)}.mat-expansion-panel{background:#424242;color:#fff}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-action-row{border-top-color:rgba(255,255,255,.12)}.mat-expansion-panel .mat-expansion-panel-header.cdk-keyboard-focused:not([aria-disabled=true]),.mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled=true]),.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]){background:rgba(255,255,255,.04)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded):not([aria-disabled=true]) .mat-expansion-panel-header:hover{background:#424242}}.mat-expansion-panel-header-title{color:#fff}.mat-expansion-panel-header-description,.mat-expansion-indicator::after{color:rgba(255,255,255,.7)}.mat-expansion-panel-header[aria-disabled=true]{color:rgba(255,255,255,.3)}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header{height:48px}.mat-expansion-panel-header.mat-expanded{height:64px}.mat-form-field-label{color:rgba(255,255,255,.7)}.mat-hint{color:rgba(255,255,255,.7)}.mat-form-field.mat-focused .mat-form-field-label{color:#c2185b}.mat-form-field.mat-focused .mat-form-field-label.mat-accent{color:#b0bec5}.mat-form-field.mat-focused .mat-form-field-label.mat-warn{color:#f44336}.mat-focused .mat-form-field-required-marker{color:#b0bec5}.mat-form-field-ripple{background-color:#fff}.mat-form-field.mat-focused .mat-form-field-ripple{background-color:#c2185b}.mat-form-field.mat-focused .mat-form-field-ripple.mat-accent{background-color:#b0bec5}.mat-form-field.mat-focused .mat-form-field-ripple.mat-warn{background-color:#f44336}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid) .mat-form-field-infix::after{color:#c2185b}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid).mat-accent .mat-form-field-infix::after{color:#b0bec5}.mat-form-field-type-mat-native-select.mat-focused:not(.mat-form-field-invalid).mat-warn .mat-form-field-infix::after{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-label{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-label.mat-accent,.mat-form-field.mat-form-field-invalid .mat-form-field-label .mat-form-field-required-marker{color:#f44336}.mat-form-field.mat-form-field-invalid .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple.mat-accent{background-color:#f44336}.mat-error{color:#f44336}.mat-form-field-appearance-legacy .mat-form-field-label{color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy .mat-hint{color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy .mat-form-field-underline{background-color:rgba(255,255,255,.7)}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-image:linear-gradient(to right, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 33%, transparent 0%);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-standard .mat-form-field-underline{background-color:rgba(255,255,255,.7)}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-image:linear-gradient(to right, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 33%, transparent 0%);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-fill .mat-form-field-flex{background-color:rgba(255,255,255,.1)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{background-color:rgba(255,255,255,.05)}.mat-form-field-appearance-fill .mat-form-field-underline::before{background-color:rgba(255,255,255,.5)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-label{color:rgba(255,255,255,.5)}.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-underline::before{background-color:rgba(0,0,0,0)}.mat-form-field-appearance-outline .mat-form-field-outline{color:rgba(255,255,255,.3)}.mat-form-field-appearance-outline .mat-form-field-outline-thick{color:#fff}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{color:#c2185b}.mat-form-field-appearance-outline.mat-focused.mat-accent .mat-form-field-outline-thick{color:#b0bec5}.mat-form-field-appearance-outline.mat-focused.mat-warn .mat-form-field-outline-thick{color:#f44336}.mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick{color:#f44336}.mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-label{color:rgba(255,255,255,.5)}.mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:rgba(255,255,255,.15)}.mat-icon.mat-primary{color:#c2185b}.mat-icon.mat-accent{color:#b0bec5}.mat-icon.mat-warn{color:#f44336}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{color:rgba(255,255,255,.7)}.mat-input-element:disabled,.mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after{color:rgba(255,255,255,.5)}.mat-input-element{caret-color:#c2185b}.mat-input-element::placeholder{color:rgba(255,255,255,.5)}.mat-input-element::-moz-placeholder{color:rgba(255,255,255,.5)}.mat-input-element::-webkit-input-placeholder{color:rgba(255,255,255,.5)}.mat-input-element:-ms-input-placeholder{color:rgba(255,255,255,.5)}.mat-input-element:not(.mat-native-select-inline) option{color:rgba(0,0,0,.87)}.mat-input-element:not(.mat-native-select-inline) option:disabled{color:rgba(0,0,0,.38)}.mat-form-field.mat-accent .mat-input-element{caret-color:#b0bec5}.mat-form-field.mat-warn .mat-input-element,.mat-form-field-invalid .mat-input-element{caret-color:#f44336}.mat-form-field-type-mat-native-select.mat-form-field-invalid .mat-form-field-infix::after{color:#f44336}.mat-list-base .mat-list-item{color:#fff}.mat-list-base .mat-list-option{color:#fff}.mat-list-base .mat-subheader{color:rgba(255,255,255,.7)}.mat-list-base .mat-list-item-disabled{background-color:rgba(255,255,255,.12);color:rgba(255,255,255,.5)}.mat-list-option:hover,.mat-list-option:focus,.mat-nav-list .mat-list-item:hover,.mat-nav-list .mat-list-item:focus,.mat-action-list .mat-list-item:hover,.mat-action-list .mat-list-item:focus{background:rgba(255,255,255,.04)}.mat-list-single-selected-option,.mat-list-single-selected-option:hover,.mat-list-single-selected-option:focus{background:rgba(255,255,255,.12)}.mat-menu-panel{background:#424242}.mat-menu-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-menu-item{background:rgba(0,0,0,0);color:#fff}.mat-menu-item[disabled],.mat-menu-item[disabled] .mat-menu-submenu-icon,.mat-menu-item[disabled] .mat-icon-no-color{color:rgba(255,255,255,.5)}.mat-menu-item .mat-icon-no-color,.mat-menu-submenu-icon{color:#fff}.mat-menu-item:hover:not([disabled]),.mat-menu-item.cdk-program-focused:not([disabled]),.mat-menu-item.cdk-keyboard-focused:not([disabled]),.mat-menu-item-highlighted:not([disabled]){background:rgba(255,255,255,.04)}.mat-paginator{background:#424242}.mat-paginator,.mat-paginator-page-size .mat-select-trigger{color:rgba(255,255,255,.7)}.mat-paginator-decrement,.mat-paginator-increment{border-top:2px solid #fff;border-right:2px solid #fff}.mat-paginator-first,.mat-paginator-last{border-top:2px solid #fff}.mat-icon-button[disabled] .mat-paginator-decrement,.mat-icon-button[disabled] .mat-paginator-increment,.mat-icon-button[disabled] .mat-paginator-first,.mat-icon-button[disabled] .mat-paginator-last{border-color:rgba(255,255,255,.5)}.mat-paginator-container{min-height:56px}.mat-progress-bar-background{fill:#552a3b}.mat-progress-bar-buffer{background-color:#552a3b}.mat-progress-bar-fill::after{background-color:#c2185b}.mat-progress-bar.mat-accent .mat-progress-bar-background{fill:#505455}.mat-progress-bar.mat-accent .mat-progress-bar-buffer{background-color:#505455}.mat-progress-bar.mat-accent .mat-progress-bar-fill::after{background-color:#b0bec5}.mat-progress-bar.mat-warn .mat-progress-bar-background{fill:#613532}.mat-progress-bar.mat-warn .mat-progress-bar-buffer{background-color:#613532}.mat-progress-bar.mat-warn .mat-progress-bar-fill::after{background-color:#f44336}.mat-progress-spinner circle,.mat-spinner circle{stroke:#c2185b}.mat-progress-spinner.mat-accent circle,.mat-spinner.mat-accent circle{stroke:#b0bec5}.mat-progress-spinner.mat-warn circle,.mat-spinner.mat-warn circle{stroke:#f44336}.mat-radio-outer-circle{border-color:rgba(255,255,255,.7)}.mat-radio-button.mat-primary.mat-radio-checked .mat-radio-outer-circle{border-color:#c2185b}.mat-radio-button.mat-primary .mat-radio-inner-circle,.mat-radio-button.mat-primary .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-primary.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-primary:active .mat-radio-persistent-ripple{background-color:#c2185b}.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#b0bec5}.mat-radio-button.mat-accent .mat-radio-inner-circle,.mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-accent:active .mat-radio-persistent-ripple{background-color:#b0bec5}.mat-radio-button.mat-warn.mat-radio-checked .mat-radio-outer-circle{border-color:#f44336}.mat-radio-button.mat-warn .mat-radio-inner-circle,.mat-radio-button.mat-warn .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),.mat-radio-button.mat-warn.mat-radio-checked .mat-radio-persistent-ripple,.mat-radio-button.mat-warn:active .mat-radio-persistent-ripple{background-color:#f44336}.mat-radio-button.mat-radio-disabled.mat-radio-checked .mat-radio-outer-circle,.mat-radio-button.mat-radio-disabled .mat-radio-outer-circle{border-color:rgba(255,255,255,.5)}.mat-radio-button.mat-radio-disabled .mat-radio-ripple .mat-ripple-element,.mat-radio-button.mat-radio-disabled .mat-radio-inner-circle{background-color:rgba(255,255,255,.5)}.mat-radio-button.mat-radio-disabled .mat-radio-label-content{color:rgba(255,255,255,.5)}.mat-radio-button .mat-ripple-element{background-color:#fff}.mat-select-value{color:#fff}.mat-select-placeholder{color:rgba(255,255,255,.5)}.mat-select-disabled .mat-select-value{color:rgba(255,255,255,.5)}.mat-select-arrow{color:rgba(255,255,255,.7)}.mat-select-panel{background:#424242}.mat-select-panel:not([class*=mat-elevation-z]){box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple){background:rgba(255,255,255,.12)}.mat-form-field.mat-focused.mat-primary .mat-select-arrow{color:#c2185b}.mat-form-field.mat-focused.mat-accent .mat-select-arrow{color:#b0bec5}.mat-form-field.mat-focused.mat-warn .mat-select-arrow{color:#f44336}.mat-form-field .mat-select.mat-select-invalid .mat-select-arrow{color:#f44336}.mat-form-field .mat-select.mat-select-disabled .mat-select-arrow{color:rgba(255,255,255,.5)}.mat-drawer-container{background-color:#303030;color:#fff}.mat-drawer{background-color:#424242;color:#fff}.mat-drawer.mat-drawer-push{background-color:#424242}.mat-drawer:not(.mat-drawer-side){box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-drawer-side{border-right:solid 1px rgba(255,255,255,.12)}.mat-drawer-side.mat-drawer-end{border-left:solid 1px rgba(255,255,255,.12);border-right:none}[dir=rtl] .mat-drawer-side{border-left:solid 1px rgba(255,255,255,.12);border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-left:none;border-right:solid 1px rgba(255,255,255,.12)}.mat-drawer-backdrop.mat-drawer-shown{background-color:rgba(189,189,189,.6)}.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#b0bec5}.mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:rgba(176,190,197,.54)}.mat-slide-toggle.mat-checked .mat-ripple-element{background-color:#b0bec5}.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb{background-color:#c2185b}.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar{background-color:rgba(194,24,91,.54)}.mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element{background-color:#c2185b}.mat-slide-toggle.mat-warn.mat-checked .mat-slide-toggle-thumb{background-color:#f44336}.mat-slide-toggle.mat-warn.mat-checked .mat-slide-toggle-bar{background-color:rgba(244,67,54,.54)}.mat-slide-toggle.mat-warn.mat-checked .mat-ripple-element{background-color:#f44336}.mat-slide-toggle:not(.mat-checked) .mat-ripple-element{background-color:#fff}.mat-slide-toggle-thumb{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);background-color:#bdbdbd}.mat-slide-toggle-bar{background-color:rgba(255,255,255,.5)}.mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-primary .mat-slider-track-fill,.mat-slider.mat-primary .mat-slider-thumb,.mat-slider.mat-primary .mat-slider-thumb-label{background-color:#c2185b}.mat-slider.mat-primary .mat-slider-thumb-label-text{color:#fff}.mat-slider.mat-primary .mat-slider-focus-ring{background-color:rgba(194,24,91,.2)}.mat-slider.mat-accent .mat-slider-track-fill,.mat-slider.mat-accent .mat-slider-thumb,.mat-slider.mat-accent .mat-slider-thumb-label{background-color:#b0bec5}.mat-slider.mat-accent .mat-slider-thumb-label-text{color:rgba(0,0,0,.87)}.mat-slider.mat-accent .mat-slider-focus-ring{background-color:rgba(176,190,197,.2)}.mat-slider.mat-warn .mat-slider-track-fill,.mat-slider.mat-warn .mat-slider-thumb,.mat-slider.mat-warn .mat-slider-thumb-label{background-color:#f44336}.mat-slider.mat-warn .mat-slider-thumb-label-text{color:#fff}.mat-slider.mat-warn .mat-slider-focus-ring{background-color:rgba(244,67,54,.2)}.mat-slider:hover .mat-slider-track-background,.mat-slider.cdk-focused .mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-disabled .mat-slider-track-background,.mat-slider.mat-slider-disabled .mat-slider-track-fill,.mat-slider.mat-slider-disabled .mat-slider-thumb{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-disabled:hover .mat-slider-track-background{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value .mat-slider-focus-ring{background-color:rgba(255,255,255,.12)}.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing .mat-slider-thumb,.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing .mat-slider-thumb-label{background-color:#fff}.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused .mat-slider-thumb,.mat-slider.mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused .mat-slider-thumb-label{background-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing) .mat-slider-thumb{border-color:rgba(255,255,255,.3);background-color:rgba(0,0,0,0)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing):hover .mat-slider-thumb,.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing).cdk-focused .mat-slider-thumb{border-color:rgba(255,255,255,.3)}.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing):hover.mat-slider-disabled .mat-slider-thumb,.mat-slider.mat-slider-min-value:not(.mat-slider-thumb-label-showing).cdk-focused.mat-slider-disabled .mat-slider-thumb{border-color:rgba(255,255,255,.3)}.mat-slider-has-ticks .mat-slider-wrapper::after{border-color:rgba(255,255,255,.7)}.mat-slider-horizontal .mat-slider-ticks{background-image:repeating-linear-gradient(to right, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent);background-image:-moz-repeating-linear-gradient(0.0001deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent)}.mat-slider-vertical .mat-slider-ticks{background-image:repeating-linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7) 2px, transparent 0, transparent)}.mat-step-header.cdk-keyboard-focused,.mat-step-header.cdk-program-focused,.mat-step-header:hover:not([aria-disabled]),.mat-step-header:hover[aria-disabled=false]{background-color:rgba(255,255,255,.04)}.mat-step-header:hover[aria-disabled=true]{cursor:default}@media(hover: none){.mat-step-header:hover{background:none}}.mat-step-header .mat-step-label,.mat-step-header .mat-step-optional{color:rgba(255,255,255,.7)}.mat-step-header .mat-step-icon{background-color:rgba(255,255,255,.7);color:#fff}.mat-step-header .mat-step-icon-selected,.mat-step-header .mat-step-icon-state-done,.mat-step-header .mat-step-icon-state-edit{background-color:#c2185b;color:#fff}.mat-step-header.mat-accent .mat-step-icon{color:rgba(0,0,0,.87)}.mat-step-header.mat-accent .mat-step-icon-selected,.mat-step-header.mat-accent .mat-step-icon-state-done,.mat-step-header.mat-accent .mat-step-icon-state-edit{background-color:#b0bec5;color:rgba(0,0,0,.87)}.mat-step-header.mat-warn .mat-step-icon{color:#fff}.mat-step-header.mat-warn .mat-step-icon-selected,.mat-step-header.mat-warn .mat-step-icon-state-done,.mat-step-header.mat-warn .mat-step-icon-state-edit{background-color:#f44336;color:#fff}.mat-step-header .mat-step-icon-state-error{background-color:rgba(0,0,0,0);color:#f44336}.mat-step-header .mat-step-label.mat-step-label-active{color:#fff}.mat-step-header .mat-step-label.mat-step-label-error{color:#f44336}.mat-stepper-horizontal,.mat-stepper-vertical{background-color:#424242}.mat-stepper-vertical-line::before{border-left-color:rgba(255,255,255,.12)}.mat-horizontal-stepper-header::before,.mat-horizontal-stepper-header::after,.mat-stepper-horizontal-line{border-top-color:rgba(255,255,255,.12)}.mat-horizontal-stepper-header{height:72px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header,.mat-vertical-stepper-header{padding:24px 24px}.mat-stepper-vertical-line::before{top:-16px;bottom:-16px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::after,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::before{top:36px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{top:36px}.mat-sort-header-arrow{color:#c6c6c6}.mat-tab-nav-bar,.mat-tab-header{border-bottom:1px solid rgba(255,255,255,.12)}.mat-tab-group-inverted-header .mat-tab-nav-bar,.mat-tab-group-inverted-header .mat-tab-header{border-top:1px solid rgba(255,255,255,.12);border-bottom:none}.mat-tab-label,.mat-tab-link{color:#fff}.mat-tab-label.mat-tab-disabled,.mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.5)}.mat-tab-header-pagination-chevron{border-color:#fff}.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:rgba(255,255,255,.5)}.mat-tab-group[class*=mat-background-]>.mat-tab-header,.mat-tab-nav-bar[class*=mat-background-]{border-bottom:none;border-top:none}.mat-tab-group.mat-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(233,30,99,.3)}.mat-tab-group.mat-primary .mat-ink-bar,.mat-tab-nav-bar.mat-primary .mat-ink-bar{background-color:#c2185b}.mat-tab-group.mat-primary.mat-background-primary>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-primary.mat-background-primary>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-primary.mat-background-primary>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-primary.mat-background-primary>.mat-tab-link-container .mat-ink-bar{background-color:#fff}.mat-tab-group.mat-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(207,216,220,.3)}.mat-tab-group.mat-accent .mat-ink-bar,.mat-tab-nav-bar.mat-accent .mat-ink-bar{background-color:#b0bec5}.mat-tab-group.mat-accent.mat-background-accent>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-accent.mat-background-accent>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-accent.mat-background-accent>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-accent.mat-background-accent>.mat-tab-link-container .mat-ink-bar{background-color:rgba(0,0,0,.87)}.mat-tab-group.mat-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-warn .mat-ink-bar,.mat-tab-nav-bar.mat-warn .mat-ink-bar{background-color:#f44336}.mat-tab-group.mat-warn.mat-background-warn>.mat-tab-header .mat-ink-bar,.mat-tab-group.mat-warn.mat-background-warn>.mat-tab-link-container .mat-ink-bar,.mat-tab-nav-bar.mat-warn.mat-background-warn>.mat-tab-header .mat-ink-bar,.mat-tab-nav-bar.mat-warn.mat-background-warn>.mat-tab-link-container .mat-ink-bar{background-color:#fff}.mat-tab-group.mat-background-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-primary .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(233,30,99,.3)}.mat-tab-group.mat-background-primary>.mat-tab-header,.mat-tab-group.mat-background-primary>.mat-tab-link-container,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination{background-color:#c2185b}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-tab-link{color:#fff}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.4)}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-primary>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-focus-indicator::before{border-color:#fff}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#fff;opacity:.4}.mat-tab-group.mat-background-primary>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-primary>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-primary>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-primary>.mat-tab-header-pagination .mat-ripple-element{background-color:#fff;opacity:.12}.mat-tab-group.mat-background-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-accent .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(207,216,220,.3)}.mat-tab-group.mat-background-accent>.mat-tab-header,.mat-tab-group.mat-background-accent>.mat-tab-link-container,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination{background-color:#b0bec5}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-tab-link{color:rgba(0,0,0,.87)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(0,0,0,.4)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-accent>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-focus-indicator::before{border-color:rgba(0,0,0,.87)}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#000;opacity:.4}.mat-tab-group.mat-background-accent>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-accent>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-accent>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-accent>.mat-tab-header-pagination .mat-ripple-element{background-color:#000;opacity:.12}.mat-tab-group.mat-background-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-group.mat-background-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-label.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-label.cdk-program-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-link.cdk-keyboard-focused:not(.mat-tab-disabled),.mat-tab-nav-bar.mat-background-warn .mat-tab-link.cdk-program-focused:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-background-warn>.mat-tab-header,.mat-tab-group.mat-background-warn>.mat-tab-link-container,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination{background-color:#f44336}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-label,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-tab-link,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-label,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-tab-link{color:#fff}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-tab-link.mat-tab-disabled,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-label.mat-tab-disabled,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-tab-link.mat-tab-disabled{color:rgba(255,255,255,.4)}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-group.mat-background-warn>.mat-tab-header .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-focus-indicator::before,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-focus-indicator::before{border-color:#fff}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination-disabled .mat-tab-header-pagination-chevron{border-color:#fff;opacity:.4}.mat-tab-group.mat-background-warn>.mat-tab-header .mat-ripple-element,.mat-tab-group.mat-background-warn>.mat-tab-link-container .mat-ripple-element,.mat-tab-group.mat-background-warn>.mat-tab-header-pagination .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-link-container .mat-ripple-element,.mat-tab-nav-bar.mat-background-warn>.mat-tab-header-pagination .mat-ripple-element{background-color:#fff;opacity:.12}.mat-toolbar{background:#212121;color:#fff}.mat-toolbar.mat-primary{background:#c2185b;color:#fff}.mat-toolbar.mat-accent{background:#b0bec5;color:rgba(0,0,0,.87)}.mat-toolbar.mat-warn{background:#f44336;color:#fff}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar-multiple-rows{min-height:64px}.mat-toolbar-row,.mat-toolbar-single-row{height:64px}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:56px}.mat-toolbar-row,.mat-toolbar-single-row{height:56px}}.mat-tooltip{background:rgba(97,97,97,.9)}.mat-tree{background:#424242}.mat-tree-node,.mat-nested-tree-node{color:#fff}.mat-tree-node{min-height:48px}.mat-snack-bar-container{color:rgba(0,0,0,.87);background:#fafafa;box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-simple-snackbar-action{color:inherit}", "html {\n    box-sizing: border-box;\n    -webkit-box-sizing: border-box;\n    -moz-box-sizing: border-box;\n    overscroll-behavior: none;\n}\n\n*,\n*:before,\n*:after {\n    box-sizing: inherit;\n    -webkit-box-sizing: inherit;\n    -moz-box-sizing: inherit;\n    margin: 0;\n    padding: 0;\n    -webkit-backface-visibility: hidden;\n    -webkit-touch-collout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n}\n\nhtml,\nbody,\ndiv,\nspan,\napplet,\nobject,\niframe,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np,\nblockquote,\npre,\na,\nabbr,\nacronym,\naddress,\nbig,\ncite,\ncode,\ndel,\ndfn,\nem,\nimg,\nins,\nkbd,\nq,\ns,\nsamp,\nsmall,\nstrike,\nstrong,\nsub,\nsup,\ntt,\nvar,\nb,\nu,\ni,\ncenter,\ndl,\ndt,\ndd,\nol,\nul,\nli,\nfieldset,\nform,\nlabel,\nlegend,\ntable,\ncaption,\ntbody,\ntfoot,\nthead,\ntr,\nth,\ntd,\narticle,\naside,\ncanvas,\ndetails,\nembed,\nfigure,\nfigcaption,\nfooter,\nheader,\nhgroup,\nmenu,\nnav,\noutput,\nruby,\nsection,\nsummary,\ntime,\nmark,\naudio,\nvideo {\n    border: 0;\n    font-size: 100%;\n    font: inherit;\n    vertical-align: baseline;\n}\n\narticle,\naside,\ndetails,\nfigcaption,\nfigure,\nfooter,\nheader,\nhgroup,\nmenu,\nnav,\nsection {\n    display: block;\n}\n\nbody {\n    line-height: 1;\n    font-style: normal;\n}\n\nol,\nul {\n    list-style: none;\n}\n\nblockquote,\nq {\n    quotes: none;\n}\n\nblockquote:before,\nblockquote:after,\nq:before,\nq:after {\n    content: none;\n}\n\ntable {\n    border-collapse: collapse;\n    border-spacing: 0;\n}\n\ntd,\nth {\n    padding: 0;\n}\n\ninput {\n    outline: none;\n\n    &:-webkit-autofill {\n        -webkit-box-shadow: 0 0 0 1000px white inset;\n    }\n}\n\nbutton,\nhtml input[type='button'],\ninput[type='reset'],\ninput[type='submit'] {\n    -webkit-appearance: button;\n    cursor: pointer;\n    outline: none;\n}\n\nbutton[disabled],\nhtml input[disabled] {\n    cursor: default;\n}\n\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n    border: 0;\n    padding: 0;\n}\n\ninput {\n    line-height: normal;\n}\n\ninput[type='search'] {\n    -webkit-appearance: textfield;\n    box-sizing: content-box;\n}\n\ninput[type='search']::-webkit-search-cancel-button,\ninput[type='search']::-webkit-search-decoration {\n    -webkit-appearance: none;\n}\n\na {\n    text-decoration: none;\n\n    &:active,\n    &:hover,\n    &:focus {\n        outline: 0;\n    }\n}\n\ni {\n    font-style: italic;\n}\n\nb,\nstrong {\n    font-weight: 700;\n}\n", "@import '@angular/material/prebuilt-themes/pink-bluegrey.css';\n\n@import 'assets/scss/base/index';\n\n@import 'assets/scss/layout/index';\n\n@import 'assets/scss/components/index';\n\n@import 'assets/scss/modals/index';\n\n@import 'assets/scss/pages/index';\n\n@import 'assets/scss/helpers/index';\n", ".cdk-dialog-container {\n    &:focus {\n        outline: none;\n    }\n}\n", ":root {\n    // red\n    --red-600: rgba(255, 103, 103, 0.75);\n    --red-500: #ff6767;\n    --red-100: #ffcbcb;\n\n    // blue\n    --blue-900: #0c0c3a;\n    --blue-800: #0c1243;\n    --blue-700: #0f2055;\n    --blue-500: #11316b;\n    --blue-450: #144182;\n    --blue-400: rgba(31, 143, 235, 0.3);\n    --blue-300: rgba(31, 143, 235, 0.15);\n\n    // orange\n    --orange-500: #ff6f00;\n\n    // gray\n    --gray-900: rgba(255, 255, 255, 0.1);\n    --gray-800: rgba(255, 255, 255, 0.2);\n    --gray-700: rgba(255, 255, 255, 0.3);\n    --gray-600: rgba(255, 255, 255, 0.4);\n    --gray-500: rgba(255, 255, 255, 0.5);\n    --gray-400: rgba(255, 255, 255, 0.75);\n\n    // aqua\n    --aqua-500: #16d1d6;\n\n    // azure\n    --azure-600: #1c72b9;\n    --azure-500: #1f8feb;\n\n    // white\n    --white-500: #ffffff;\n\n    // black\n    --black-300: rgba(0, 0, 0, 0.6);\n\n    // amethyst\n    --amethyst-500: #9a69f7;\n\n    // silver\n    --silver-500: #8898b5;\n\n    // gradient\n    --gradietAquaToBlue: radial-gradient(100% 188.88% at 0% 0%, #16d1d6 0%, #274cff 100%);\n\n    --gradietLightAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, rgba(163, 102, 255, 0.5) 0%, rgba(96, 31, 255, 0.5) 100%);\n    --gradietAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, #a366ff 0%, #601fff 100%);\n\n    // shadow\n    --shadow-gray: 0px 2.11765px 5.64706px rgba(0, 0, 0, 0.15), 0px 2.11765px 0.705882px rgba(0, 0, 0, 0.06);\n    --shadow-black-300: 0 0 1rem var(--black-300);\n\n    // chart\n    --chartOptionsBackgroundColor: #2b3644;\n    --chartOptionsHoverColor: #556576;\n}\n", ":root {\n    --border-radius: 0.8rem;\n}\n\n.light {\n    --main-background: #f0f6fb;\n    --page-content-background: #fcfcfc;\n    --sidebar-background: #fcfcfc;\n    --tab-content-background: #fcfcfc;\n    --tab-preloader-background: #fcfcfc;\n    --tab-preloader-text: var(--blue-900);\n    --tab-header-background: #d9ebfa;\n    --tab-header-active-background: #fcfcfc;\n    --main-text: var(--blue-900);\n    --button-color: var(--blue-900);\n    --synchronization-status-color: var(--blue-900);\n    --synchronization-progress-bar-container-background: rgba(0, 0, 0, 0.1);\n    --dialog-background: #ffffff;\n    --block-sync: #505274;\n    --control-alias-wallet-background: #1f8feb1a;\n    --control-alias-wallet-text: var(--main-text);\n    --alias-history-count-text: #1f8feb;\n\n    // sidebar\n    --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);\n    --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);\n\n    // auth\n    --auth-card-background: #fcfcfc;\n    --auth-card-form-background: #ffffff;\n    --auth-card-form-border: 1px solid #1f8feb33;\n\n    // form\n    --form-card-background: #f0f6fb;\n\n    // ng-select\n    --ng-select-bg: #dbecfa;\n    --ng-select-border: #1f8feb40;\n    --ng-select-highlight: #1f8feb10;\n    --ng-select-circle-border: 1px solid #1f8feb50;\n    --ng-select-circle-background: #1f8feb;\n\n    // field\n    --input-background: #ffffff;\n    --input-color: var(--blue-900);\n    --input-placeholder: #0c0c3a4d;\n    --border: 1px solid #1f8feb66;\n    --border-not-empty: 1px solid #1f8feb99;\n    --border-disabled: 1px solid #1f8feb66;\n    --border-error: 1px solid var(--red-600);\n    --hint-text: #0c0c3a99;\n\n    // field amount\n    --amount-ticker-text: #0c0c3a99;\n    --amount-btn-revers-background: #1f8feb1a;\n\n    // checkbox\n    --checkbox-border: 1px solid #1f8feb66;\n    --checkbox-hover-border: 1px solid #1f8feb99;\n    --checkbox-active-border: 1px solid #1f8feb99;\n    --checkbox-checked-background: #1f8feb;\n\n    // switch\n    --switch-on-background: var(--aqua-500);\n    --switch-off-background: #0c0c3a1a;\n    --switch-circle-background: var(--white-500);\n\n    //wallet\n    --wallet-background: #1f8feb26;\n    --wallet-active-background: var(--gradietAquaToBlue);\n    --wallet-border: 1px solid #1f8feb00;\n    --wallet-border-color-hover: #1f8feb;\n    --wallet-auditable-watch-only-background: radial-gradient(\n        100% 246.57% at 0% 0%,\n        rgba(163, 102, 255, 0.75) 0%,\n        rgba(96, 31, 255, 0.75) 100%\n    );\n    --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);\n    --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);\n    --wallet-watch-only-after-background: #dbecf9;\n    --wallet-watch-only-text: var(--blue-900);\n    --wallet-active-text: #ffffff;\n    --wallet-text: var(--blue-900);\n\n    // table\n    --table-thead-bg: #1f8feb4d;\n    --table-row-bg: #dbecf9;\n    --table-rounded-corners-border: 1px solid #d9dfe8;\n\n    --table-info-border: 1px solid #d9dfe8;\n    --table-info-label-background: #f0f6fb;\n\n    // buttons\n    --btn-icon-background: #1f8feb1a;\n    --btn-icon-hover-background: #1f8feb4d;\n\n    // list\n    --list-background: #f9fcff;\n    --list-border: 1px solid #1f8feb80;\n    --list-item-hover-background: #1f8feb1a;\n\n    // tooltip\n    --tooltip-background: #8dbee8;\n\n    // details\n    --details-background: #dbecfa;\n}\n.dark {\n    --main-background: var(--blue-900);\n    --page-content-background: var(--blue-700);\n    --sidebar-background: var(--blue-700);\n    --tab-content-background: var(--blue-700);\n    --tab-preloader-background: var(--blue-700);\n    --tab-preloader-text: #ffffff;\n    --tab-header-background: var(--blue-800);\n    --tab-header-active-background: var(--blue-700);\n    --main-text: var(--white-500);\n    --button-color: var(--white-500);\n    --synchronization-status-color: var(--white-500);\n    --synchronization-progress-bar-container-background: var(--gray-900);\n    --dialog-background: var(--blue-700);\n    --block-sync: #a8abb5;\n    --control-alias-wallet-background: #ffffff1a;\n    --control-alias-wallet-text: var(--main-text);\n    --alias-history-count-text: #1f8feb;\n\n    // sidebar\n    --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);\n    --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);\n\n    // auth\n    --auth-card-background: var(--blue-700);\n    --auth-card-form-background: var(--blue-500);\n    --auth-card-form-border: 1px solid transparent;\n\n    //form\n    --form-card-background: var(--blue-500);\n\n    //ng-select\n    --ng-select-bg: var(--blue-500);\n    --ng-select-border: var(--gray-500);\n    --ng-select-highlight: var(--gray-900);\n    --ng-select-circle-border: 1px solid white;\n    --ng-select-circle-background: var(--white-500);\n\n    // field\n    --input-background: transparent;\n    --input-color: var(--white-500);\n    --input-placeholder: var(--gray-800);\n    --border: 1px solid var(--gray-800);\n    --border-not-empty: 1px solid var(--gray-500);\n    --border-disabled: 1px solid var(--gray-800);\n    --border-error: 1px solid var(--red-600);\n    --hint-text: #ffffff60;\n\n    // field amount\n    --amount-ticker-text: #ffffff60;\n    --amount-btn-revers-background: #ffffff1a;\n\n    // checkbox\n    --checkbox-border: 1px solid #ffffff33;\n    --checkbox-hover-border: 1px solid #ffffff66;\n    --checkbox-active-border: 1px solid #ffffff66;\n    --checkbox-checked-background: #ffffff;\n\n    // switch\n    --switch-on-background: var(--aqua-500);\n    --switch-off-background: var(--gray-800);\n    --switch-circle-background: var(--white-500);\n\n    //wallet\n    --wallet-background: var(--blue-500);\n    --wallet-active-background: var(--gradietAquaToBlue);\n    --wallet-border: 1px solid var(--blue-500);\n    --wallet-border-color-hover: var(--gray-800);\n    --wallet-auditable-watch-only-background: var(--gradietLightAmethystToPurpurle);\n    --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);\n    --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);\n    --wallet-watch-only-after-background: var(--blue-500);\n    --wallet-watch-only-text: #ffffff;\n    --wallet-active-text: #ffffff;\n    --wallet-text: #ffffff;\n\n    // table\n    --table-thead-bg: var(--blue-400);\n    --table-row-bg: var(--blue-300);\n    --table-rounded-corners-border: 1px solid #33426e;\n\n    --table-info-border: 1px solid #33426e;\n    --table-info-label-background: var(--blue-500);\n\n    // buttons\n    --btn-icon-background: var(--gray-900);\n    --btn-icon-hover-background: var(--gray-700);\n\n    // list\n    --list-background: var(--blue-500);\n    --list-border: var(--border);\n    --list-item-hover-background: var(--gray-900);\n\n    // tooltip\n    --tooltip-background: var(--blue-450);\n\n    // details\n    --details-background: var(--blue-500);\n}\n", "@font-face {\n    font-family: SF-Pro-Rounded;\n    src: url(~src/assets/fonts/SF-Pro-Rounded-Regular.ttf);\n    font-weight: 400;\n}\n\n@font-face {\n    font-family: SF-Pro-Rounded;\n    src: url(~src/assets/fonts/SF-Pro-Rounded-Medium.ttf);\n    font-weight: 500;\n}\n\n@font-face {\n    font-family: SF-Pro-Rounded;\n    src: url(~src/assets/fonts/SF-Pro-Rounded-Semibold.ttf);\n    font-weight: 600;\n}\n", "html,\ninput,\ntextarea,\nselect,\nbutton {\n    color: var(--main-text);\n    font-family: SF-Pro-Rounded, sans-serif;\n    font-weight: 400;\n}\n\nh1 {\n    font-size: 3.6rem;\n    line-height: 1.2;\n}\n\nh2 {\n    font-size: 2.8rem;\n    line-height: 1.2;\n}\n\nh3 {\n    font-size: 2rem;\n    line-height: 1.2;\n}\n\ndiv,\nspan,\napplet,\nobject,\niframe,\nh4,\nh5,\nh6,\np,\nblockquote,\npre,\na,\nabbr,\nacronym,\naddress,\nbig,\ncite,\ncode,\ndel,\ndfn,\nem,\nimg,\nins,\nkbd,\nq,\ns,\nsamp,\nsmall,\nstrike,\nstrong,\nsub,\nsup,\ntt,\nvar,\nb,\nu,\ni,\ncenter,\ndl,\ndt,\ndd,\nol,\nul,\nli,\nfieldset,\nform,\nlabel,\nlegend,\ntable,\ncaption,\ntbody,\ntfoot,\nthead,\ntr,\nth,\ntd,\narticle,\naside,\ncanvas,\ndetails,\nembed,\nfigure,\nfigcaption,\nfooter,\nheader,\nhgroup,\nmenu,\nnav,\noutput,\nruby,\nsection,\nsummary,\ntime,\nmark,\naudio,\nvideo {\n    font-size: 1.8rem;\n    line-height: 1.2;\n}\n", "::-webkit-scrollbar {\n    background-color: transparent;\n    cursor: default;\n    width: 1rem;\n    height: 1rem;\n}\n\n::-webkit-scrollbar-track {\n    background: transparent;\n}\n\n::-webkit-scrollbar-thumb {\n    background-color: var(--silver-500);\n    background-clip: padding-box;\n    border: 0.2rem solid transparent;\n    border-radius: 1rem;\n}\n\n::-webkit-scrollbar-thumb:hover {\n    background-color: var(--silver-500);\n}\n\nbody {\n    &::-webkit-scrollbar-corner {\n        background-color: var(--blue-900);\n    }\n}\n\n.scrolled-content {\n    overflow-y: scroll;\n    overflow-x: hidden;\n    height: auto;\n    margin-right: -2rem;\n\n    &::-webkit-scrollbar {\n        width: 2rem;\n    }\n\n    &::-webkit-scrollbar-thumb {\n        border: 0.8rem solid transparent;\n    }\n}\n", ".sr-only {\n    position: absolute !important;\n    width: 1px !important;\n    height: 1px !important;\n    padding: 0 !important;\n    margin: -1px !important;\n    overflow: hidden !important;\n    clip: rect(0, 0, 0, 0) !important;\n    white-space: nowrap !important;\n    border: 0 !important;\n}\n", "html,\nbody {\n    position: fixed;\n    overflow: hidden;\n    overscroll-behavior: none;\n\n    width: 100%;\n    min-width: 1200px;\n    max-width: 100vw;\n\n    height: 100vh;\n    min-height: 700px;\n}\n\nbody {\n    background: var(--main-background);\n    color: var(--main-text);\n}\n\napp-root {\n    display: flex;\n    flex-wrap: nowrap;\n\n    width: 100%;\n    height: 100%;\n}\n\n.page-container {\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n\n    .toolbar {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        min-height: 40px;\n        flex: 0 0 auto;\n\n        .left,\n        .right {\n            display: flex;\n            align-items: center;\n        }\n    }\n\n    .page-content {\n        width: 100%;\n        height: auto;\n        display: flex;\n        flex-direction: column;\n        flex: auto;\n        overflow: hidden;\n        padding: 2rem;\n        border-radius: 0.8rem;\n        background-color: var(--page-content-background);\n    }\n}\n", ".alias {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.control-alias-wallet-container {\n    position: relative;\n    padding: 0.4rem 0.6rem;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    flex-wrap: nowrap;\n    cursor: pointer;\n\n    .alias {\n        border-radius: 0.8rem;\n        padding: 7px 12px 7px;\n        background: var(--control-alias-wallet-background);\n        color: var(--control-alias-wallet-text);\n        font-size: 1.8rem;\n\n        display: inline-flex;\n        align-items: center;\n\n        mat-icon {\n            margin-left: 0.6rem;\n        }\n    }\n\n    .alias-history-count {\n        margin-left: 1rem;\n        font-size: 1.8rem;\n        color: var(--control-alias-wallet-text);\n        opacity: 0.7;\n    }\n\n    &.available {\n        &:after {\n            display: block;\n            content: '';\n            width: 1.4rem;\n            height: 1.4rem;\n            overflow: hidden;\n            position: absolute;\n            left: 0;\n            top: 0;\n            background-image: url('~src/assets/icons/white/crown.svg');\n            background-repeat: no-repeat;\n            background-size: contain;\n        }\n    }\n}\n\n.alias-container {\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    flex-wrap: nowrap;\n\n    &.available {\n        padding: 0.4rem 0.6rem;\n        .alias {\n            padding: 0.4rem 1.6rem;\n            min-height: 3.2rem;\n            background: var(--gradietAquaToBlue);\n            color: #ffffff;\n            border-radius: 0.8rem;\n            position: relative;\n        }\n\n        &:after {\n            display: block;\n            content: '';\n            width: 1.4rem;\n            height: 1.4rem;\n            overflow: hidden;\n            position: absolute;\n            left: 0;\n            top: 0;\n            background-image: url('~src/assets/icons/white/crown.svg');\n            background-repeat: no-repeat;\n            background-size: contain;\n        }\n    }\n}\n\n.alias-history-count {\n    margin-left: 1rem;\n    color: var(--alias-history-count-text);\n    font-size: 1.8rem;\n}\n\n.alias-history-list {\n    max-height: 8rem;\n    overflow: auto;\n}\n", "button,\n.btn {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    color: var(--button-color);\n    transition: all 0.25s ease;\n    border: none;\n    outline: none;\n    background-color: transparent;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    border-radius: 0.8rem;\n    font-size: 1.8rem;\n\n    &:not(:disabled) {\n        cursor: pointer;\n    }\n\n    &:not(:disabled):hover {\n        cursor: pointer;\n    }\n\n    &:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n    }\n\n    &.primary,\n    &.outline {\n        width: 100%;\n        padding: 0 2rem 0 2rem;\n\n        &.small {\n            min-height: 4.5rem;\n        }\n\n        &.big {\n            min-height: 5.3rem;\n        }\n    }\n\n    &.primary {\n        background-color: var(--azure-500);\n        color: var(--white-500);\n\n        &:not(:disabled) {\n            &:focus,\n            &:hover,\n            &.active {\n                background-color: var(--azure-600);\n            }\n        }\n    }\n\n    &.outline {\n        border: 1px solid var(--azure-500);\n\n        &:not(:disabled) {\n            &:hover,\n            &:focus,\n            &.active {\n                background-color: var(--gray-900);\n            }\n        }\n    }\n\n    &.btn-icon {\n        min-width: 2rem;\n        min-height: 2rem;\n\n        &.small {\n            min-width: 2.8rem;\n            min-height: 2.8rem;\n        }\n\n        &.big {\n            min-width: 4rem;\n            min-height: 4rem;\n        }\n    }\n\n    &.btn-icon {\n        background-color: var(--btn-icon-background);\n        transition: background-color 0.2s ease-in-out;\n\n        &.circle {\n            border-radius: 50%;\n        }\n\n        .row-options {\n        }\n\n        &:hover,\n        &:focus {\n            background-color: var(--btn-icon-hover-background);\n        }\n    }\n}\n\n.btn-light-background {\n    background-color: var(--btn-icon-background);\n\n    &:not(:disabled):hover {\n        background-color: var(--btn-icon-hover-background);\n    }\n}\n\n.light {\n    button,\n    .btn {\n        &.outline {\n            &:not(:disabled) {\n                &:hover,\n                &:focus,\n                &.active {\n                    background-color: rgba(0, 0, 0, 0.05);\n                }\n            }\n        }\n\n        &.btn-icon {\n            &.row-options {\n                color: #1f8feb;\n            }\n        }\n    }\n}\n", ".ngx-contextmenu {\n    &--dropdown-menu {\n        border: none;\n        padding: 0;\n        background-color: var(--chartOptionsBackgroundColor);\n        box-shadow: var(--shadow-black-300);\n    }\n\n    li {\n        display: block;\n        font-size: 1.3rem;\n        text-transform: uppercase;\n        text-align: center;\n    }\n\n    button {\n        display: block;\n        padding: 0.5em 1em;\n        color: var(--white-500);\n        border-radius: 0;\n        width: 100%;\n\n        &:hover {\n            background-color: var(--chartOptionsHoverColor);\n            color: var(--white-500);\n        }\n    }\n}\n", ".dropdown {\n    position: relative;\n\n    .content-bottom-right {\n        position: absolute;\n        top: 5rem;\n        right: 0;\n        width: 19rem;\n        z-index: 99;\n    }\n\n    .item {\n        height: 3.9rem;\n        display: flex;\n        align-items: center;\n        padding: 0.5rem;\n        &:hover {\n            background-color: var(--gray-900);\n            cursor: pointer;\n        }\n\n        .alias {\n            width: 100%;\n            margin-right: 0.3rem;\n            padding: 0.4rem 1rem;\n        }\n    }\n}\n\n.list {\n    border-radius: 0.8rem;\n    background-color: var(--list-background);\n    border: var(--list-border);\n    .item {\n        &:hover,\n        .active {\n            background-color: var(--list-item-hover-background);\n        }\n\n        button {\n            display: inline-flex;\n            white-space: nowrap;\n            justify-content: flex-start;\n            border-radius: 0;\n            span {\n                overflow: hidden;\n                text-overflow: ellipsis;\n                white-space: nowrap;\n            }\n        }\n    }\n}\n\n.zano-autocomplete-panel {\n    border-radius: 0.8rem !important;\n    background-color: var(--list-background);\n    border: var(--list-border);\n\n    .mat-option {\n        height: 4rem;\n        line-height: 1;\n        padding: 0;\n        cursor: pointer;\n        color: var(--main-text);\n        &:hover {\n            background-color: var(--list-item-hover-background);\n        }\n    }\n\n    .mat-option-text {\n        padding: 0.4rem;\n    }\n}\n", ".form {\n    max-width: 50rem;\n    width: 100%;\n    border-radius: 0.8rem;\n\n    .error {\n        color: var(--red-500);\n    }\n\n    &__card {\n        display: flex;\n        flex-direction: column;\n        padding: 2rem 2rem 0;\n        margin-bottom: 2rem;\n        border-radius: 8px;\n        background-color: var(--form-card-background);\n    }\n\n    &__row {\n        display: grid;\n        width: 100%;\n        grid-template-columns: repeat(2, 1fr);\n        justify-content: space-between;\n        grid-gap: 20px;\n    }\n\n    &__field {\n        position: relative;\n        display: flex;\n        flex: 0 0 auto;\n        flex-direction: column;\n        align-items: flex-start;\n        padding-bottom: 2rem;\n        width: 100%;\n\n        &--input {\n            /* If input is not empty */\n            &:not(:placeholder-shown):not(.invalid):not(.ng-invalid) {\n                /* You need to add a placeholder to your fields. For example: <input \"placeholder=\" \"/> */\n                border: var(--border-not-empty);\n            }\n\n            &:not(:placeholder-shown) {\n                &.invalid,\n                &.ng-touched.ng-invalid {\n                    border: var(--border-error);\n                }\n            }\n\n            /* If input is empty */\n            &:placeholder-shown {\n                border: var(--border);\n            }\n        }\n\n        label,\n        .label {\n            margin-bottom: 0.8rem;\n            color: var(--azure-500);\n        }\n\n        &--row {\n            display: flex;\n            flex: 0 0 auto;\n\n            > div,\n            > fieldset {\n                max-width: calc(50% - 1rem);\n                width: 100%;\n\n                &:first-child {\n                    margin-right: 1rem;\n                }\n\n                &:last-child {\n                    margin-left: 1rem;\n                }\n            }\n        }\n\n        &--input,\n        &--select {\n            border: var(--border);\n            border-radius: 0.8rem;\n            outline: none;\n            padding: 0 1.2rem;\n            width: 100%;\n            height: 4rem;\n            background-color: var(--input-background);\n            overflow: hidden;\n            text-overflow: ellipsis;\n            color: var(--input-color);\n            font-size: 1.8rem;\n            line-height: 1.2;\n            transition: border 0.2s ease-in-out;\n\n            &:disabled {\n                border: var(--border-disabled);\n                cursor: not-allowed;\n            }\n\n            &:read-only {\n                cursor: default;\n            }\n\n            &:not(:disabled):not(:read-only) {\n                &:hover {\n                    cursor: pointer;\n                }\n            }\n\n            &::placeholder {\n                color: var(--input-placeholder);\n            }\n\n            &.invalid,\n            &.ng-touched.ng-invalid {\n                border: var(--border-error);\n\n                &::placeholder {\n                    color: var(--red-500);\n                }\n            }\n\n            &:not(:disabled):not(:read-only) {\n                &:placeholder-shown {\n                    &:focus {\n                        border: var(--border-not-empty);\n                    }\n                }\n\n                &:hover {\n                    border: var(--border-not-empty);\n\n                    &.invalid,\n                    &.ng-touched.ng-invalid {\n                        border: var(--border-error);\n\n                        &::placeholder {\n                            color: var(--red-500);\n                        }\n                    }\n                }\n            }\n        }\n\n        &.textarea {\n            width: 100%;\n            height: auto;\n\n            textarea {\n                border: var(--border);\n                border-radius: 0.8rem;\n                outline: none;\n                padding: 1rem;\n                width: 100%;\n                min-width: 100%;\n                height: 100%;\n                min-height: 7.5rem;\n                max-height: 7.5rem;\n                overflow: auto;\n                resize: none;\n                background-color: transparent;\n                color: var(--input-color);\n                font-size: 1.8rem;\n                line-height: 1.2;\n\n                &:disabled {\n                    border: var(--border-disabled);\n                    cursor: not-allowed;\n                }\n\n                &:not(:disabled) {\n                    &:hover {\n                        cursor: pointer;\n                    }\n                }\n\n                &::placeholder {\n                    color: var(--gray-800);\n                }\n\n                /* If input is not empty */\n                &:not(:placeholder-shown) {\n                    /* You need to add a placeholder to your fields. For example: <input \"placeholder=\" \"/> */\n                    border: var(--border-not-empty);\n                }\n\n                /* If input is empty */\n                &:placeholder-shown {\n                    border: var(--border);\n                }\n\n                .ng-touched {\n                    .ng-invalid {\n                        border: var(--border-error);\n                    }\n                }\n\n                &.invalid {\n                    border: var(--border-error);\n\n                    &::placeholder {\n                        color: var(--red-500);\n                    }\n                }\n            }\n        }\n\n        .error,\n        .success,\n        .info {\n            overflow: hidden;\n            width: 100%;\n            font-size: 1.6rem;\n            margin-top: 1rem;\n        }\n\n        .hint {\n            margin-top: 5px;\n            color: var(--hint-text);\n            font-size: 1.4rem;\n        }\n\n        &.fixed {\n            padding-bottom: 2.6rem;\n\n            .error,\n            .success,\n            .info {\n                margin-top: 0;\n                position: absolute;\n                bottom: 0.2rem;\n\n                & > * {\n                    font-size: 1.6rem;\n                }\n            }\n\n            .hint {\n                margin-top: 0;\n                position: absolute;\n                bottom: 0.2rem;\n            }\n        }\n\n        .error {\n            color: var(--red-500);\n        }\n\n        .success {\n            color: var(--aqua-500);\n        }\n\n        &-dropdown {\n            position: relative;\n\n            .dropdown {\n                overflow-y: auto;\n                position: absolute;\n                top: calc(100% + 1rem);\n                left: 0;\n                max-width: 100%;\n                width: 100%;\n                max-height: 15rem;\n                border: var(--border);\n            }\n        }\n    }\n\n    .details {\n        .header {\n            padding: 1.2rem 2rem;\n            width: 100%;\n            max-width: 20rem;\n            background-color: var(--details-background);\n            border-radius: 0.8rem 0.8rem 0 0;\n\n            &.border-radius-all {\n                border-radius: 0.8rem;\n            }\n        }\n\n        .content {\n            display: flex;\n            flex-direction: column;\n            padding: 2rem;\n            background-color: var(--details-background);\n            border-radius: 0 0.8rem 0.8rem 0.8rem;\n        }\n    }\n}\n\n.checkbox {\n    display: flex;\n    align-items: center;\n    min-height: 2.4rem;\n    overflow: hidden;\n    position: relative;\n\n    span {\n        display: inline-block;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        cursor: pointer;\n        min-height: 2.4rem;\n        line-height: 2.4rem;\n        padding-left: 3.6rem;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        user-select: none;\n    }\n\n    input[readonly] {\n        & + span {\n            pointer-events: none;\n        }\n\n        & + span:before {\n            pointer-events: none;\n        }\n    }\n\n    input[type='checkbox'] {\n        position: absolute;\n        top: 0;\n        left: 0;\n        visibility: visible;\n        width: 2.4rem;\n        height: 2.4rem;\n        opacity: 0;\n        overflow: hidden;\n\n        & + span {\n            position: relative;\n        }\n\n        & + span:before,\n        & + span:after {\n            content: '';\n            position: absolute;\n            top: 50%;\n            transform: translateY(-50%);\n            transition: all 0.2s ease-in-out;\n        }\n\n        & + span:before {\n            left: 0;\n            width: 2.4rem;\n            height: 2.4rem;\n            border: var(--checkbox-border);\n            border-radius: 0.4rem;\n        }\n\n        &:not(:disabled) + span:hover:before {\n            border: var(--checkbox-hover-border);\n        }\n\n        &:focus + span:before {\n            border: var(--checkbox-active-border);\n        }\n\n        &:checked {\n            & + span:before,\n            & + span:hover:before {\n                border: var(--checkbox-active-border);\n            }\n\n            & + span:after {\n                left: 0.4rem;\n                width: 1.6rem;\n                height: 1.6rem;\n                border-radius: 0.2rem;\n                background: var(--checkbox-checked-background);\n            }\n        }\n\n        &:disabled {\n            & + span {\n                cursor: not-allowed;\n            }\n\n            & + span:before {\n                cursor: not-allowed;\n            }\n        }\n    }\n}\n\n.switch {\n    display: flex;\n    align-items: center;\n    border-radius: 1.1rem;\n    cursor: pointer;\n    padding: 0.2rem;\n    width: 3.6rem;\n    height: 2.2rem;\n    transition: all 0.2s ease-in-out;\n    outline: none;\n\n    &.disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n    }\n\n    &.on {\n        justify-content: flex-end;\n        background-color: var(--switch-on-background);\n    }\n\n    &.off {\n        justify-content: flex-start;\n        background-color: var(--switch-off-background);\n    }\n\n    .circle {\n        border-radius: 50%;\n        width: 1.8rem;\n        height: 1.8rem;\n        background-color: var(--switch-circle-background);\n        box-shadow: var(--shadow-gray);\n    }\n}\n\n.amount {\n    .form__field--input {\n        padding-right: 10.8rem;\n    }\n\n    .ticker {\n        position: absolute;\n        top: 3.1rem;\n        right: 4.8rem;\n        width: 5rem;\n        height: 3.6rem;\n        display: flex;\n        justify-content: flex-end;\n        align-items: center;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        color: var(--amount-ticker-text);\n    }\n\n    .btn-reverse {\n        width: 3.8rem;\n        height: 3.6rem;\n        position: absolute;\n        border-radius: 0 0.4rem 0.4rem 0;\n        right: 0.18rem;\n        top: 3.1rem;\n\n        display: flex;\n        justify-items: center;\n        align-items: center;\n        background: var(--amount-btn-revers-background);\n    }\n}\n\n.XSmall,\n.Small {\n    .form {\n        &__field {\n            &--row {\n                flex-direction: column;\n\n                > div,\n                > fieldset {\n                    max-width: 100%;\n\n                    &:first-child {\n                        margin-right: 0;\n                    }\n\n                    &:last-child {\n                        margin-left: 0;\n                    }\n                }\n            }\n        }\n    }\n}\n", "body {\n    .mat-icon {\n        width: 1.8rem;\n        min-width: 1.8rem;\n        height: 1.8rem;\n        font-size: 1.8rem;\n\n        &.small {\n            width: 1.6rem;\n            min-width: 1.6rem;\n            height: 1.6rem;\n            font-size: 1.6rem;\n        }\n    }\n}\n\n// Deprecated, use register custom-icons for mat-icon\ni {\n    display: inline-block;\n\n    svg {\n        width: 100%;\n        height: 100%;\n    }\n}\n\n.icon {\n    display: inline-flex;\n    min-width: 1.8rem;\n    min-height: 1.8rem;\n    transition: all 0.25s ease;\n\n    &.small {\n        min-width: 1.4rem;\n        min-height: 1.4rem;\n    }\n\n    // BLUE\n    &.question-circle {\n        background: center / contain no-repeat url(~src/assets/icons/blue/question-circle.svg);\n    }\n\n    &.info-circle {\n        background: center / contain no-repeat url(~src/assets/icons/blue/info-circle.svg);\n    }\n\n    &.purchase-arrow-down {\n        background: center / contain no-repeat url(~src/assets/icons/blue/purchase-arrow-down.svg);\n    }\n\n    &.purchase-arrow-up {\n        background: center / contain no-repeat url(~src/assets/icons/blue/purchase-arrow-up.svg);\n    }\n\n    // WHITE\n    &.custom-asset {\n        background: center / contain no-repeat url(~src/assets/icons/white/custom-asset_icon.svg);\n    }\n\n    &.show-balance {\n        background: center / contain no-repeat url(~src/assets/icons/white/show-balance_icon.svg);\n    }\n\n    &.hide-balance {\n        background: center / contain no-repeat url(~src/assets/icons/white/hide-balance_ico.svg);\n    }\n\n    &.emit {\n        background: center / contain no-repeat url(~src/assets/icons/white/emit_icon.svg);\n    }\n\n    &.arrow-down-square {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-down-square.svg);\n    }\n\n    &.swap {\n        background: center / contain no-repeat url(~src/assets/icons/white/swap_icon.svg);\n    }\n\n    &.add {\n        background: center / contain no-repeat url(~src/assets/icons/white/add.svg);\n    }\n\n    &.regenerate {\n        background: center / contain no-repeat url(~src/assets/icons/white/regenerate.svg);\n    }\n\n    &.balance-icon {\n        background: center / contain no-repeat url(~src/assets/icons/white/balance_icon.svg);\n    }\n\n    &.info-icon {\n        background: center / contain no-repeat url(~src/assets/icons/white/info_icon.svg);\n    }\n\n    &.arrow-left-stroke {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-left-stroke.svg);\n    }\n\n    &.arrow-left-slider {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-left-slider.svg);\n    }\n\n    &.arrow-right-stroke {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-right-stroke.svg);\n    }\n\n    &.arrow-right-slider {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-right-slider.svg);\n    }\n\n    &.arrow-up-square {\n        background: center / contain no-repeat url(~src/assets/icons/white/arrow-up-square.svg);\n    }\n\n    &.close {\n        background: center / contain no-repeat url(~src/assets/icons/white/close.svg);\n    }\n\n    &.close-square {\n        background: center / contain no-repeat url(~src/assets/icons/white/close-square.svg);\n    }\n\n    &.check-shield {\n        background: center / contain no-repeat url(~src/assets/icons/white/check-shield.svg);\n    }\n\n    &.contacts {\n        background: center / contain no-repeat url(~src/assets/icons/white/contacts.svg);\n    }\n\n    &.copy {\n        background: center / contain no-repeat url(~src/assets/icons/white/copy.svg);\n    }\n\n    &.check {\n        background: center / contain no-repeat url(~src/assets/icons/white/check.svg);\n    }\n\n    &.check-circle {\n        background: center / contain no-repeat url(~src/assets/icons/white/check-circle.svg);\n    }\n\n    &.delete {\n        background: center / contain no-repeat url(~src/assets/icons/white/delete.svg);\n    }\n\n    &.options-vertical {\n        background: center / contain no-repeat url(~src/assets/icons/white/options-vertical.svg);\n    }\n\n    &.temp {\n        background: center / contain no-repeat url(~src/assets/icons/white/temp.svg);\n    }\n\n    &.document {\n        background: center / contain no-repeat url(~src/assets/icons/white/document.svg);\n    }\n\n    &.dots {\n        background: center / contain no-repeat url(~src/assets/icons/white/dots.svg);\n    }\n\n    &.dropdown-arrow-down {\n        background: center / contain no-repeat url(~src/assets/icons/white/dropdown-arrow-down.svg);\n    }\n\n    &.dropdown-arrow-left {\n        background: center / contain no-repeat url(~src/assets/icons/white/dropdown-arrow-left.svg);\n    }\n\n    &.dropdown-arrow-right {\n        background: center / contain no-repeat url(~src/assets/icons/white/dropdown-arrow-right.svg);\n    }\n\n    &.dropdown-arrow-up {\n        background: center / contain no-repeat url(~src/assets/icons/white/dropdown-arrow-up.svg);\n    }\n\n    &.edit-square {\n        background: center / contain no-repeat url(~src/assets/icons/white/edit-square.svg);\n    }\n\n    &.export {\n        background: center / contain no-repeat url(~src/assets/icons/white/export.svg);\n    }\n\n    &.logout {\n        background: center / contain no-repeat url(~src/assets/icons/white/logout.svg);\n    }\n\n    &.plus {\n        background: center / contain no-repeat url(~src/assets/icons/white/plus.svg);\n    }\n\n    &.settings {\n        background: center / contain no-repeat url(~src/assets/icons/white/settings.svg);\n    }\n\n    &.staking {\n        background: center / contain no-repeat url(~src/assets/icons/white/staking.svg);\n    }\n\n    &.time-circle {\n        background: center / contain no-repeat url(~src/assets/icons/white/time-circle.svg);\n    }\n\n    &.wallet-options {\n        background: center / contain no-repeat url(~src/assets/icons/white/wallet-options.svg);\n    }\n\n    &.update {\n        background: center / contain no-repeat url(~src/assets/icons/white/update.svg);\n    }\n\n    &.update-with-dash {\n        background: center / contain no-repeat url(~src/assets/icons/white/update-with-dash_icon.svg);\n    }\n\n    &.lock-transaction {\n        background: center / contain no-repeat url(~src/assets/icons/white/lock-transaction.svg);\n    }\n\n    &.unlock-transaction {\n        background: center / contain no-repeat url(~src/assets/icons/white/unlock-transaction.svg);\n    }\n\n    &.modal-info {\n        background: center / contain no-repeat url(~src/assets/icons/white/modal-info.svg);\n    }\n\n    // orange\n    &.time-orange {\n        background: center / contain no-repeat url(~src/assets/icons/orange/time.svg);\n    }\n\n    // red\n    &.unsecured {\n        background: center / contain no-repeat url(~src/assets/icons/red/unsecured.svg);\n    }\n\n    &.new {\n        background: center / contain no-repeat url(~src/assets/icons/red/new.svg);\n    }\n\n    &.alert {\n        background: center / contain no-repeat url(~src/assets/icons/red/alert.svg);\n    }\n\n    &.error {\n        background: center / contain no-repeat url(~src/assets/icons/red/modal-alert.svg);\n    }\n\n    // aqua\n    &.secured {\n        background: center / contain no-repeat url(~src/assets/icons/aqua/secured.svg);\n    }\n\n    &.success {\n        background: center / contain no-repeat url(~src/assets/icons/aqua/modal-success.svg);\n    }\n\n    // gray\n    &.fire {\n        background: center / contain no-repeat url(~src/assets/icons/gray/fire_ico.svg);\n    }\n\n    &.block {\n        background: center / contain no-repeat url(~src/assets/icons/gray/block_ico.svg);\n    }\n}\n", ".loader {\n    border: 2rem solid var(--azure-500);\n    border-top: 2rem solid transparent;\n    border-radius: 50%;\n    min-width: 13rem;\n    min-height: 13rem;\n    animation: spin 2s linear infinite;\n}\n\n@keyframes spin {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n", ".migrate-alert {\n    .btn-migrate {\n        background-color: #1f8feb33;\n        color: #1f8feb;\n        font-size: 1.8rem;\n        line-height: 1.2;\n        padding: 0.8rem 2rem;\n        border-radius: 0.8rem;\n        cursor: pointer;\n        transition: background-color 0.2s ease;\n\n        &:hover {\n            background-color: #1f8feb50;\n        }\n    }\n\n    .migration-details {\n        font-size: 1.6rem;\n    }\n}\n", ".modal-overlay {\n    position: fixed;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: var(--black-300);\n    padding: 2rem;\n    z-index: 100;\n}\n\n.dialog-wrapper {\n    padding: 2rem;\n    border-radius: 0.8rem;\n    max-height: 90vh;\n\n    background: var(--dialog-background);\n}\n\n.modal-overlay-transparent {\n    background: transparent;\n}\n\n.modal {\n    position: relative;\n    overflow: hidden;\n\n    .message-container {\n        overflow: hidden;\n        text-overflow: ellipsis;\n\n        .title,\n        .message {\n            max-width: 100%;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            word-wrap: break-word;\n        }\n    }\n\n    button.close {\n        position: absolute;\n        top: 0.8rem;\n        right: 0.8rem;\n    }\n}\n", ".zano-mat-dialog {\n    .mat-dialog-title {\n        font-size: 1.8rem;\n        line-height: 1.2;\n        font-weight: 400;\n    }\n\n    .mat-dialog-container {\n        padding: 2rem;\n        border-radius: 0.8rem;\n\n        color: var(--main-text);\n        background: var(--dialog-background);\n    }\n\n    .mat-dialog-content {\n        margin: 0 -2rem;\n        padding: 0 2rem;\n    }\n\n    .mat-dialog-actions {\n        padding: 2rem 0;\n        margin-bottom: -2rem;\n    }\n}\n", "@mixin rtl {\n    @at-root [dir='rtl'] #{&} {\n        @content;\n    }\n}\n\n$ng-select-highlight: var(--ng-select-highlight) !default;\n$ng-select-primary-text: var(--main-text) !important;\n$ng-select-disabled-text: var(--main-text) !default;\n$ng-select-border: var(--ng-select-border) !default;\n$ng-select-border-radius: 0.8rem !default;\n$ng-select-bg: var(--ng-select-bg) !default;\n$ng-select-selected: $ng-select-highlight !default;\n$ng-select-selected-text: $ng-select-primary-text !default;\n\n$ng-select-marked: $ng-select-highlight !default;\n$ng-select-marked-text: $ng-select-primary-text !default;\n\n$ng-select-box-shadow: none;\n$ng-select-placeholder: var(--gray-700) !default;\n$ng-select-height: 4rem !default;\n$ng-select-value-padding-left: 1rem !default;\n$ng-select-value-font-size: 1.8rem !default;\n$ng-select-value-text: $ng-select-primary-text !default;\n\n$ng-select-dropdown-bg: $ng-select-bg !default;\n$ng-select-dropdown-border: $ng-select-border !default;\n$ng-select-dropdown-optgroup-text: var(--main-text) !default;\n$ng-select-dropdown-optgroup-marked: $ng-select-dropdown-optgroup-text !default;\n$ng-select-dropdown-option-bg: $ng-select-dropdown-bg !default;\n$ng-select-dropdown-option-text: var(--main-text) !default;\n$ng-select-dropdown-option-disabled: rgba(255, 255, 255, 0.5) !important !default;\n\n$ng-select-input-text: var(--main-text) !default;\n\n$circle-border: var(--ng-select-circle-border) !default;\n$circle-background: var(--ng-select-circle-background) !default;\n\n.ng-select {\n    width: 100%;\n\n    &.ng-select-opened {\n        > .ng-select-container {\n            background: $ng-select-bg;\n            border-color: $ng-select-border;\n\n            &:hover {\n                box-shadow: none;\n            }\n\n            .ng-arrow {\n                display: flex !important;\n                align-items: center;\n                justify-content: center;\n                min-width: 0.8rem !important;\n                min-height: 0.8rem !important;\n                border-top: 1px solid;\n                border-right: 1px solid;\n                transform: rotate(-45deg);\n            }\n        }\n\n        &.ng-select-top {\n            > .ng-select-container {\n                border-top-right-radius: 0;\n                border-top-left-radius: 0;\n            }\n        }\n\n        &.ng-select-right {\n            > .ng-select-container {\n                border-top-right-radius: 0;\n                border-bottom-right-radius: 0;\n            }\n        }\n\n        &.ng-select-bottom {\n            > .ng-select-container {\n                border-bottom-right-radius: 0;\n                border-bottom-left-radius: 0;\n            }\n        }\n\n        &.ng-select-left {\n            > .ng-select-container {\n                border-top-left-radius: 0;\n                border-bottom-left-radius: 0;\n            }\n        }\n    }\n\n    &.ng-select-focused {\n        &:not(.ng-select-opened) > .ng-select-container {\n            border-color: $ng-select-border;\n            box-shadow: $ng-select-box-shadow;\n        }\n    }\n\n    &.ng-select-disabled {\n        cursor: not-allowed !important;\n\n        > .ng-select-container {\n            cursor: not-allowed !important;\n\n            &:hover {\n                box-shadow: none;\n                border: 1px solid $ng-select-bg;\n            }\n        }\n    }\n\n    .ng-has-value .ng-placeholder {\n        display: none;\n    }\n\n    .ng-select-container {\n        align-items: center;\n        color: $ng-select-primary-text;\n        background-color: $ng-select-bg;\n        border-radius: $ng-select-border-radius;\n        border: 1px solid $ng-select-bg;\n        min-height: $ng-select-height;\n        transition: border 0.2s ease-in-out;\n        cursor: pointer !important;\n\n        // &.ng-has-value show border if select has value\n        &:hover {\n            box-shadow: none;\n            border: 1px solid $ng-select-border;\n        }\n\n        .ng-value-container {\n            align-items: center;\n            padding-left: $ng-select-value-padding-left;\n            @include rtl {\n                padding-right: $ng-select-value-padding-left;\n                padding-left: 0;\n            }\n\n            .ng-placeholder {\n                color: $ng-select-placeholder;\n            }\n        }\n    }\n\n    &.ng-select-single {\n        .ng-select-container {\n            height: $ng-select-height;\n\n            .ng-value-container {\n                .ng-input {\n                    top: 0.8rem;\n                    left: 0;\n                    padding-left: $ng-select-value-padding-left;\n                    padding-right: 5rem;\n\n                    > input {\n                        color: var(--white-500);\n                        font-size: 1.8rem;\n                    }\n\n                    @include rtl {\n                        padding-right: $ng-select-value-padding-left;\n                        padding-left: 5rem;\n                    }\n                }\n            }\n        }\n    }\n\n    &.ng-select-multiple {\n        &.ng-select-disabled {\n            cursor: not-allowed;\n\n            > .ng-select-container .ng-value-container .ng-value {\n                background-color: $ng-select-disabled-text;\n                border: 1px solid $ng-select-border;\n\n                .ng-value-label {\n                    padding: 0 0.5rem;\n                }\n            }\n        }\n\n        .ng-select-container {\n            .ng-value-container {\n                padding-top: 0.5rem;\n                padding-left: 0.7rem;\n                @include rtl {\n                    padding-right: 0.7rem;\n                    padding-left: 0;\n                }\n\n                .ng-value {\n                    font-size: $ng-select-value-font-size;\n                    margin-bottom: 0.5rem;\n                    color: $ng-select-value-text;\n                    background-color: $ng-select-selected;\n                    border-radius: 0.2rem;\n                    margin-right: 0.5rem;\n                    @include rtl {\n                        margin-right: 0;\n                        margin-left: 0.5rem;\n                    }\n\n                    &.ng-value-disabled {\n                        background-color: $ng-select-disabled-text;\n\n                        .ng-value-label {\n                            padding-left: 0.5rem;\n                            @include rtl {\n                                padding-left: 0;\n                                padding-right: 0.5rem;\n                            }\n                        }\n                    }\n\n                    .ng-value-label {\n                        display: inline-block;\n                        padding: 0.1rem 0.5rem;\n                    }\n\n                    .ng-value-icon {\n                        display: inline-block;\n                        padding: 0.1rem 0.5rem;\n\n                        &:hover {\n                            background-color: $ng-select-selected, 5;\n                        }\n\n                        &.left {\n                            border-right: 0.15rem solid $ng-select-selected;\n                            @include rtl {\n                                border-left: 0.15rem solid $ng-select-selected;\n                                border-right: none;\n                            }\n                        }\n\n                        &.right {\n                            border-left: 0.15rem solid $ng-select-selected;\n                            @include rtl {\n                                border-left: 0;\n                                border-right: 0.15rem solid $ng-select-selected;\n                            }\n                        }\n                    }\n                }\n\n                .ng-input {\n                    padding: 0 0 0.3rem 0.3rem;\n                    @include rtl {\n                        padding: 0 0.3rem 0.3rem 0;\n                    }\n\n                    > input {\n                        color: $ng-select-input-text;\n                        font-size: 1.8rem;\n                    }\n                }\n\n                .ng-placeholder {\n                    top: 0.8rem;\n                    padding-bottom: 0.5rem;\n                    padding-left: 0.3rem;\n                    @include rtl {\n                        padding-right: 0.3rem;\n                        padding-left: 0;\n                    }\n                }\n            }\n        }\n    }\n\n    .ng-clear-wrapper {\n        color: $ng-select-border;\n\n        &:hover .ng-clear {\n            color: #d0021b;\n        }\n    }\n\n    .ng-spinner-zone {\n        padding: 0.5rem 0.5rem 0 0;\n\n        @include rtl {\n            padding: 0.5rem 0 0 0.5rem;\n        }\n    }\n\n    .ng-arrow-wrapper {\n        width: 2.5rem;\n        padding-right: 0.5rem;\n        @include rtl {\n            padding-left: 0.5rem;\n            padding-right: 0;\n        }\n\n        &:hover {\n            .ng-arrow {\n                border-top-color: $ng-select-border;\n            }\n        }\n\n        .ng-arrow {\n            display: flex !important;\n            align-items: center;\n            justify-content: center;\n            min-width: 0.8rem !important;\n            min-height: 0.8rem !important;\n            border-top: 1px solid;\n            border-right: 1px solid;\n            transform: rotate(135deg);\n        }\n    }\n\n    &.invalid,\n    &.ng-touched.ng-invalid {\n        > .ng-select-container,\n        .ng-dropdown-panel {\n            border: var(--border-error);\n        }\n    }\n}\n\n.ng-dropdown-panel {\n    background-color: $ng-select-dropdown-bg;\n    border: 2px solid $ng-select-dropdown-border;\n    box-shadow: none;\n    left: 0;\n\n    &.ng-select-top {\n        bottom: 100%;\n        border-top-right-radius: $ng-select-border-radius;\n        border-top-left-radius: $ng-select-border-radius;\n        border-bottom-color: $ng-select-border;\n        margin-bottom: -0.1rem;\n\n        .ng-dropdown-panel-items {\n            .ng-option {\n                &:first-child {\n                    border-top-right-radius: $ng-select-border-radius;\n                    border-top-left-radius: $ng-select-border-radius;\n                }\n            }\n        }\n    }\n\n    &.ng-select-right {\n        left: 100%;\n        top: 0;\n        border-top-right-radius: $ng-select-border-radius;\n        border-bottom-right-radius: $ng-select-border-radius;\n        border-bottom-left-radius: $ng-select-border-radius;\n        border-bottom-color: $ng-select-border;\n        margin-bottom: -0.1rem;\n\n        .ng-dropdown-panel-items {\n            .ng-option {\n                &:first-child {\n                    border-top-right-radius: $ng-select-border-radius;\n                }\n            }\n        }\n    }\n\n    &.ng-select-bottom {\n        top: 100%;\n        border-bottom-right-radius: $ng-select-border-radius;\n        border-bottom-left-radius: $ng-select-border-radius;\n        border-top-color: $ng-select-border;\n        margin-top: -0.1rem;\n\n        .ng-dropdown-panel-items {\n            .ng-option {\n                &:last-child {\n                    border-bottom-right-radius: $ng-select-border-radius;\n                    border-bottom-left-radius: $ng-select-border-radius;\n                }\n            }\n        }\n    }\n\n    &.ng-select-left {\n        left: -100%;\n        top: 0;\n        border-top-left-radius: $ng-select-border-radius;\n        border-bottom-right-radius: $ng-select-border-radius;\n        border-bottom-left-radius: $ng-select-border-radius;\n        border-bottom-color: $ng-select-border;\n        margin-bottom: -0.1rem;\n\n        .ng-dropdown-panel-items {\n            .ng-option {\n                &:first-child {\n                    border-top-left-radius: $ng-select-border-radius;\n                }\n            }\n        }\n    }\n\n    .ng-dropdown-header {\n        border-bottom: 0.15rem solid $ng-select-border;\n        padding: 0.5rem 0.7rem;\n    }\n\n    .ng-dropdown-footer {\n        border-top: 0.15rem solid $ng-select-border;\n        padding: 0.5rem 0.7rem;\n    }\n\n    .ng-dropdown-panel-items {\n        .ng-optgroup {\n            user-select: none;\n            padding: 0.8rem 1rem;\n            font-weight: 500;\n            color: $ng-select-dropdown-optgroup-text;\n            cursor: pointer;\n\n            &.ng-option-disabled {\n                cursor: not-allowed;\n            }\n\n            &.ng-option-marked {\n                background-color: $ng-select-marked;\n            }\n\n            &.ng-option-selected,\n            &.ng-option-selected.ng-option-marked {\n                color: $ng-select-dropdown-optgroup-marked;\n                background-color: $ng-select-selected;\n                font-weight: 600;\n            }\n        }\n\n        .ng-option {\n            background-color: $ng-select-dropdown-option-bg;\n            color: $ng-select-dropdown-option-text;\n            padding: 0.8rem 1rem;\n\n            &.ng-option-selected,\n            &.ng-option-selected.ng-option-marked {\n                color: $ng-select-selected-text;\n                background-color: $ng-select-selected;\n\n                .ng-option-label {\n                    font-weight: 600;\n                }\n            }\n\n            &.ng-option-marked {\n                background-color: $ng-select-marked;\n                color: $ng-select-marked-text;\n            }\n\n            &.ng-option-disabled {\n                color: $ng-select-dropdown-option-disabled;\n            }\n\n            &.ng-option-child {\n                padding-left: 2.2rem;\n                @include rtl {\n                    padding-right: 2.2rem;\n                    padding-left: 0;\n                }\n            }\n\n            .ng-tag-label {\n                font-size: 80%;\n                font-weight: 400;\n                padding-right: 0.5rem;\n                @include rtl {\n                    padding-left: 0.5rem;\n                    padding-right: 0;\n                }\n            }\n        }\n    }\n\n    @include rtl {\n        direction: rtl;\n        text-align: right;\n    }\n}\n\n.ng-select {\n    &.with-circle {\n        .ng-dropdown-panel {\n            .ng-option {\n                position: relative;\n                padding: 0.8rem 3rem 0.8rem 0.8rem;\n\n                &:after {\n                    position: absolute;\n                    top: 50%;\n                    right: 1rem;\n                    transform: translateY(-50%);\n                    display: block;\n                    content: '';\n                    width: 1.8rem;\n                    height: 1.8rem;\n                    border: $circle-border;\n                    border-radius: 50%;\n                }\n\n                &.ng-option-selected {\n                    &:before {\n                        position: absolute;\n                        top: 50%;\n                        right: 1.4rem;\n                        transform: translateY(-50%);\n                        display: block;\n                        content: '';\n                        width: 1rem;\n                        height: 1rem;\n                        background: $circle-background;\n                        border-radius: 50%;\n                    }\n                }\n            }\n        }\n    }\n}\n", ".ngx-pagination {\n    a {\n        min-width: 29px;\n        cursor: pointer;\n        color: var(--main-text) !important;\n        &:hover {\n            background: transparent !important;\n        }\n    }\n\n    .current {\n        background: transparent !important;\n        color: var(--azure-500) !important;\n    }\n\n    .pagination-next,\n    .pagination-previous {\n        background-color: var(--btn-icon-background);\n        transition: background-color 0.2s ease-in-out;\n        border-radius: 999px;\n\n        &:hover {\n            background-color: var(--btn-icon-hover-background);\n        }\n\n        &.disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n        }\n    }\n}\n\n.custom-pagination {\n    display: flex;\n    align-items: center;\n    min-height: 2.8rem;\n}\n", ".wrapper-tab-preloader {\n    display: flex;\n    z-index: 999;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: var(--tab-preloader-background);\n}\n\n.preloader {\n    align-self: center;\n    color: var(--tab-preloader-text);\n    font-size: 2rem;\n    margin: 0 auto;\n    text-align: center;\n    width: 50%;\n\n    .loading-bar {\n        display: block;\n        animation: move 5s linear infinite;\n        background-color: var(--azure-500);\n        background-image: -webkit-gradient(\n                linear,\n                0 0,\n                100% 100%,\n                color-stop(0.125, rgba(0, 0, 0, 0.15)),\n                color-stop(0.125, transparent),\n                color-stop(0.25, transparent),\n                color-stop(0.25, rgba(0, 0, 0, 0.1)),\n                color-stop(0.375, rgba(0, 0, 0, 0.1)),\n                color-stop(0.375, transparent),\n                color-stop(0.5, transparent),\n                color-stop(0.5, rgba(0, 0, 0, 0.15)),\n                color-stop(0.625, rgba(0, 0, 0, 0.15)),\n                color-stop(0.625, transparent),\n                color-stop(0.75, transparent),\n                color-stop(0.75, rgba(0, 0, 0, 0.1)),\n                color-stop(0.875, rgba(0, 0, 0, 0.1)),\n                color-stop(0.875, transparent),\n                to(transparent)\n            ),\n            -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.125, rgba(0, 0, 0, 0.3)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.25)), color-stop(0.375, rgba(0, 0, 0, 0.25)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.3)), color-stop(0.625, rgba(0, 0, 0, 0.3)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.25)), color-stop(0.875, rgba(0, 0, 0, 0.25)), color-stop(0.875, transparent), to(transparent));\n        background-size: 10rem 10rem;\n        width: 100%;\n        height: 1rem;\n    }\n}\n\n@keyframes move {\n    0% {\n        background-position: 100% -10rem;\n    }\n    100% {\n        background-position: 100% 10rem;\n    }\n}\n", "app-progress-container {\n    .progress-bar-container {\n        color: var(--white-500);\n\n        .progress-bar {\n            background-color: var(--blue-300);\n\n            .progress-bar-full {\n                background-color: var(--azure-500);\n            }\n        }\n    }\n}\n", ".seed-phrase {\n    .content {\n        .item {\n            .number {\n                width: 1.8rem;\n                height: 1.8rem;\n                border-radius: 50%;\n                font-size: 1rem;\n            }\n\n            .word {\n                white-space: nowrap;\n            }\n        }\n    }\n}\n\n.light {\n    .seed-phrase {\n        .content {\n            .item {\n                border: var(--border);\n                background-color: #1f8feb1a;\n                color: #1f8feb;\n\n                .number {\n                    background-color: #1f8feb26;\n                    color: #1f8feb;\n                }\n            }\n        }\n    }\n}\n\n.dark {\n    .seed-phrase {\n        .content {\n            .item {\n                border: var(--border);\n                background-color: var(--gray-900);\n\n                .number {\n                    background-color: var(--gray-600);\n                }\n            }\n        }\n    }\n}\n", "app-synchronization-status {\n    width: 100%;\n}\n\n.synchronization-status {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    color: var(--synchronization-status-color);\n    width: 100%;\n\n    .status-container {\n        position: relative;\n        width: 100%;\n        margin-bottom: 0.5rem;\n\n        .offline,\n        .online {\n            display: flex;\n            width: 100%;\n\n            span {\n                position: relative;\n                padding-left: 2.2rem;\n\n                &:before {\n                    content: '';\n                    position: absolute;\n                    top: 50%;\n                    left: 0;\n                    transform: translateY(-50%);\n                    border-radius: 50%;\n                    width: 1rem;\n                    height: 1rem;\n                }\n            }\n        }\n\n        .offline > span:before {\n            background-color: var(--red-500);\n        }\n\n        .online > span:before {\n            background-color: var(--aqua-500);\n        }\n\n        .syncing,\n        .loading {\n            font-size: 1.4rem;\n            line-height: 1.2;\n        }\n\n        .progress-bar-container {\n            width: 100%;\n            height: 0.6rem;\n\n            .syncing {\n                display: flex;\n                justify-content: flex-start;\n                align-items: center;\n                margin-top: 0.4rem;\n\n                .progress-bar {\n                    border-radius: 0.2rem;\n                    height: 0.6rem;\n                    width: 100%;\n                    overflow: hidden;\n                    background-color: var(--synchronization-progress-bar-container-background);\n\n                    .fill {\n                        border-radius: 0.2rem;\n                        height: 100%;\n                        background-color: var(--aqua-500);\n                    }\n                }\n\n                .progress-percent {\n                    color: var(--aqua-500);\n                    font-size: 1.4rem;\n                    line-height: 1.2;\n                    padding-left: 1rem;\n                }\n            }\n\n            .loading {\n                background-color: var(--aqua-500);\n                animation: move 5s linear infinite;\n                background-image: -webkit-gradient(\n                        linear,\n                        0 0,\n                        100% 100%,\n                        color-stop(0.125, rgba(0, 0, 0, 0.15)),\n                        color-stop(0.125, transparent),\n                        color-stop(0.25, transparent),\n                        color-stop(0.25, rgba(0, 0, 0, 0.1)),\n                        color-stop(0.375, rgba(0, 0, 0, 0.1)),\n                        color-stop(0.375, transparent),\n                        color-stop(0.5, transparent),\n                        color-stop(0.5, rgba(0, 0, 0, 0.15)),\n                        color-stop(0.625, rgba(0, 0, 0, 0.15)),\n                        color-stop(0.625, transparent),\n                        color-stop(0.75, transparent),\n                        color-stop(0.75, rgba(0, 0, 0, 0.1)),\n                        color-stop(0.875, rgba(0, 0, 0, 0.1)),\n                        color-stop(0.875, transparent),\n                        to(transparent)\n                    ),\n                    -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.125, rgba(0, 0, 0, 0.3)), color-stop(0.125, transparent), color-stop(0.25, transparent), color-stop(0.25, rgba(0, 0, 0, 0.25)), color-stop(0.375, rgba(0, 0, 0, 0.25)), color-stop(0.375, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(0, 0, 0, 0.3)), color-stop(0.625, rgba(0, 0, 0, 0.3)), color-stop(0.625, transparent), color-stop(0.75, transparent), color-stop(0.75, rgba(0, 0, 0, 0.25)), color-stop(0.875, rgba(0, 0, 0, 0.25)), color-stop(0.875, transparent), to(transparent));\n                background-size: 7rem 7rem;\n                height: 100%;\n            }\n        }\n\n        .blocks {\n            margin-top: 5px;\n            font-size: 1.4rem;\n            line-height: 1.4;\n            font-weight: 400;\n            word-break: break-all;\n\n            i {\n                min-width: 1rem;\n                min-height: 1rem;\n            }\n\n            span {\n                font-size: 1.4rem;\n                line-height: 1.4;\n                font-weight: 400;\n                color: #a8abb5;\n            }\n        }\n    }\n\n    .update-container {\n        display: flex;\n        align-items: center;\n        text-align: right;\n\n        .update-text {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-items: center;\n            font-size: 1.4rem;\n            line-height: 1.2;\n            text-align: left;\n\n            &.time {\n                font-size: 1.1rem;\n            }\n        }\n\n        .icon {\n            flex: 1 0 auto;\n            margin: 0.3rem 0 0 0.6rem;\n            width: 1.2rem;\n            height: 1.2rem;\n        }\n\n        .standard {\n            color: var(--aqua-500);\n        }\n\n        .important {\n            color: var(--orange-500);\n        }\n\n        .critical {\n            color: var(--red-500);\n        }\n\n        .time-orange {\n            color: var(--orange-500);\n        }\n\n        .icon {\n            &.standard {\n                .st0 {\n                    fill: var(--aqua-500);\n                }\n            }\n\n            &.important {\n                .st0 {\n                    fill: var(--orange-500);\n                }\n            }\n\n            &.critical {\n                .st0 {\n                    fill: var(--red-500);\n                }\n            }\n        }\n    }\n}\n\n@keyframes move {\n    0% {\n        background-position: 100% -7rem;\n    }\n    100% {\n        background-position: 100% 7rem;\n    }\n}\n", "table.zano-table {\n    width: 100%;\n    table-layout: fixed;\n\n    .row-divider {\n        height: 1rem;\n        -webkit-transition: 0.2s height linear, 0s font-size;\n        transition: 0.2s height linear, 0s font-size;\n        transition-delay: 0s, 0.2s;\n\n        &.hide {\n            height: 0;\n        }\n    }\n\n    & > thead {\n        text-align: left;\n        border-radius: 0.8rem;\n        overflow: auto;\n\n        & > tr {\n            & > th {\n                background-color: var(--tab-content-background);\n                z-index: 5;\n                max-width: 10rem;\n                overflow: hidden;\n                text-overflow: ellipsis;\n\n                .bg {\n                    background-color: var(--table-thead-bg);\n                }\n\n                .title {\n                    overflow: hidden;\n                    text-overflow: ellipsis;\n                    padding: 2rem;\n                    width: 100%;\n                    white-space: nowrap;\n                }\n\n                & > &:first-child {\n                    .title {\n                        border-radius: 0.8rem 0 0 0.8rem;\n                    }\n                }\n\n                & > &:last-child {\n                    .title {\n                        border-radius: 0 0.8rem 0.8rem 0;\n                    }\n                }\n            }\n\n            /** Sticky header */\n            & > th {\n                position: sticky;\n                top: 0;\n            }\n        }\n    }\n\n    & > tbody {\n        text-align: left;\n\n        & > tr {\n            background-color: var(--table-row-bg);\n            -webkit-transition: 0.5s height linear, 0s font-size;\n            transition: 0.5s height linear, 0s font-size;\n            transition-delay: 0s, 0.5s;\n            height: auto;\n\n            & > td {\n                padding: 2rem;\n                vertical-align: middle;\n                white-space: nowrap;\n                max-width: 30rem;\n                overflow: hidden;\n                text-overflow: ellipsis;\n\n                & > &:first-child {\n                    border-radius: 0.8rem 0 0 0.8rem;\n                }\n\n                & > &:last-child {\n                    border-radius: 0 0.8rem 0.8rem 0;\n                }\n            }\n\n            & > &:not(.details) {\n                cursor: pointer;\n            }\n        }\n    }\n}\n\n.table-info {\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    border: var(--table-info-border);\n    border-radius: 0.8rem;\n    overflow: hidden;\n\n    .separator {\n        border: none;\n        border-bottom: var(--table-info-border);\n    }\n\n    .row {\n        display: flex;\n        flex-wrap: nowrap;\n        width: 100%;\n        min-height: 6rem;\n\n        .label,\n        .text {\n            overflow: hidden;\n            padding: 2rem;\n        }\n\n        .label {\n            color: var(--azure-500);\n            background: var(--table-info-label-background);\n            overflow: hidden;\n            text-overflow: ellipsis;\n        }\n\n        .text {\n            width: 100%;\n            word-break: break-word;\n        }\n    }\n}\n\n// Table with rounded-corners\ntable.rounded-corners {\n    border-spacing: 0;\n    border-collapse: separate;\n    border-radius: 1rem;\n    border: var(--table-rounded-corners-border);\n}\n\ntable.rounded-corners th:not(:last-child),\ntable.rounded-corners td:not(:last-child) {\n    border-right: var(--table-rounded-corners-border);\n}\n\ntable.rounded-corners > tbody > tr:first-child > td:first-child {\n    border-top-left-radius: 0.8rem;\n}\ntable.rounded-corners > tbody > tr:first-child > td:last-child {\n    border-top-right-radius: 0.8rem;\n}\n\ntable.rounded-corners > tbody > tr:last-child > td:first-child {\n    border-bottom-left-radius: 0.8rem;\n}\n\ntable.rounded-corners > tbody > tr:last-child > td:last-child {\n    border-bottom-right-radius: 0.8rem;\n}\n\ntable.rounded-corners > thead > tr:not(:last-child) > th,\ntable.rounded-corners > thead > tr:not(:last-child) > td,\ntable.rounded-corners > tbody > tr:not(:last-child) > th,\ntable.rounded-corners > tbody > tr:not(:last-child) > td,\ntable.rounded-corners > tfoot > tr:not(:last-child) > th,\ntable.rounded-corners > tfoot > tr:not(:last-child) > td,\ntable.rounded-corners > tr:not(:last-child) > td,\ntable.rounded-corners > tr:not(:last-child) > th,\ntable.rounded-corners > thead:not(:last-child),\ntable.rounded-corners > tbody:not(:last-child),\ntable.rounded-corners > tfoot:not(:last-child) {\n    border-bottom: var(--table-rounded-corners-border);\n}\n", ".tabs {\n    display: flex;\n    flex-direction: column;\n    flex: 1 1 auto;\n    overflow: hidden;\n\n    .tabs-header {\n        display: flex;\n        justify-content: space-between;\n        min-height: 5.8rem;\n\n        .tab-header {\n            background-color: var(--tab-header-background);\n            border-radius: 0.8rem 0.8rem 0 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            cursor: pointer;\n            min-height: 5.8rem;\n            flex: 1 1 auto;\n            transition: background-color 0.25s ease-in-out;\n\n            i,\n            span {\n                opacity: 0.75;\n                transition: opacity 0.25s ease-in-out;\n            }\n\n            &.active,\n            &:hover:not(.active):not(.disabled) {\n                background-color: var(--tab-header-active-background);\n\n                i,\n                span {\n                    opacity: 1;\n                }\n            }\n\n            &.hide {\n                display: none;\n            }\n\n            .indicator {\n                margin-left: 0.5rem;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                border-radius: 2rem;\n                font-size: 1.2rem;\n                line-height: 1.4rem;\n                min-width: 2.4rem;\n                height: 1.6rem;\n            }\n\n            &:disabled {\n                cursor: not-allowed;\n            }\n\n            &:not(:last-child) {\n                margin-right: 0.5rem;\n            }\n        }\n    }\n\n    .tabs-content {\n        display: flex;\n        flex: auto;\n        overflow: hidden;\n        border-radius: 0 0 0.8rem 0.8rem;\n        background-color: var(--tab-content-background);\n        padding: 2rem;\n        position: relative;\n    }\n}\n", ".table-tooltip {\n    z-index: 9999;\n    padding: 1rem;\n    border-radius: 0.8rem;\n    background: var(--tooltip-background);\n\n    .tooltip-inner {\n        font-size: 1.4rem;\n        line-height: 1.2;\n        white-space: pre-wrap;\n    }\n\n    &.ng-tooltip-top {\n        margin-top: -1rem;\n\n        &:after {\n            content: '';\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n            position: absolute;\n            bottom: -0.5rem;\n            left: calc(50% - 0.5rem);\n        }\n    }\n\n    .ng-tooltip-bottom-left {\n        margin-top: 1rem;\n\n        &::before {\n            content: '';\n            position: absolute;\n            top: -0.5rem;\n            left: 3rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background-color: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-top-left {\n        margin-top: -1rem;\n\n        &:after {\n            content: '';\n            position: absolute;\n            bottom: -0.5rem;\n            left: 1.5rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-top-right {\n        margin-top: -1rem;\n\n        &:after {\n            content: '';\n            position: absolute;\n            bottom: -0.5rem;\n            right: 0.7rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-bottom {\n        margin-top: 1rem;\n\n        &:before {\n            content: '';\n            position: absolute;\n            top: -0.5rem;\n            left: calc(50% - 0.5rem);\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-bottom-left {\n        margin-top: 1rem;\n\n        &::before {\n            content: '';\n            position: absolute;\n            top: -0.5rem;\n            left: 3rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background-color: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-bottom-right {\n        position: relative;\n        margin-top: 1rem;\n\n        &:before {\n            content: '';\n            position: absolute;\n            top: -0.5rem;\n            right: 0.5rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-left {\n        margin-left: -1rem;\n\n        &:after {\n            content: '';\n            position: absolute;\n            top: calc(50% - 0.5rem);\n            right: -0.5rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n\n    &.ng-tooltip-right {\n        margin-left: 1rem;\n\n        &:before {\n            content: '';\n            position: absolute;\n            top: calc(50% - 0.5rem);\n            left: -0.5rem;\n            display: block;\n            width: 1rem;\n            height: 1rem;\n            background: var(--tooltip-background);\n            transform: rotate(45deg);\n        }\n    }\n}\n\n.table-tooltip-dimensions {\n    .tooltip-inner {\n        overflow: auto;\n        max-width: 20rem;\n        max-height: 10rem;\n    }\n}\n\n.tooltip {\n    z-index: 999;\n    padding: 1rem;\n    border-radius: 0.6rem;\n    background-color: var(--tooltip-background);\n    font-size: 1.2rem;\n}\n\n.balance-tooltip {\n    z-index: 999;\n    padding: 1rem;\n    border-radius: 1rem;\n    background-color: var(--tooltip-background);\n\n    .tooltip-inner {\n        display: flex;\n        flex-direction: column;\n        font-size: 1.3rem;\n\n        .available {\n            margin-bottom: 0.7rem;\n\n            b {\n                font-weight: 600;\n            }\n        }\n\n        .locked {\n            margin-bottom: 0.7rem;\n\n            b {\n                font-weight: 600;\n            }\n        }\n\n        .link {\n            cursor: pointer;\n            color: var(--azure-500);\n        }\n    }\n\n    .balance-scroll-list {\n        display: flex;\n        flex-direction: column;\n        max-height: 20rem;\n        overflow-y: auto;\n    }\n\n    &.ng-tooltip-top {\n        margin-top: -1rem;\n    }\n\n    &.ng-tooltip-bottom {\n        margin-top: 1rem;\n    }\n\n    &.ng-tooltip-left {\n        margin-left: -1rem;\n    }\n\n    &.ng-tooltip-right {\n        margin-left: 1rem;\n    }\n}\n\n.account-tooltip {\n    z-index: 9999;\n    background-color: var(--tooltip-background);\n    .tooltip-inner {\n        word-break: break-word;\n        max-width: 18rem;\n    }\n}\n\n.comment-tooltip {\n    z-index: 999;\n    background-color: var(--tooltip-background);\n    .tooltip-inner {\n        word-break: break-word;\n        max-width: 50rem;\n        max-height: 25rem;\n    }\n}\n\n.update-tooltip {\n    z-index: 999;\n    padding: 1rem;\n    background-color: var(--tooltip-background);\n\n    &.important {\n        background: var(--red-500);\n\n        &.ng-tooltip-left-bottom {\n            &:after {\n                border-color: transparent transparent var(--red-500) var(--red-500);\n            }\n        }\n\n        &.ng-tooltip-right-bottom {\n            &:before {\n                border-color: transparent var(--red-500) var(--red-500) transparent;\n            }\n        }\n    }\n\n    &.critical {\n        padding: 2.5rem;\n        background: var(--red-500);\n\n        .tooltip-inner {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n\n        &.ng-tooltip-left-bottom {\n            &:after {\n                border-color: transparent transparent var(--red-500) var(--red-500);\n            }\n        }\n\n        &.ng-tooltip-right-bottom {\n            &:before {\n                border-color: transparent var(--red-500) var(--red-500) transparent;\n            }\n        }\n    }\n\n    .tooltip-inner {\n        font-size: 1.3rem;\n        line-height: 1.2;\n        white-space: pre-wrap;\n\n        .standard-update {\n            font-size: 1.5rem;\n            line-height: 1.2;\n            color: var(--azure-500);\n        }\n\n        .important-update {\n            font-size: 1.5rem;\n            line-height: 1.2;\n            color: var(--orange-500);\n        }\n\n        .critical-update {\n            font-size: 1.5rem;\n            line-height: 1.2;\n            text-align: center;\n        }\n\n        .wrong-time {\n            font-size: 1.5rem;\n            line-height: 1.2;\n            color: var(--orange-500);\n        }\n\n        .icon {\n            margin: 1.5rem 0;\n            width: 5rem;\n            height: 5rem;\n        }\n    }\n\n    &.ng-tooltip-left-bottom {\n        margin-left: -1.5rem;\n\n        &:after {\n            content: '';\n            position: absolute;\n            bottom: 0.6rem;\n            right: -1rem;\n            border-width: 0.5rem;\n            border-style: solid;\n            border-color: transparent transparent var(--tooltip-background) var(--tooltip-background);\n        }\n    }\n\n    &.ng-tooltip-right-bottom {\n        margin-left: 1.5rem;\n\n        &:before {\n            content: '';\n            position: absolute;\n            bottom: 0.6rem;\n            left: -1rem;\n            border-width: 0.5rem;\n            border-style: solid;\n            border-color: transparent var(--tooltip-background) var(--tooltip-background) transparent;\n        }\n    }\n}\n\n.update-tooltip {\n    z-index: 999;\n    background-color: var(--tooltip-background);\n    .tooltip-inner {\n        .icon {\n            background: center / contain no-repeat url(~src/assets/icons/red/update-alert.svg);\n        }\n    }\n}\n", ".wallet {\n    border-radius: 0.8rem;\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    max-width: 19rem;\n    min-width: 19rem;\n    width: 100%;\n    padding: 1.2rem;\n    background-color: var(--wallet-background);\n    border: var(--wallet-border);\n    cursor: pointer;\n    color: var(--wallet-text);\n\n    &.offset-testnet {\n        margin: 1rem 0.5rem 0 0;\n    }\n\n    .testnet {\n        background-color: var(--red-500);\n        color: var(--white);\n        text-transform: uppercase;\n        font-weight: bold;\n        font-size: 0.8rem;\n        padding: 0.2rem 0.5rem;\n        border-radius: 999px;\n        z-index: 10;\n\n        position: absolute;\n        right: -0.5rem;\n        top: -0.4rem;\n    }\n\n    .content {\n        z-index: 10;\n    }\n\n    .header {\n        display: flex;\n        flex-wrap: nowrap;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 0.8rem;\n\n        .left {\n            overflow: hidden;\n\n            .name {\n                .indicator {\n                    display: inline-flex;\n                    align-items: center;\n                    justify-content: center;\n                    border-radius: 50%;\n                    width: 1.8rem;\n                    height: 1.8rem;\n                    padding: 0.5rem;\n                    margin-right: 0.8rem;\n                    font-size: 1.2rem;\n                    line-height: 1;\n                    background-color: var(--white-500);\n                    color: var(--azure-500);\n                }\n            }\n        }\n    }\n\n    .balance {\n        display: flex;\n        align-items: center;\n        font-weight: 600;\n        margin-bottom: 0.5rem;\n    }\n\n    .price {\n        font-size: 1.4rem;\n        font-weight: 600;\n        line-height: 1.2;\n\n        display: flex;\n        flex-wrap: nowrap;\n        align-items: baseline;\n\n        .percent,\n        .currency {\n            font-size: 1.4rem;\n            font-weight: 400;\n        }\n    }\n\n    .staking {\n        margin-top: 0.8rem;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n    }\n\n    .account-synchronization {\n        display: flex;\n        align-items: center;\n        width: 100%;\n\n        .progress-bar {\n            border-radius: 1rem;\n            flex: 1 0 auto;\n            height: 0.4rem;\n            overflow: hidden;\n\n            .fill {\n                height: 100%;\n            }\n        }\n\n        .progress-percent {\n            flex: 0 0 auto;\n            font-size: 1.4rem;\n            line-height: 1.2;\n            padding-left: 1rem;\n        }\n    }\n\n    &.active {\n        color: var(--wallet-active-text);\n        border: none;\n        padding: 1.35rem;\n        background: var(--wallet-active-background);\n    }\n\n    &.auditable,\n    &.watch-only {\n        border-width: 0;\n        padding: 1.35rem;\n        background: var(--wallet-auditable-watch-only-background);\n    }\n\n    &.auditable {\n        &:hover:not(.active) {\n            padding: 1.2rem;\n            border-width: 0.15rem;\n        }\n\n        &.active {\n            padding: 1.35rem;\n            border: none;\n            background: var(--wallet-auditable-active-background);\n        }\n    }\n\n    &.watch-only {\n        color: var(--wallet-watch-only-text);\n\n        &:after {\n            content: '';\n            display: block;\n            background: var(--wallet-watch-only-after-background);\n            position: absolute;\n            border-radius: 0.6rem;\n            left: 0.25rem;\n            right: 0.25rem;\n            top: 0.25rem;\n            bottom: 0.25rem;\n            z-index: 1;\n        }\n\n        &.active,\n        &:hover {\n            background: var(--wallet-watch-only-active-background);\n        }\n    }\n\n    &:hover:not(.active):not(.watch-only) {\n        border-color: var(--wallet-border-color-hover);\n    }\n\n    &:focus {\n        outline: none;\n    }\n\n    &:focus-visible {\n        outline: 2px solid;\n        outline-offset: 4px;\n    }\n\n    .progress-bar {\n        background-color: var(--gray-800);\n\n        .fill {\n            background-color: var(--white-500);\n        }\n    }\n}\n\napp-wallet-card {\n    &:last-child {\n        .wallet {\n            margin-bottom: 0 !important;\n        }\n    }\n}\n\n.light {\n    .wallet {\n        &.active {\n            .header {\n                .close {\n                    color: #ffffff;\n                }\n            }\n\n            &.watch-only {\n                .header {\n                    .close {\n                        color: var(--wallet-watch-only-text);\n                    }\n                }\n            }\n        }\n\n        .progress-bar {\n            background-color: #0c0c3a1a;\n\n            .fill {\n                background-color: var(--aqua-500);\n            }\n        }\n\n        &.active .progress-bar {\n            .fill {\n                background-color: var(--white-500);\n            }\n        }\n    }\n}\n", ".mat-tooltip {\n    font-size: 1.6rem;\n    background: var(--tooltip-background);\n}\n\n.mat-tooltip-address {\n    max-width: 90vw !important;\n}\n", ".zano-mat-menu {\n    &.mat-menu-panel {\n        background-color: var(--list-background);\n        border: var(--list-border);\n        border-radius: var(--border-radius);\n        min-height: 5.7rem;\n    }\n\n    .mat-menu-item {\n        height: 4.1rem;\n        line-height: 4.1rem;\n\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n\n        color: var(--main-text);\n    }\n\n    .mat-menu-item .mat-icon-no-color,\n    .mat-menu-submenu-icon {\n        color: var(--main-text);\n    }\n}\n\n.light {\n    .zano-mat-menu {\n        .mat-menu-item:hover:not([disabled]),\n        .mat-menu-item.cdk-program-focused:not([disabled]),\n        .mat-menu-item.cdk-keyboard-focused:not([disabled]),\n        .mat-menu-item-highlighted:not([disabled]) {\n            background-color: rgba(0, 0, 0, 0.04);\n        }\n    }\n}\n", "app-send-details-modal,\napp-success-sweep-bare-outs,\napp-transaction-details-for-custom-assets,\napp-swap-details {\n    .status {\n        .image {\n            max-width: 13rem;\n            max-height: 13rem;\n            width: 100%;\n            height: 100%;\n\n            img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n\n    .details {\n        .header {\n            min-height: 4rem;\n            max-height: 4rem;\n            background-color: var(--details-background);\n        }\n\n        &-wrapper {\n            max-height: 35rem;\n            background-color: var(--details-background);\n            scroll-behavior: smooth;\n        }\n\n        &-list {\n            width: 100%;\n            .item {\n                .image {\n                    max-width: 1.5rem;\n                    max-height: 1.5rem;\n                    width: 100%;\n                    height: 100%;\n\n                    img {\n                        width: 100%;\n                        height: 100%;\n                    }\n                }\n            }\n        }\n    }\n}\n", "app-assign-alias {\n    .assign-alias-tooltip {\n        z-index: 999;\n        max-width: 46rem;\n        background-color: var(--blue-450);\n        color: var(--white-500);\n    }\n\n    .has-no-edit-symbol {\n        position: relative;\n        width: 100%;\n\n        input {\n            padding-left: 2.35rem;\n        }\n\n        &:after {\n            content: '@';\n            position: absolute;\n            display: inline-block;\n            top: 50%;\n            left: 1rem;\n            transform: translateY(-50%);\n        }\n    }\n}\n", ".assets-table {\n    table-layout: auto !important;\n\n    .token-logo {\n        background: var(--blue-300);\n        min-width: 5.5rem;\n        min-height: 5.5rem;\n        max-width: 5.5rem;\n        max-height: 5.5rem;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        border-radius: 50%;\n        overflow: hidden;\n\n        img,\n        svg {\n            width: 100%;\n            height: 100%;\n            object-fit: contain;\n        }\n    }\n}\n", "app-contracts {\n    .container {\n        .wrap-table {\n            table.contracts-table {\n                tbody {\n                    tr {\n                        cursor: pointer;\n                        outline: none !important;\n\n                        .contract {\n                            position: relative;\n\n                            .icon {\n                                flex-shrink: 0;\n\n                                &.new {\n                                    width: 1.7rem;\n                                    height: 1.7rem;\n                                }\n\n                                &.alert {\n                                    width: 1.7rem;\n                                    height: 1.2rem;\n                                }\n\n                                &.purchase,\n                                &.sell {\n                                    width: 1.5rem;\n                                    height: 1.5rem;\n                                }\n                            }\n\n                            span {\n                                text-overflow: ellipsis;\n                                overflow: hidden;\n                            }\n                        }\n\n                        .status,\n                        .comment {\n                            text-overflow: ellipsis;\n                            overflow: hidden;\n                            max-width: 100%;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n", "app-history {\n    .wrap-table {\n        table.history-table {\n            tbody {\n                tr {\n                    .status {\n                        position: relative;\n\n                        .confirmation {\n                            width: 1.7rem;\n                            height: 1.7rem;\n                        }\n\n                        img.status-transaction {\n                            width: 1.5rem;\n                            height: 1.5rem;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n", "app-purchase {\n    .container {\n        position: relative;\n\n        .form {\n            max-width: 100%;\n        }\n\n        .details {\n            .content {\n                .form__field--row {\n                    > div {\n                        flex: 0 1 22rem;\n                        margin-right: 2rem;\n\n                        &:last-child {\n                            margin-right: 0;\n                        }\n                    }\n                }\n            }\n        }\n\n        .purchase-buttons {\n            button {\n                flex: 0 1 33%;\n                margin-right: 0.5rem;\n\n                &:last-child {\n                    margin-right: 0;\n                }\n            }\n        }\n\n        .nullify-block-row {\n            .nullify-block-buttons {\n                button {\n                    flex: 0 1 25%;\n                    margin: 0 0.5rem;\n                }\n            }\n        }\n\n        .time-cancel-block-row {\n            .form__field {\n                width: 25%;\n            }\n\n            .time-cancel-block-buttons {\n                button {\n                    flex: 0 1 25%;\n                    margin: 0 0.5rem;\n                }\n            }\n        }\n    }\n}\n", "app-receive {\n    .container {\n        .wrap-qr {\n            max-width: 30rem;\n            max-height: 30rem;\n\n            img {\n                width: 100%;\n                height: 100%;\n                object-fit: contain;\n                border-radius: 0.8rem;\n            }\n        }\n\n        .address {\n            width: 27.1rem;\n            height: 4rem;\n            border: var(--border);\n        }\n    }\n}\n\n.light {\n    app-receive {\n        .container {\n            .wrap-qr {\n                img {\n                    border: var(--border);\n                }\n            }\n        }\n    }\n}\n", "app-staking {\n    .chart {\n        &-header {\n            .selected-group {\n                min-width: 19rem;\n            }\n\n            .items {\n                .item {\n                    min-width: 18rem;\n                    max-width: 25rem;\n                    min-height: 4rem;\n                    border: var(--border);\n\n                    .left {\n                        min-width: fit-content;\n                        width: auto;\n                    }\n                }\n            }\n        }\n\n        & {\n            position: relative;\n            border: var(--border);\n            min-height: 29rem;\n\n            > div {\n                position: absolute;\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n}\n\n.light {\n    app-staking {\n        .chart {\n            &-header {\n                .items {\n                    .item {\n                        border: var(--table-info-border);\n                    }\n                }\n            }\n        }\n    }\n}\n", "app-wallet {\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    display: flex;\n    flex-direction: column;\n}\n", "app-no-wallet {\n    .no-wallet-wrapper {\n        position: relative;\n\n        app-synchronization-status {\n            position: absolute;\n            left: 0;\n            bottom: 0;\n        }\n    }\n}\n", "app-contacts {\n    .wrap-table {\n        table.contacts-table {\n            thead {\n                tr {\n                    th:last-child {\n                        max-width: 14rem;\n                    }\n                }\n            }\n\n            tbody {\n                tr {\n                    td {\n                        &:last-child {\n                            width: 14rem;\n                            min-width: fit-content;\n                        }\n\n                        .button-wrapper {\n                            button {\n                                margin-right: 0.8rem;\n\n                                &:last-child {\n                                    margin-right: 0;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n", ":focus {\n    outline: none;\n}\n\n.user-is-tabbing :focus {\n    outline: 2px solid;\n    outline-offset: -2px;\n    transition: outline 0.1s ease-in;\n}\n\n.light {\n    .user-is-tabbing :focus {\n        outline-color: black;\n    }\n}\n.dark {\n    .user-is-tabbing :focus {\n        outline-color: white;\n    }\n}\n", ".color-red {\n    color: var(--red-500);\n}\n\n.color-primary {\n    color: var(--azure-500);\n}\n\n.color-aqua {\n    color: var(--aqua-500);\n}\n\n.border-radius-0_8-rem {\n    border-radius: 0.8rem;\n}\n\n.cursor-pointer {\n    cursor: pointer;\n}\n\n.cursor-default {\n    cursor: default !important;\n}\n\n.text-ellipsis {\n    text-overflow: ellipsis;\n    overflow: hidden;\n    white-space: nowrap;\n}\n\n.text-align-center {\n    text-align: center;\n}\n\n.text-align-end {\n    text-align: end;\n}\n\n.word-break-break-all {\n    word-break: break-all;\n}\n\n.word-break-break-word {\n    word-break: break-word;\n}\n\n// background\n\n.bg-light-gray {\n    background-color: var(--gray-900);\n}\n\n.bg-light-blue {\n    background-color: var(--blue-700);\n}\n\n.bg-light-blue-details {\n    background-color: var(--blue-500);\n}\n\n.background-none {\n    background: none;\n}\n\n// overflow\n.overflow-hidden {\n    overflow: hidden;\n}\n\n.overflow-auto {\n    overflow: auto;\n}\n\n.overflow-x-hidden {\n    overflow-x: hidden;\n}\n\n.overflow-y-hidden {\n    overflow-y: hidden;\n}\n\n.overflow-x-auto {\n    overflow-x: auto;\n}\n\n.overflow-y-auto {\n    overflow-y: auto;\n}\n\n.no-scroll {\n    overflow: hidden;\n}\n\n.rotate-90 {\n    transform: rotate(90deg);\n}\n\n.rotate-180 {\n    transform: rotate(180deg);\n}\n\n.rotate-270 {\n    transform: rotate(270deg);\n}\n\n.rotate-360 {\n    transform: rotate(360deg);\n}\n\n.opacity-0 {\n    opacity: 0;\n}\n\n.opacity-1 {\n    opacity: 1;\n}\n", ".ml-auto {\n    margin-left: auto;\n}\n\n.mr-auto {\n    margin-right: auto;\n}\n\n.mt-auto {\n    margin-top: auto;\n}\n\n.mb-auto {\n    margin-bottom: auto;\n}\n\n.mx-0 {\n    margin-left: 0;\n    margin-right: 0;\n}\n\n.mx-0_5 {\n    margin-left: 0.5rem;\n    margin-right: 0.5rem;\n}\n\n.mx-1 {\n    margin-left: 1rem;\n    margin-right: 1rem;\n}\n\n.mx-2 {\n    margin-left: 2rem;\n    margin-right: 2rem;\n}\n\n.mx-3 {\n    margin-left: 3rem;\n    margin-right: 3rem;\n}\n\n.my-0 {\n    margin-top: 0;\n    margin-bottom: 0;\n}\n\n.my-0_5 {\n    margin-top: 0.5rem;\n    margin-bottom: 0.5rem;\n}\n\n.my-1 {\n    margin-top: 1rem;\n    margin-bottom: 1rem;\n}\n\n.my-2 {\n    margin-top: 2rem;\n    margin-bottom: 2rem;\n}\n\n.my-3 {\n    margin-top: 3rem;\n    margin-bottom: 3rem;\n}\n\n.ml-0 {\n    margin-left: 0;\n}\n\n.ml-0_5 {\n    margin-left: 0.5rem;\n}\n\n.ml-1 {\n    margin-left: 1rem;\n}\n\n.ml-2 {\n    margin-left: 2rem;\n}\n\n.ml-3 {\n    margin-left: 3rem;\n}\n\n.mb-0 {\n    margin-bottom: 0;\n}\n\n.mb-0_5 {\n    margin-bottom: 0.5rem;\n}\n\n.mb-1 {\n    margin-bottom: 1rem;\n}\n\n.mb-2 {\n    margin-bottom: 2rem;\n}\n\n.mb-3 {\n    margin-bottom: 3rem;\n}\n\n.mr-0 {\n    margin-right: 0;\n}\n\n.mr-0_5 {\n    margin-right: 0.5rem;\n}\n\n.mr-1 {\n    margin-right: 1rem;\n}\n\n.mr-2 {\n    margin-right: 2rem;\n}\n\n.mr-3 {\n    margin-right: 3rem;\n}\n\n.mt-0 {\n    margin-top: 0;\n}\n\n.mt-0_5 {\n    margin-top: 0.5rem;\n}\n\n.mt-1 {\n    margin-top: 1rem;\n}\n\n.mt-2 {\n    margin-top: 2rem;\n}\n\n.mt-3 {\n    margin-top: 3rem;\n}\n\n.m-0 {\n    margin: 0;\n}\n\n.m-1 {\n    margin: 0.5rem;\n}\n\n.m-1 {\n    margin: 1rem;\n}\n\n.m-2 {\n    margin: 2rem;\n}\n\n.m-3 {\n    margin: 3rem;\n}\n\n.m-auto {\n    margin: auto;\n}\n\n.mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n}\n\n.my-auto {\n    margin-top: auto;\n    margin-bottom: auto;\n}\n", ".px-0 {\n    padding-left: 0;\n    padding-right: 0;\n}\n\n.px-0_5 {\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n}\n\n.px-1 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n}\n\n.px-2 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n}\n\n.px-3 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n}\n\n.py-0 {\n    padding-top: 0;\n    padding-bottom: 0;\n}\n\n.py-0_5 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n}\n\n.py-0_8 {\n    padding-top: 0.8rem;\n    padding-bottom: 0.8rem;\n}\n\n.py-1 {\n    padding-top: 1rem;\n    padding-bottom: 1rem;\n}\n\n.py-2 {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n}\n\n.py-3 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n}\n\n.pl-0 {\n    padding-left: 0;\n}\n\n.pl-0_5 {\n    padding-left: 0.5rem;\n}\n\n.pl-1 {\n    padding-left: 1rem;\n}\n\n.pl-2 {\n    padding-left: 2rem;\n}\n\n.pl-3 {\n    padding-left: 3rem;\n}\n\n.pb-0 {\n    padding-bottom: 0;\n}\n\n.pb-0_5 {\n    padding-bottom: 0.5rem;\n}\n\n.pb-1 {\n    padding-bottom: 1rem;\n}\n\n.pb-2 {\n    padding-bottom: 2rem;\n}\n\n.pb-3 {\n    padding-bottom: 3rem;\n}\n\n.pr-0 {\n    padding-right: 0;\n}\n\n.pr-0_5 {\n    padding-right: 0.5rem;\n}\n\n.pr-1 {\n    padding-right: 1rem;\n}\n\n.pr-2 {\n    padding-right: 2rem;\n}\n\n.pr-3 {\n    padding-right: 3rem;\n}\n\n.pt-0 {\n    padding-top: 0;\n}\n\n.pt-0_5 {\n    padding-top: 0.5rem;\n}\n\n.pt-1 {\n    padding-top: 1rem;\n}\n\n.pt-2 {\n    padding-top: 2rem;\n}\n\n.pt-3 {\n    padding-top: 3rem;\n}\n\n.p-0 {\n    padding: 0;\n}\n\n.p-0_5 {\n    padding: 0.5rem;\n}\n\n.p-1 {\n    padding: 1rem;\n}\n\n.p-2 {\n    padding: 2rem;\n}\n\n.p-3 {\n    padding: 3rem;\n}\n", ".w-100 {\n    width: 100%;\n}\n\n.max-w-100 {\n    max-width: 100%;\n}\n\n.h-100 {\n    height: 100%;\n}\n\n.max-h-90-vh {\n    max-height: 90vh;\n}\n\n.max-h-100 {\n    max-height: 100%;\n}\n\n.min-width-4_4-rem {\n    min-width: 4.4rem;\n}\n\n.min-w-19-rem {\n    min-width: 19rem;\n}\n\n.min-height-4_4-rem {\n    min-height: 4.4rem;\n}\n\n.max-w-19-rem {\n    max-width: 19rem;\n}\n\n.max-w-34-rem {\n    max-width: 34rem;\n}\n\n.max-w-38-rem {\n    max-width: 38rem;\n}\n\n.max-w-42-rem {\n    max-width: 42rem;\n}\n\n.max-w-50-rem {\n    max-width: 50rem;\n}\n\n.max-w-54-rem {\n    max-width: 54rem;\n}\n", ".token-logo {\n    background: var(--blue-300);\n    width: 1.5rem;\n    height: 1.5rem;\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n    border-radius: 50%;\n    overflow: hidden;\n\n    img,\n    svg {\n        width: 100%;\n        height: 100%;\n        object-fit: contain;\n    }\n}\n"], "names": [], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}