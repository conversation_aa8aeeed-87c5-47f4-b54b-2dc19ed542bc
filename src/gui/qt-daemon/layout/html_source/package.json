{"name": "zano", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build --watch": "ng build --output-path \"/Applications/Zano.app/Contents/MacOS/html\" --watch", "build --watch2": "ng build --output-path \"/Applications/Zano 2.app/Contents/MacOS/html\" --watch", "build --watch3": "ng build --output-path \"/work/nazar/Zano/html/\" --watch", "build --html": "node update-build-time.js && ng build --output-path \"../html/\"", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "format": "npx prettier --write ."}, "private": true, "dependencies": {"@angular-builders/custom-webpack": "^14.1.0", "@angular/animations": "^14.2.10", "@angular/cdk": "^14.2.7", "@angular/common": "^14.2.10", "@angular/compiler": "^14.2.10", "@angular/core": "^14.2.10", "@angular/flex-layout": "^14.0.0-beta.41", "@angular/forms": "^14.2.10", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.2.10", "@angular/platform-browser-dynamic": "^14.2.10", "@angular/router": "^14.2.10", "@ng-select/ng-select": "^9.1.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@perfectmemory/ngx-contextmenu": "^14.1.0", "angular-animations": "^0.11.0", "angular-highcharts": "^14.1.7", "bignumber.js": "^9.1.0", "core-js": "^3.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "highcharts": "^10.3.1", "idlejs": "^3.0.0", "json-bignumber": "^1.1.1", "lodash": "^4.17.20", "ngx-pagination": "^6.0.3", "ngx-papaparse": "^5.1.0", "prettier-eslint": "^15.0.1", "qrcode": "^1.5.1", "rxjs": "~7.5.7", "save-dev": "0.0.1-security", "tslib": "^2.4.1", "zone.js": "~0.12.0", "stylus": "github:stylus/stylus#0.59.0"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.9", "@angular-eslint/builder": "14.2.0", "@angular-eslint/eslint-plugin": "14.2.0", "@angular-eslint/eslint-plugin-template": "14.2.0", "@angular-eslint/schematics": "14.2.0", "@angular-eslint/template-parser": "14.2.0", "@angular/cli": "^14.2.9", "@angular/compiler-cli": "^14.2.10", "@angular/language-service": "^14.2.10", "@types/jasmine": "^4.3.0", "@types/jasminewd2": "^2.0.3", "@types/node": "^18.11.9", "@typescript-eslint/eslint-plugin": "5.43.0", "@typescript-eslint/parser": "5.43.0", "eslint": "^8.27.0", "jasmine-core": "^4.5.0", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.1", "karma-chrome-launcher": "^3.1.0", "karma-coverage-istanbul-reporter": "^3.0.2", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "prettier": "2.8.8", "protractor": "^7.0.0", "ts-node": "^10.9.1", "typescript": "~4.8.4"}}