<router-outlet
    *ngIf="[0, 1, 2, 6].includes(variablesService.daemon_state) && !(zanoLoadersService.getState('fullScreen') | async)"
></router-outlet>

<!-- Synch / Error / Complete States -->
<section *ngIf="[3, 4, 5].includes(variablesService.daemon_state)" aria-busy="true" aria-live="polite" class="preloader" role="status">
    <p *ngIf="variablesService.daemon_state === 3" class="mb-2">
        {{ 'SIDEBAR.SYNCHRONIZATION.LOADING' | translate }}
    </p>
    <p *ngIf="variablesService.daemon_state === 4" class="mb-2">
        {{ 'SIDEBAR.SYNCHRONIZATION.ERROR' | translate }}
    </p>
    <p *ngIf="variablesService.daemon_state === 5" class="mb-2">
        {{ 'SIDEBAR.SYNCHRONIZATION.COMPLETE' | translate }}
    </p>
    <div aria-hidden="true" class="loading-bar"></div>
</section>

<!-- Full Screen Loader -->
<section *ngIf="zanoLoadersService.getState('fullScreen') | async" aria-busy="true" aria-live="polite" class="preloader" role="status">
    <p class="mb-2">
        {{ zanoLoadersService.getMessage('fullScreen') | async | translate }}
    </p>
    <div aria-hidden="true" class="loading-bar"></div>
</section>

<app-register-context-templates></app-register-context-templates>

<app-open-wallet-modal *ngIf="needOpenWallets.length" [wallets]="needOpenWallets"></app-open-wallet-modal>
