:host {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem 2rem;
    max-width: 23rem;
    width: 100%;
    height: 100%;
    background-color: var(--sidebar-background);
}

::ng-deep {
    .sidebar {
        &-content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;

            &-wallet-list {
                position: relative;
                height: 100%;
                overflow: hidden;

                .scrolled-content {
                    height: 100%;
                }

                .wallet {
                    &:first-child {
                        margin-top: 3rem;
                    }

                    &:last-child {
                        margin-bottom: 3rem;
                    }
                }

                &::before {
                    content: '';
                    background: var(--gradient-sidebar-content-wallet-list-top);
                    display: block;
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3rem;
                    z-index: 999;
                }

                &::after {
                    content: '';
                    background: var(--gradient-sidebar-content-wallet-list-bottom);
                    display: block;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 3rem;
                    z-index: 999;
                }
            }
        }

        &-nav {
            min-height: 21rem;
            max-height: 25rem;

            & > button {
                margin-bottom: 1rem;
            }
        }

        &-footer {
            display: flex;
            justify-items: center;
            align-items: center;
            width: 100%;
            min-height: 6rem;
            overflow: hidden;

            // centered status Online, Offline
            .synchronization-status {
                .status-container {
                    .offline,
                    .online {
                        justify-content: center;
                    }
                }
            }
        }
    }

    .cdk-drag-placeholder {
        opacity: 0.3;
    }

    .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

    .cdk-drag-preview {
        &.wallet {
            color: var(--white-500);
            background: var(--blue-500);

            &.price {
                color: var(--gray-800);

                .percent {
                    color: var(--gray-800);
                }
            }
        }
    }
}
