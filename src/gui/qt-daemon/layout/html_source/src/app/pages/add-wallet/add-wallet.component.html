<main aria-describedby="add-wallet-description" class="page-container">
    <p class="sr-only" id="add-wallet-description">
        {{ 'ACCESSIBILITY.ADD_WALLET.DESCRIPTIONS.DESCRIPTION1' | translate }}
    </p>

    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 aria-live="assertive" class="ml-2">{{ 'BREADCRUMBS.ADD_WALLET' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <div class="page-content">
        <div class="scrolled-content" fxFlex="1 1 auto" fxLayout="column" fxLayoutAlign="center center">
            <section class="add-wallet w-100" fxLayout="row" fxLayoutAlign="center center">
                <div class="wrap-controls text-align-center max-w-38-rem">
                    <h4 class="mb-2">{{ 'MAIN.TITLE' | translate }}</h4>

                    <button *ngIf="variablesService.daemon_state === 2" [routerLink]="['/create']" appAutofocus
                            class="primary big w-100 mb-1"
                            role="link"
                            type="button">
                        {{ 'MAIN.BUTTON_NEW_WALLET' | translate }}
                    </button>

                    <div *ngIf="variablesService.daemon_state !== 2" [matTooltip]="'Wallet syncing...'" class="mb-1">
                        <button [disabled]="true"
                                class="primary big w-100"
                                type="button">
                            {{ 'MAIN.BUTTON_NEW_WALLET' | translate }}
                        </button>
                    </div>

                    <button (click)="openWallet()" class="primary big w-100 mb-1" type="button">
                        {{ 'MAIN.BUTTON_OPEN_WALLET' | translate }}
                    </button>

                    <button *ngIf="variablesService.daemon_state === 2" [routerLink]="['/restore']"
                            class="outline big w-100 mb-2" role="link" type="button">
                        {{ 'MAIN.BUTTON_RESTORE_BACKUP' | translate }}
                    </button>

                    <div [matTooltip]="'Wallet syncing...'" class="mb-2">
                        <button *ngIf="variablesService.daemon_state !== 2"
                                [disabled]="true" class="outline big w-100"
                                type="button">
                            {{ 'MAIN.BUTTON_RESTORE_BACKUP' | translate }}
                        </button>
                    </div>

                    <button
                        (click)="openInBrowser($event)"
                        (keydown.enter)="openInBrowser($event)"
                        (keydown.space)="openInBrowser($event)"
                        [attr.aria-label]="'MAIN.HELP' | translate"
                        class="text-align-center cursor-pointer how-to-create mx-auto"
                        fxLayout="row"
                        fxLayoutAlign="center center"
                        role="link"
                        type="button"
                    >
                        <mat-icon class="mr-1" svgIcon="zano-question"></mat-icon>
                        <span class="color-primary">{{ 'MAIN.HELP' | translate }}</span>
                    </button>
                </div>
            </section>
        </div>
    </div>
</main>
