<main cdkTrapFocus class="auth">
    <h1 class="sr-only" aria-live="assertive">
        {{ 'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL1' | translate }}
    </h1>

    <div class="auth-card">
        <div aria-hidden="true" class="mb-2">
            <zano-logo></zano-logo>
        </div>

        <ng-container [ngTemplateOutlet]="type === 'reg' ? regMasterPassFormTemp : loginFormTemp"></ng-container>
    </div>

    <app-synchronization-status></app-synchronization-status>
</main>

<ng-template #regMasterPassFormTemp>
    <form
        role="form"
        (ngSubmit)="onSubmitCreatePass()"
        [attr.aria-busy]="submitLoading ? 'true' : null"
        [formGroup]="regMasterPassForm"
        class="form"
    >
        <fieldset class="auth-card-form">
            <legend class="sr-only">
                {{ 'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL2' | translate }}
            </legend>

            <!-- Master Password -->
            <div class="form__field mb-2">
                <label for="master-pass">
                    {{ 'LOGIN.SETUP_MASTER_PASS' | translate }}
                </label>
                <input
                    (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                    [attr.aria-describedby]="'master-pass-error'"
                    [placeholder]="'PLACEHOLDERS.MASTER_PASS_PLACEHOLDER' | translate"
                    appAutofocus
                    autofocus
                    autocomplete="new-password"
                    class="form__field--input"
                    formControlName="password"
                    id="master-pass"
                    type="password"
                />
                <div
                    *ngIf="regMasterPassForm.controls.password.dirty && regMasterPassForm.controls.password.errors"
                    aria-live="assertive"
                    class="error"
                    id="master-pass-error"
                >
                    <div *ngIf="regMasterPassForm.controls.password.hasError('pattern')">
                        {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                    </div>
                </div>
            </div>

            <!-- Confirm Password -->
            <div class="form__field fixed">
                <label for="confirm-pass">
                    {{ 'LOGIN.SETUP_CONFIRM_PASS' | translate }}
                </label>
                <input
                    (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                    [attr.aria-describedby]="regMasterPassForm.errors?.mismatch ? 'confirm-pass-error' : null"
                    class="form__field--input"
                    formControlName="confirmation"
                    id="confirm-pass"
                    placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_CONFIRM' | translate }}"
                    type="password"
                />
                <div
                    *ngIf="
                        regMasterPassForm.controls.password.dirty &&
                        regMasterPassForm.controls.confirmation.dirty &&
                        regMasterPassForm.errors
                    "
                    aria-live="assertive"
                    class="error"
                    id="confirm-pass-error"
                >
                    <div *ngIf="regMasterPassForm.errors['mismatch']">
                        {{ 'LOGIN.FORM_ERRORS.MISMATCH' | translate }}
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- Next -->
        <button
            [attr.aria-busy]="submitLoading"
            [attr.aria-label]="'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL5' | translate"
            [disabled]="
                !regMasterPassForm.controls.password.value.length ||
                !regMasterPassForm.controls.confirmation.value.length ||
                (regMasterPassForm.errors && regMasterPassForm.hasError('mismatch')) ||
                regMasterPassForm.controls.password.errors
            "
            class="primary big w-100 mb-1"
            type="submit"
        >
            {{ 'LOGIN.BUTTON_NEXT' | translate }}
        </button>

        <!-- Skip -->
        <button
            (click)="onSkipCreatePass()"
            [attr.aria-label]="'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL6' | translate"
            [disabled]="regMasterPassForm.controls.password.value.length || regMasterPassForm.controls.confirmation.value.length"
            class="primary big w-100"
            type="button"
        >
            {{ 'LOGIN.BUTTON_SKIP' | translate }}
        </button>
    </form>
</ng-template>

<ng-template #loginFormTemp>
    <form role="form" (ngSubmit)="onSubmitAuthPass()" [attr.aria-busy]="submitLoading ? 'true' : null" [formGroup]="loginForm" class="form">
        <fieldset class="auth-card-form">
            <legend class="sr-only">
                {{ 'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL7' | translate }}
            </legend>

            <div class="form__field fixed">
                <label for="master-pass-login">
                    {{ 'LOGIN.MASTER_PASS' | translate }}
                </label>

                <input
                    (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                    [attr.aria-describedby]="'master-pass-login-error'"
                    [attr.aria-invalid]="loginForm.controls.password.invalid || null"
                    [placeholder]="'PLACEHOLDERS.MASTER_PASS_PLACEHOLDER' | translate"
                    appAutofocus
                    autofocus
                    autocomplete="off"
                    class="form__field--input"
                    formControlName="password"
                    id="master-pass-login"
                    type="password"
                />

                <div
                    [attr.aria-live]="loginForm.controls.password.invalid ? 'assertive' : 'off'"
                    class="error"
                    id="master-pass-login-error"
                >
                    <ng-container
                        *ngIf="
                            loginForm.controls.password.invalid &&
                            (loginForm.controls.password.dirty || loginForm.controls.password.touched)
                        "
                    >
                        <span *ngIf="loginForm.controls.password.hasError('wrong_password')">
                            {{ 'LOGIN.FORM_ERRORS.INVALID_PASS' | translate }}
                        </span>
                    </ng-container>
                </div>
            </div>
        </fieldset>

        <!-- Login Button -->
        <button
            [attr.aria-busy]="submitLoading"
            [attr.aria-label]="'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL8' | translate"
            class="primary big w-100 mb-1"
            type="submit"
        >
            {{ 'LOGIN.BUTTON_NEXT' | translate }}
            <span *ngIf="submitLoading" [ngTemplateOutlet]="buttonLoaderTemp" class="ml-1"></span>
        </button>

        <!-- Reset Button -->
        <button
            (click)="beforeDropSecureAppData()"
            [attr.aria-busy]="resetLoading"
            [attr.aria-label]="'ACCESSIBILITY.AUTH.LOGIN.ARIAL_LABEL9' | translate"
            class="outline big w-100"
            type="button"
        >
            {{ 'LOGIN.BUTTON_RESET' | translate }}
            <span *ngIf="resetLoading" [ngTemplateOutlet]="buttonLoaderTemp" class="ml-1"></span>
        </button>
    </form>
</ng-template>

<ng-template #buttonLoaderTemp>
    <zano-loader></zano-loader>
</ng-template>
