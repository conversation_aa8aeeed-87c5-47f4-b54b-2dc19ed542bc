<main class="auth" fxFlexFill fxLayout="row" fxLayoutAlign="center center">
    <section class="auth-card">
        <div aria-hidden="true" class="logo mb-3" fxLayout="row" fxLayoutAlign="center center">
            <img [src]="zanoLogo" alt="zano-logo" />
        </div>

        <h4 class="mb-2 text-align-center" aria-live="assertive">{{ 'MAIN.TITLE' | translate }}</h4>

        <button [routerLink]="['/create']" class="primary big w-100 mb-1" role="link" type="button">
            {{ 'MAIN.BUTTON_NEW_WALLET' | translate }}
        </button>

        <button (click)="openWallet()" class="primary big w-100 mb-1" role="link" type="button">
            {{ 'MAIN.BUTTON_OPEN_WALLET' | translate }}
        </button>

        <button [routerLink]="['/restore']" class="outline big w-100 mb-2" role="link" type="button">
            {{ 'MAIN.BUTTON_RESTORE_BACKUP' | translate }}
        </button>

        <button
            (click)="openInBrowser($event)"
            (keydown.enter)="openInBrowser($event)"
            (keydown.space)="openInBrowser($event)"
            [attr.aria-label]="'MAIN.HELP' | translate"
            class="text-align-center cursor-pointer how-to-create mx-auto"
            fxLayout="row"
            fxLayoutAlign="center center"
            type="button"
            role="link"
        >
            <mat-icon class="mr-1" svgIcon="zano-question"></mat-icon>
            <span class="color-primary">{{ 'MAIN.HELP' | translate }}</span>
        </button>
    </section>

    <app-synchronization-status></app-synchronization-status>
</main>
