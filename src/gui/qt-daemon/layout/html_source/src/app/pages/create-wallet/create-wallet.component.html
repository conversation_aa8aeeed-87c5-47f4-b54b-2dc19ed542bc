<main class="page-container" aria-describedby="create-wallet-description">
    <p class="sr-only" id="create-wallet-description">
        {{ 'ACCESSIBILITY.CREATE_WALLET.DESCRIPTIONS.DESCRIPTION1' | translate }}
    </p>

    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 class="ml-2" aria-live="assertive">{{ 'BREADCRUMBS.ADD_WALLET' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <div class="page-content">
        <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

        <section class="scrolled-content">
            <form role="form" [formGroup]="createForm" class="form" [attr.aria-busy]="loading ? 'true' : null">
                <fieldset class="form__field">
                    <label for="wallet-name">{{ 'CREATE_WALLET.NAME' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenu($event)"
                        [placeholder]="'PLACEHOLDERS.WALLET_NAME_PLACEHOLDER' | translate"
                        [readonly]="createForm.controls.path.valid"
                        [attr.aria-describedby]="'wallet-name-error'"
                        autofocus
                        appAutofocus
                        class="form__field--input"
                        formControlName="name"
                        id="wallet-name"
                        maxlength="{{ variablesService.maxWalletNameLength }}"
                        type="text"
                    />
                    <div
                        *ngIf="createForm.controls.name.invalid && (createForm.controls.name.dirty || createForm.controls.name.touched)"
                        class="error"
                        id="wallet-name-error"
                        [attr.aria-live]="'assertive'"
                    >
                        <div *ngIf="createForm.controls.name.hasError('duplicate')">
                            {{ 'CREATE_WALLET.FORM_ERRORS.NAME_DUPLICATE' | translate }}
                        </div>
                        <div *ngIf="createForm.controls.name.hasError('required')">
                            {{ 'CREATE_WALLET.FORM_ERRORS.NAME_REQUIRED' | translate }}
                        </div>
                    </div>
                    <div *ngIf="createForm.controls.name.value.length > variablesService.maxWalletNameLength" class="error">
                        {{ 'CREATE_WALLET.FORM_ERRORS.MAX_LENGTH' | translate }}
                    </div>
                </fieldset>

                <fieldset class="form__field">
                    <label for="wallet-password">{{ 'CREATE_WALLET.PASS' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                        [readonly]="createForm.controls.path.valid"
                        [attr.aria-describedby]="'wallet-password-error'"
                        class="form__field--input"
                        formControlName="password"
                        id="wallet-password"
                        placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_NEW' | translate }}"
                        type="password"
                    />
                    <div
                        *ngIf="createForm.controls.password.dirty && createForm.controls.password.invalid"
                        class="error"
                        id="wallet-password-error"
                        [attr.aria-live]="'assertive'"
                    >
                        <div *ngIf="createForm.controls.password.hasError('pattern')">
                            {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                        </div>
                    </div>
                </fieldset>

                <fieldset class="form__field">
                    <label for="confirm-wallet-password">{{ 'CREATE_WALLET.CONFIRM' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                        [class.invalid]="createForm.hasError('mismatch') && createForm.controls.confirm.value.length > 0"
                        [readonly]="createForm.controls.path.valid"
                        [attr.aria-describedby]="'confirm-wallet-password-error'"
                        class="form__field--input"
                        formControlName="confirm"
                        id="confirm-wallet-password"
                        placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_CONFIRM' | translate }}"
                        type="password"
                    />
                    <div
                        *ngIf="
                            createForm.controls.confirm.dirty &&
                            createForm.hasError('mismatch') &&
                            createForm.controls.confirm.value.length > 0
                        "
                        id="confirm-wallet-password-error"
                        [attr.aria-live]="'assertive'"
                        class="error"
                    >
                        {{ 'CREATE_WALLET.FORM_ERRORS.CONFIRM_NOT_MATCH' | translate }}
                    </div>
                </fieldset>

                <button *ngIf="createForm.controls.path.valid" class="outline big w-100 mb-2" disabled type="button">
                    <mat-icon class="mr-1" svgIcon="zano-check-circle"></mat-icon>
                    {{ savedWalletName }}
                </button>

                <button
                    (click)="selectWalletLocation()"
                    *ngIf="createForm.controls.path.invalid"
                    [disabled]="createForm.controls.name.invalid || createForm.controls.password.invalid || createForm.hasError('mismatch')"
                    class="outline big w-100 mb-2"
                    type="button"
                >
                    {{ 'CREATE_WALLET.BUTTON_SELECT' | translate }}
                </button>

                <button (click)="createWallet()" [disabled]="createForm.invalid" class="primary big w-100" type="button">
                    {{ 'CREATE_WALLET.BUTTON_CREATE' | translate }}
                    <span *ngIf="loading" [ngTemplateOutlet]="loaderTemp" class="ml-1"></span>
                </button>
            </form>
        </section>
    </div>
</main>

<ng-template #loaderTemp>
    <zano-loader></zano-loader>
</ng-template>
