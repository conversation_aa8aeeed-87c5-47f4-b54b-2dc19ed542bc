<main class="page-container" aria-describedby="restore-wallet-description">
    <p class="sr-only" id="restore-wallet-description">
        {{ 'ACCESSIBILITY.RESTORE_WALLET.DESCRIPRIONS.DESCRIPTION1' | translate }}
    </p>

    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 class="ml-2" aria-live="assertive">{{ 'BREADCRUMBS.ADD_WALLET' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <section class="page-content">
        <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

        <div class="scrolled-content">
            <form role="form" (ngSubmit)="restore()" [formGroup]="form" class="form">
                <!-- Name -->
                <fieldset class="form__field fixed">
                    <label for="name">{{ 'RESTORE_WALLET.LABEL_NAME' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenu($event)"
                        [attr.aria-describedby]="'name-error'"
                        [placeholder]="'PLACEHOLDERS.WALLET_NAME_PLACEHOLDER' | translate"
                        appAutofocus
                        autofocus
                        class="form__field--input"
                        formControlName="name"
                        id="name"
                        type="text"
                    />
                    <ng-container *ngIf="form.get('name') as control">
                        <div *ngIf="control | isVisibleControlError" aria-live="assertive" class="error" id="name-error">
                            <div *ngIf="control.hasError('duplicate')">
                                {{ 'RESTORE_WALLET.FORM_ERRORS.NAME_DUPLICATE' | translate }}
                            </div>
                            <div *ngIf="control.hasError('maxlength')">
                                {{ 'RESTORE_WALLET.FORM_ERRORS.MAX_LENGTH' | translate }}
                            </div>
                            <div *ngIf="control.hasError('required')">
                                {{ 'RESTORE_WALLET.FORM_ERRORS.NAME_REQUIRED' | translate }}
                            </div>
                        </div>
                    </ng-container>
                </fieldset>

                <!-- Password -->
                <fieldset class="form__field">
                    <label for="password">{{ 'RESTORE_WALLET.PASS' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                        [attr.aria-describedby]="'password-error'"
                        [placeholder]="'PLACEHOLDERS.WALET_PASSWORD_PLACEHOLDER' | translate"
                        class="form__field--input"
                        formControlName="password"
                        id="password"
                        type="password"
                    />
                    <ng-container *ngIf="form.controls.password as control">
                        <div *ngIf="control | isVisibleControlError" aria-live="assertive" class="error" id="password-error">
                            <div *ngIf="control.hasError('pattern')">
                                {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                            </div>
                        </div>
                    </ng-container>
                </fieldset>

                <!-- Confirm Password -->
                <fieldset class="form__field fixed">
                    <label for="confirm">{{ 'RESTORE_WALLET.CONFIRM' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                        [attr.aria-describedby]="'confirm-error'"
                        [placeholder]="'PLACEHOLDERS.CONFIRM_WALET_PASSWORD_PLACEHOLDER' | translate"
                        class="form__field--input"
                        formControlName="confirm"
                        id="confirm"
                        type="password"
                    />
                    <div *ngIf="form.hasError('mismatch')" aria-live="assertive" class="error" id="confirm-error">
                        {{ 'RESTORE_WALLET.FORM_ERRORS.CONFIRM_NOT_MATCH' | translate }}
                    </div>
                </fieldset>

                <!-- Seed phrase -->
                <fieldset class="form__field fixed">
                    <label for="seed-phrase">{{ 'RESTORE_WALLET.LABEL_PHRASE_KEY' | translate }}</label>
                    <input
                        (contextmenu)="variablesService.onContextMenu($event)"
                        [attr.aria-describedby]="'seed-phrase-error'"
                        [placeholder]="'PLACEHOLDERS.SEED_PHRASE_PLACEHOLDER' | translate"
                        class="form__field--input"
                        formControlName="seedPhrase"
                        id="seed-phrase"
                        type="text"
                    />
                    <ng-container *ngIf="form.get('seedPhrase') as control">
                        <div
                            *ngIf="(control.invalid || !this.seedPhraseInfo?.syntax_correct) && (control.dirty || control.touched)"
                            aria-live="assertive"
                            class="error"
                            id="seed-phrase-error"
                        >
                            <div
                                *ngIf="
                                    control.hasError('required') &&
                                    !control.hasError('password_seed_phrase_not_valid') &&
                                    seedPhraseInfo?.require_password
                                "
                            >
                                {{ 'RESTORE_WALLET.FORM_ERRORS.KEY_REQUIRED' | translate }}
                            </div>
                            <div *ngIf="control.hasError('password_seed_phrase_not_valid') && seedPhraseInfo?.require_password">
                                {{ 'RESTORE_WALLET.FORM_ERRORS.PASSWORD_SEED_PHRASE_INCORRECT' | translate }}
                            </div>
                            <div *ngIf="!this.seedPhraseInfo?.syntax_correct">
                                {{ 'RESTORE_WALLET.FORM_ERRORS.SEED_PHRASE_IS_NO_VALID' | translate }}
                            </div>
                        </div>
                    </ng-container>
                </fieldset>

                <!-- Seed password -->
                <ng-container *ngIf="seedPhraseInfo">
                    <ng-container *ngIf="seedPhraseInfo?.syntax_correct && seedPhraseInfo?.require_password">
                        <fieldset class="form__field fixed">
                            <label for="seed-password">{{ 'RESTORE_WALLET.SEED_PASSWORD' | translate }}</label>
                            <input
                                [attr.aria-describedby]="'seed-password-error'"
                                [ngClass]="{
                                    invalid: !seedPhraseInfo?.hash_sum_matched
                                }"
                                [placeholder]="'PLACEHOLDERS.SEED_PHRASE_PLACEHOLDER' | translate"
                                class="form__field--input"
                                formControlName="seedPassword"
                                id="seed-password"
                                type="password"
                            />
                            <ng-container *ngIf="form.get('seedPassword') as control">
                                <div
                                    *ngIf="(control.dirty || control.touched) && !seedPhraseInfo?.hash_sum_matched"
                                    aria-live="assertive"
                                    class="error"
                                    id="seed-password-error"
                                >
                                    <span>{{ 'RESTORE_WALLET.FORM_ERRORS.INCORRECT_PASSWORD' | translate }}</span>
                                </div>
                            </ng-container>
                            <div *ngIf="seedPhraseInfo?.hash_sum_matched" aria-live="assertive" class="success">
                                <span>{{ 'RESTORE_WALLET.OK' | translate }}</span>
                            </div>
                        </fieldset>
                    </ng-container>
                </ng-container>

                <button (click)="selectLocation()" [disabled]="invalidSeedPhraseInfo" class="outline big w-100 mb-2" type="button">
                    {{ selectedLocationWalletName ?? 'RESTORE_WALLET.BUTTON_SELECT' | translate }}
                </button>

                <button [disabled]="isDisabledCreatedWallet" class="primary big w-100 mb-2" type="submit">
                    {{ 'RESTORE_WALLET.BUTTON_CREATE' | translate }}
                </button>
            </form>
        </div>
    </section>
</main>
