<main class="page-container">
    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 class="ml-2" aria-live="assertive">{{ 'BREADCRUMBS.ADD_WALLET' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <section class="page-content">
        <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

        <div class="scrolled-content">
            <div class="wrap-seed-phrase" fxFlex="100" fxLayout="column">
                <form role="form" [formGroup]="detailsForm" class="form">
                    <fieldset class="form__field">
                        <label for="wallet-name">{{ 'WALLET_DETAILS.LABEL_NAME' | translate }}</label>
                        <input
                            (contextmenu)="variablesService.onContextMenu($event)"
                            [attr.aria-describedby]="'wallet-name-error'"
                            [maxLength]="variablesService.maxWalletNameLength"
                            [placeholder]="'PLACEHOLDERS.WALLET_NAME_PLACEHOLDER' | translate"
                            appAutofocus
                            autofocus
                            class="form__field--input"
                            formControlName="name"
                            id="wallet-name"
                            readonly
                            type="text"
                        />
                        <div
                            *ngIf="
                                detailsForm.controls['name'].invalid &&
                                (detailsForm.controls['name'].dirty || detailsForm.controls['name'].touched)
                            "
                            aria-live="assertive"
                            class="error"
                            id="wallet-name-error"
                        >
                            <div *ngIf="detailsForm.controls['name'].errors['duplicate']">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.NAME_DUPLICATE' | translate }}
                            </div>
                            <div *ngIf="detailsForm.get('name').value.length >= variablesService.maxWalletNameLength">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.MAX_LENGTH' | translate }}
                            </div>
                            <div *ngIf="detailsForm.controls['name'].errors['required']">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.NAME_REQUIRED' | translate }}
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="form__field">
                        <label for="wallet-location">{{ 'WALLET_DETAILS.LABEL_FILE_LOCATION' | translate }}</label>
                        <input class="form__field--input" formControlName="path" id="wallet-location" readonly type="text" />
                    </fieldset>
                </form>

                <ng-container *ngIf="!showSeed; else seedPhraseContent">
                    <form role="form" (ngSubmit)="onSubmitSeed()" [formGroup]="seedPhraseForm" class="form form__card pb-2">
                        <fieldset class="form__field">
                            <label for="create-password">{{ 'WALLET_DETAILS.CREATE_PASSWORD_SECURE' | translate }}</label>
                            <input
                                [attr.aria-describedby]="'create-password-error'"
                                class="form__field--input"
                                formControlName="password"
                                id="create-password"
                                placeholder="{{ 'PLACEHOLDERS.PASSWORD_PLACEHOLDER' | translate }}"
                                type="password"
                            />
                            <div
                                *ngIf="
                                    seedPhraseForm.controls.password.invalid &&
                                    (seedPhraseForm.controls['password'].dirty || seedPhraseForm.controls['password'].touched)
                                "
                                aria-live="assertive"
                                class="error"
                                id="create-password-error"
                            >
                                <ng-container *ngIf="seedPhraseForm.controls['password'].hasError('pattern')">
                                    {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                                </ng-container>
                            </div>
                        </fieldset>

                        <fieldset class="form__field">
                            <label for="confirm-password">{{ 'WALLET_DETAILS.FORM.CONFIRM_PASSWORD' | translate }}</label>
                            <input
                                [attr.aria-describedby]="'confirm-password-error'"
                                [class.invalid]="seedPhraseForm.invalid && seedPhraseForm.get('confirmPassword').value.length > 0"
                                class="form__field--input"
                                formControlName="confirmPassword"
                                id="confirm-password"
                                placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_CONFIRM' | translate }}"
                                type="password"
                            />
                            <div
                                *ngIf="
                                    seedPhraseForm.invalid &&
                                    (seedPhraseForm.controls['confirmPassword'].dirty || seedPhraseForm.controls['confirmPassword'].touched)
                                "
                                aria-live="assertive"
                                class="error"
                                id="confirm-password-error"
                            >
                                <div *ngIf="seedPhraseForm.invalid && seedPhraseForm.get('confirmPassword').value.length > 0">
                                    {{ 'WALLET_DETAILS.FORM_ERRORS.PASSWORDS_DONT_MATCH' | translate }}
                                </div>
                            </div>
                        </fieldset>

                        <button [disabled]="!seedPhraseForm.valid" class="primary w-100 big mb-2" type="submit">
                            <mat-icon class="mr-1" svgIcon="zano-check-shield"></mat-icon>
                            {{ 'WALLET_DETAILS.FORM.GENERATE_SECURE_SEED' | translate }}
                        </button>

                        <p class="text-align-center color-primary">
                            <mat-icon class="mr-1" svgIcon="zano-info"></mat-icon>
                            {{ 'WALLET_DETAILS.FORM.SECURED_SEED_WILL_REQUIRE' | translate }}
                        </p>
                    </form>
                </ng-container>

                <ng-template #seedPhraseContent>
                    <div class="seed-phrase form__card pb-2">
                        <div class="header mb-2" fxLayout="row" fxLayoutAlign="space-between center">
                            <div class="left">
                                <span>{{ 'WALLET_DETAILS.LABEL_SEED_PHRASE' | translate }}</span>
                            </div>
                            <div class="right">
                                <span
                                    *ngIf="seedPhraseForm.controls.password.value.length === 0"
                                    class="status color-red"
                                    fxLayout="row"
                                    fxLayoutAlign="start center"
                                >
                                    {{ 'WALLET_DETAILS.SEED_IS_UNSECURED' | translate }}
                                    <mat-icon class="ml-1" svgIcon="zano-unsecured"></mat-icon>
                                </span>
                                <span
                                    *ngIf="seedPhraseForm.controls.password.value.length > 0"
                                    class="status color-aqua"
                                    fxLayout="row"
                                    fxLayoutAlign="start center"
                                >
                                    {{ 'WALLET_DETAILS.SEED_IS_SECURED' | translate }}
                                    <mat-icon class="ml-1" svgIcon="zano-secured"></mat-icon>
                                </span>
                            </div>
                        </div>

                        <div
                            (contextmenu)="variablesService.onContextMenuOnlyCopy($event, seedPhrase)"
                            class="content mb-1"
                            fxLayout="row wrap"
                        >
                            <ng-container *ngFor="let word of seedPhrase.split(' '); let index = index">
                                <div
                                    class="item p-1 mr-1 mb-1 border-radius-0_8-rem"
                                    fxLayout="row nowrap"
                                    fxLayoutAlign="start center"
                                    tabindex="0"
                                    [attr.aria-label]="word"
                                >
                                    <div class="number p-1 mr-1" fxLayout="row" fxLayoutAlign="center center">
                                        {{ index + 1 }}
                                    </div>
                                    <span class="word">{{ word }}</span>
                                </div>
                            </ng-container>
                        </div>
                        <div class="footer max-w-50-rem w-100" fxLayout="column" fxLayoutAlign="start center">
                            <div *ngIf="showSeed" class="wrap-buttons w-100 mb-2" fxLayout="row nowrap">
                                <button (click)="copySeedPhrase()" class="outline big w-100" aria-live="assertive" type="button">
                                    <ng-container *ngIf="!seedPhraseCopied">
                                        <mat-icon class="mr-1" svgIcon="zano-copy"></mat-icon>
                                        {{ 'SEED_PHRASE.BUTTON_COPY' | translate }}
                                    </ng-container>
                                    <ng-container *ngIf="seedPhraseCopied">
                                        <mat-icon class="mr-1" svgIcon="zano-check"></mat-icon>
                                        {{ 'SEED_PHRASE.BUTTON_COPIED' | translate }}
                                    </ng-container>
                                </button>
                            </div>
                            <p *ngIf="seedPhraseForm.controls.password.value.length > 0" class="text-align-center" aria-live="assertive">
                                <mat-icon class="mr-1" svgIcon="zano-info"></mat-icon>
                                <span class="color-primary">{{ 'WALLET_DETAILS.REMEMBER_YOU_WILL_REQUIRE' | translate }}</span>
                            </p>
                        </div>
                    </div>
                </ng-template>
            </div>
        </div>
    </section>
</main>
