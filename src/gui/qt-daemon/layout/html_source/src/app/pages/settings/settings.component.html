<main class="page-container">
    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 class="ml-2" aria-live="assertive">{{ 'SETTINGS.TITLE' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <section class="page-content">
        <div class="scrolled-content">
            <section class="settings">
                <fieldset class="form__field">
                    <label for="language-label">{{ 'SETTINGS.LANGUAGE.TITLE' | translate }}</label>
                    <ng-select
                        autofocus
                        appAutofocus
                        (change)="onLanguageChange()"
                        [(ngModel)]="variablesService.settings.language"
                        [clearable]="false"
                        [items]="languagesOptions"
                        [searchable]="false"
                        bindLabel="language"
                        bindValue="name"
                        class="with-circle"
                        id="language-label"
                    >
                        <ng-template let-item="item" ng-label-tmp>
                            {{ item.language | translate }}
                        </ng-template>
                        <ng-template let-index="index" let-item="item" ng-option-tmp>
                            {{ item.language | translate }}
                        </ng-template>
                    </ng-select>
                </fieldset>

                <fieldset class="form__field">
                    <label for="app-lock-label">{{ 'SETTINGS.APP_LOCK.TITLE' | translate }}</label>
                    <ng-select
                        (change)="onLockChange()"
                        [(ngModel)]="variablesService.settings.appLockTime"
                        [clearable]="false"
                        [items]="appLockOptions"
                        [searchable]="false"
                        bindLabel="translationKey"
                        bindValue="time"
                        class="with-circle"
                        id="app-lock-label"
                    >
                        <ng-template let-item="item" ng-label-tmp>
                            {{ item.translationKey | translate }}
                        </ng-template>
                        <ng-template let-index="index" let-item="item" ng-option-tmp>
                            {{ item.translationKey | translate }}
                        </ng-template>
                    </ng-select>
                </fieldset>

                <fieldset class="form__field">
                    <label for="scale-label">{{ 'SETTINGS.SCALE.TITLE' | translate }}</label>
                    <ng-select
                        (change)="setScale()"
                        [(ngModel)]="variablesService.settings.scale"
                        [clearable]="false"
                        [items]="appScaleOptions"
                        [searchable]="false"
                        bindLabel="name"
                        bindValue="value"
                        class="with-circle"
                        id="scale-label"
                    >
                        <ng-template let-item="item" ng-label-tmp>
                            {{ item.name | translate }}
                        </ng-template>
                        <ng-template let-index="index" let-item="item" ng-option-tmp>
                            {{ item.name | translate }}
                        </ng-template>
                    </ng-select>
                </fieldset>

                <fieldset class="form__field">
                    <label for="app-log-label">{{ 'SETTINGS.APP_LOG_TITLE' | translate }}</label>
                    <ng-select
                        (change)="onLogChange()"
                        [(ngModel)]="variablesService.settings.appLog"
                        [clearable]="false"
                        [items]="appLogOptions"
                        [searchable]="false"
                        bindLabel="id"
                        bindValue="id"
                        class="with-circle"
                        id="app-log-label"
                    >
                    </ng-select>
                </fieldset>

                <fieldset class="form__field">
                    <label for="currency-label">{{ 'SETTINGS.CURRENCY_TITLE' | translate }}</label>
                    <ng-select
                        (change)="onCurrencyChange()"
                        [(ngModel)]="variablesService.settings.currency"
                        [clearable]="false"
                        [items]="currenciesItems"
                        [searchable]="false"
                        bindLabel="label"
                        bindValue="id"
                        class="with-circle"
                        id="currency-label"
                    >
                    </ng-select>
                </fieldset>

                <fieldset class="form__field">
                    <label id="notifications-label">{{ 'SETTINGS.NOTIFICATIONS' | translate }}</label>
                    <app-switch
                        (emitChange)="toggleNotifications()"
                        [value]="currentNotificationsState"
                        aria-labelledby="notifications-label"
                    ></app-switch>
                </fieldset>

                <fieldset class="form__field">
                    <label id="use-tor-to-relay-transactions-label"
                        >{{ 'SETTINGS.USE_TOR_TO_RELAY_TRANSACTIONS' | translate }} (Temporarily disabled)</label
                    >
                    <app-switch
                        (emitChange)="toggleUseTor()"
                        [disabled]="true"
                        [value]="false && appUseTor"
                        aria-labelledby="use-tor-to-relay-transactions-label"
                    ></app-switch>
                </fieldset>

                <fieldset class="form__field">
                    <label id="dark-theme-label">{{ 'SETTINGS.DARK_THEME' | translate }}</label>
                    <app-switch
                        (emitChange)="toggleDarkTheme()"
                        [value]="variablesService.settings.isDarkTheme"
                        aria-labelledby="dark-theme-label"
                    ></app-switch>
                </fieldset>

                <fieldset class="form__field">
                    <label id="show-balance-label">{{ 'SETTINGS.SHOW_BALANCE' | translate }}</label>
                    <app-switch
                        (emitChange)="showPrice()"
                        [value]="this.variablesService.visibilityBalance$ | async"
                        aria-labelledby="show-balance-label"
                    ></app-switch>
                </fieldset>

                <form role="form" [formGroup]="zanoCompanionForm">
                    <div class="form__field">
                        <label id="zano-companion-label">
                            {{ 'SETTINGS.FORM.ZANO_COMPANION.LABELS.LABEL1' | translate }}
                            <ng-container *ngIf="!variablesService.appPass">
                                {{ 'SETTINGS.FORM.ZANO_COMPANION.LABELS.LABEL4' | translate }}
                            </ng-container>
                        </label>
                        <app-switch aria-labelledby="zano-companion-label" formControlName="zanoCompation"></app-switch>
                    </div>
                    <ng-container *ngIf="zanoCompanionForm.controls.zanoCompation.getRawValue()">
                        <div class="form__card">
                            <fieldset class="form__field form__field--secret">
                                <label for="field-secret">{{ 'SETTINGS.FORM.ZANO_COMPANION.LABELS.LABEL2' | translate }}</label>
                                <input
                                    (contextmenu)="
                                        variablesService.onContextMenuOnlyCopy($event, zanoCompanionForm.controls['secret'].value)
                                    "
                                    [class.invalid]="zanoCompanionForm.controls['secret'].invalid"
                                    [readonly]="true"
                                    class="form__field--input"
                                    formControlName="secret"
                                    id="field-secret"
                                    type="text"
                                />

                                <button
                                    (click)="copySecret()"
                                    [attr.aria-label]="'ACCESSIBILITY.SETTINGS.ARIA_LABEL2' | translate"
                                    class="btn--copy"
                                    type="button"
                                >
                                    <mat-icon svgIcon="zano-copy"></mat-icon>
                                </button>

                                <button
                                    (click)="regenerateSecret()"
                                    [attr.aria-label]="'ACCESSIBILITY.SETTINGS.ARIA_LABEL3' | translate"
                                    class="btn--regenerate"
                                    type="button"
                                >
                                    <mat-icon svgIcon="zano-regenerate"></mat-icon>
                                </button>

                                <div
                                    *ngIf="isSecretWasCopied"
                                    aria-live="polite"
                                    class="info success"
                                    role="alert"
                                    style="text-align: right"
                                >
                                    {{ 'SETTINGS.SECRET_WAS_COPIED' | translate }}
                                </div>
                            </fieldset>

                            <fieldset class="form__field">
                                <label for="field-port">{{ 'SETTINGS.FORM.ZANO_COMPANION.LABELS.LABEL3' | translate }}</label>
                                <input
                                    (contextmenu)="
                                        variablesService.onContextMenuOnlyCopy($event, this.variablesService.rpc_port?.toString())
                                    "
                                    [readonly]="true"
                                    [value]="this.variablesService.rpc_port"
                                    class="form__field--input"
                                    id="field-port"
                                    type="text"
                                />
                            </fieldset>
                        </div>
                    </ng-container>
                </form>

                <form role="form" (ngSubmit)="onSubmitChangePass()" [formGroup]="changeForm">
                    <h4 class="master-password-title mb-2">
                        {{
                            (!variablesService.appPass ? 'SETTINGS.MASTER_PASSWORD.TITLE1' : 'SETTINGS.MASTER_PASSWORD.TITLE2') | translate
                        }}
                    </h4>

                    <div class="form__card">
                        <fieldset *ngIf="variablesService.appPass" class="form__field">
                            <label for="old-password">{{ 'SETTINGS.MASTER_PASSWORD.OLD' | translate }}</label>
                            <input
                                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                                [attr.aria-describedby]="'old-password-error old-password-error2'"
                                [class.invalid]="
                                    changeForm.invalid &&
                                    changeForm.controls['password'].valid &&
                                    (changeForm.controls['password'].dirty || changeForm.controls['password'].touched) &&
                                    changeForm.errors &&
                                    changeForm.errors['pass_mismatch'] &&
                                    changeForm.get('password').value.length > 0
                                "
                                class="form__field--input"
                                formControlName="password"
                                id="old-password"
                                placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_OLD' | translate }}"
                                type="password"
                            />
                            <div
                                *ngIf="
                                    changeForm.invalid &&
                                    changeForm.controls['password'].valid &&
                                    (changeForm.controls['password'].dirty || changeForm.controls['password'].touched) &&
                                    changeForm.errors &&
                                    changeForm.errors['pass_mismatch'] &&
                                    changeForm.get('password').value.length > 0
                                "
                                aria-live="assertive"
                                class="error"
                                id="old-password-error"
                            >
                                {{ 'SETTINGS.FORM_ERRORS.CURRENT_PASS_NOT_MATCH' | translate }}
                            </div>
                            <div
                                *ngIf="
                                    changeForm.invalid &&
                                    (changeForm.controls['password'].dirty || changeForm.controls['password'].touched) &&
                                    changeForm.controls['password'].errors?.pattern
                                "
                                aria-live="assertive"
                                class="error"
                                id="old-password-error2"
                            >
                                {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                            </div>
                        </fieldset>

                        <fieldset class="form__field">
                            <label for="new-password">{{ 'SETTINGS.MASTER_PASSWORD.NEW' | translate }}</label>
                            <input
                                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                                [attr.aria-describedby]="'new-password-error'"
                                [class.invalid]="changeForm.controls['new_password'].touched && changeForm.controls['new_password'].invalid"
                                class="form__field--input"
                                formControlName="new_password"
                                id="new-password"
                                placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_NEW' | translate }}"
                                type="password"
                            />
                            <div
                                *ngIf="changeForm.controls['new_password'].touched && changeForm.controls['new_password'].invalid"
                                aria-live="assertive"
                                class="error"
                                id="new-password-error"
                            >
                                <div *ngIf="changeForm.controls['new_password'].errors?.pattern">
                                    {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                                </div>
                                <div *ngIf="changeForm.controls['new_password'].hasError('required')">
                                    {{ 'ERRORS.REQUIRED' | translate }}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="form__field">
                            <label for="confirm-password">{{ 'SETTINGS.MASTER_PASSWORD.CONFIRM' | translate }}</label>
                            <input
                                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                                [attr.aria-describedby]="'confirm-password-error'"
                                [class.invalid]="
                                    changeForm.invalid &&
                                    (changeForm.controls['new_confirmation'].dirty || changeForm.controls['new_confirmation'].touched) &&
                                    changeForm.errors &&
                                    changeForm.errors['mismatch'] &&
                                    changeForm.get('new_confirmation').value.length > 0
                                "
                                class="form__field--input"
                                formControlName="new_confirmation"
                                id="confirm-password"
                                placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_CONFIRM' | translate }}"
                                type="password"
                            />
                            <div
                                *ngIf="
                                    changeForm.invalid &&
                                    (changeForm.controls['new_confirmation'].dirty || changeForm.controls['new_confirmation'].touched) &&
                                    changeForm.errors &&
                                    changeForm.errors['mismatch'] &&
                                    changeForm.get('new_confirmation').value.length > 0
                                "
                                aria-live="assertive"
                                class="error"
                                id="confirm-password-error"
                            >
                                {{ 'SETTINGS.FORM_ERRORS.CONFIRM_NOT_MATCH' | translate }}
                            </div>
                        </fieldset>
                    </div>

                    <div class="submit-button-container">
                        <button [disabled]="!changeForm.valid" class="primary big max-w-19-rem w-100" type="submit">
                            {{ 'SETTINGS.MASTER_PASSWORD.BUTTON' | translate }}
                        </button>
                        <span *ngIf="ifSaved" [class.active]="ifSaved" class="ml-1 color-aqua" role="alert">{{
                            'SETTINGS.SETTINGS_SAVED' | translate
                        }}</span>
                    </div>
                </form>

                <p *ngIf="!isBuildVersionWasCopied" class="mt-2">
                    Build version: {{ currentBuild }}
                    <button
                        (click)="copyBuildVersion()"
                        [attr.aria-label]="'ACCESSIBILITY.SETTINGS.ARIA_LABEL1' | translate"
                        class="btn--copy"
                        type="button"
                    >
                        <mat-icon svgIcon="zano-copy"></mat-icon>
                    </button>
                </p>

                <p *ngIf="isBuildVersionWasCopied" aria-live="polite" class="mt-2" role="alert" style="color: var(--aqua-500)">
                    {{ 'Build version was copied' | translate }}
                </p>
            </section>
        </div>
    </section>
</main>
