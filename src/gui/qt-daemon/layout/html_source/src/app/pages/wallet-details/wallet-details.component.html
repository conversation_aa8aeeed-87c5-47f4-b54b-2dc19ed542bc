<main class="page-container">
    <section class="toolbar mb-2">
        <div class="left">
            <app-back-button></app-back-button>
            <h1 class="ml-2" aria-live="assertive">{{ 'BREADCRUMBS.WALLET_DETAILS' | translate }}</h1>
        </div>
        <div class="right"></div>
    </section>

    <section class="page-content">
        <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

        <div class="scrolled-content">
            <div fxFlexFill fxLayout="column" fxLayoutAlign="start stretch">
                <form (ngSubmit)="beforeSubmitDetails()" [formGroup]="detailsForm" class="form" role="form">
                    <fieldset class="form__field">
                        <label for="wallet-name">{{ 'WALLET_DETAILS.LABEL_NAME' | translate }}</label>

                        <input
                            (contextmenu)="variablesService.onContextMenu($event)"
                            [maxLength]="variablesService.maxWalletNameLength"
                            [placeholder]="'PLACEHOLDERS.WALLET_NAME_PLACEHOLDER' | translate"
                            aria-describedby="wallet-name-error"
                            class="form__field--input"
                            formControlName="name"
                            id="wallet-name"
                            type="text"
                        />

                        <div
                            *ngIf="
                                detailsForm.controls.name.invalid && (detailsForm.controls.name.dirty || detailsForm.controls.name.touched)
                            "
                            aria-live="assertive"
                            class="error"
                            id="wallet-name-error"
                        >
                            <div *ngIf="detailsForm.controls.name.errors['duplicate']">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.NAME_DUPLICATE' | translate }}
                            </div>
                            <div *ngIf="detailsForm.controls.name.value.length >= variablesService.maxWalletNameLength">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.MAX_LENGTH' | translate }}
                            </div>
                            <div *ngIf="detailsForm.controls.name.hasError('required')">
                                {{ 'WALLET_DETAILS.FORM_ERRORS.NAME_REQUIRED' | translate }}
                            </div>
                        </div>
                    </fieldset>

                    <div fxLayout="row nowrap" fxLayoutAlign="start center" fxLayoutGap="1rem">
                        <button [disabled]="detailsForm.invalid" class="primary big max-w-19-rem w-100 mb-1" type="submit">
                            {{ 'SETTINGS.MASTER_PASSWORD.BUTTON' | translate }}
                        </button>
                        <p *ngIf="ifSaved" aria-live="assertive" class="color-aqua">Saved!</p>
                    </div>

                    <fieldset class="form__field">
                        <label for="wallet-location">{{ 'WALLET_DETAILS.LABEL_FILE_LOCATION' | translate }}</label>
                        <input
                            (contextmenu)="variablesService.onContextMenuOnlyCopy($event, detailsForm.controls.path.value)"
                            class="form__field--input cursor-default"
                            formControlName="path"
                            id="wallet-location"
                            readonly
                            type="text"
                        />
                    </fieldset>
                </form>

                <ng-container *ngIf="!variablesService.current_wallet?.is_auditable || !variablesService.current_wallet?.is_watch_only">
                    <ng-container *ngIf="!showSeed; else seedPhraseContent">
                        <form
                            (ngSubmit)="beforeSubmitPasswordSeedPhrase()"
                            [formGroup]="passwordSeedPhraseForm"
                            class="form form__card pb-2"
                            fxFlex="0 0 auto"
                            fxLayout="column"
                            fxLayoutAlign="start center"
                            role="form"
                        >
                            <fieldset class="form__field">
                                <label for="create-password">{{ 'WALLET_DETAILS.CREATE_PASSWORD_SECURE' | translate }}</label>

                                <input
                                    aria-describedby="password-error"
                                    class="form__field--input"
                                    formControlName="password"
                                    id="create-password"
                                    placeholder="{{ 'PLACEHOLDERS.PASSWORD_PLACEHOLDER' | translate }}"
                                    type="password"
                                />

                                <div
                                    *ngIf="
                                        passwordSeedPhraseForm.controls.password.invalid &&
                                        (passwordSeedPhraseForm.controls['password'].dirty ||
                                            passwordSeedPhraseForm.controls['password'].touched)
                                    "
                                    aria-live="assertive"
                                    class="error"
                                    id="password-error"
                                >
                                    <ng-container *ngIf="passwordSeedPhraseForm.controls['password'].hasError('pattern')">
                                        {{ 'ERRORS.REGEXP_INVALID_PASSWORD' | translate }}
                                    </ng-container>
                                </div>
                            </fieldset>

                            <fieldset class="form__field">
                                <label for="confirm-password">{{ 'WALLET_DETAILS.FORM.CONFIRM_PASSWORD' | translate }}</label>

                                <input
                                    [class.invalid]="
                                        passwordSeedPhraseForm.invalid && passwordSeedPhraseForm.get('confirmPassword').value.length > 0
                                    "
                                    aria-describedby="confirm-password-error"
                                    class="form__field--input"
                                    formControlName="confirmPassword"
                                    id="confirm-password"
                                    placeholder="{{ 'PLACEHOLDERS.PLACEHOLDER_CONFIRM' | translate }}"
                                    type="password"
                                />

                                <div
                                    *ngIf="
                                        passwordSeedPhraseForm.invalid &&
                                        (passwordSeedPhraseForm.controls['confirmPassword'].dirty ||
                                            passwordSeedPhraseForm.controls['confirmPassword'].touched)
                                    "
                                    aria-live="assertive"
                                    class="error"
                                    id="confirm-password-error"
                                >
                                    <div
                                        *ngIf="
                                            passwordSeedPhraseForm.invalid && passwordSeedPhraseForm.get('confirmPassword').value.length > 0
                                        "
                                    >
                                        {{ 'WALLET_DETAILS.FORM_ERRORS.PASSWORDS_DONT_MATCH' | translate }}
                                    </div>
                                </div>
                            </fieldset>

                            <button [disabled]="!passwordSeedPhraseForm.valid" class="primary big w-100 mb-2" type="submit">
                                <mat-icon class="mr-1" svgIcon="zano-check-shield"></mat-icon>
                                {{ 'WALLET_DETAILS.FORM.GENERATE_SECURE_SEED' | translate }}
                            </button>

                            <p class="color-primary" fxLayout="row" fxLayoutAlign="center center">
                                <mat-icon class="mr-1" svgIcon="zano-info"></mat-icon>
                                {{ 'WALLET_DETAILS.FORM.SECURED_SEED_WILL_REQUIRE' | translate }}
                            </p>
                        </form>
                    </ng-container>

                    <ng-template #seedPhraseContent>
                        <div class="seed-phrase form__card pb-2" fxFlex="0 0 auto" fxLayout="column">
                            <div class="header mb-2" fxFlex="0 0 auto" fxLayout="row" fxLayoutAlign="space-between center">
                                <div class="left">
                                    <span>{{ 'WALLET_DETAILS.LABEL_SEED_PHRASE' | translate }}</span>
                                </div>
                                <div class="right">
                                    <span
                                        *ngIf="passwordSeedPhraseForm.controls.password.value.length === 0"
                                        class="status color-red"
                                        fxLayout="row"
                                        fxLayoutAlign="start center"
                                        aria-live="assertive"
                                    >
                                        {{ 'WALLET_DETAILS.SEED_IS_UNSECURED' | translate }}
                                        <mat-icon class="ml-1" svgIcon="zano-unsecured"></mat-icon>
                                    </span>
                                    <span
                                        *ngIf="passwordSeedPhraseForm.controls.password.value.length > 0"
                                        class="status color-aqua"
                                        fxLayout="row"
                                        fxLayoutAlign="start center"
                                        aria-live="assertive"
                                    >
                                        {{ 'WALLET_DETAILS.SEED_IS_SECURED' | translate }}
                                        <mat-icon class="ml-1" svgIcon="zano-secured"></mat-icon>
                                    </span>
                                </div>
                            </div>
                            <div
                                (contextmenu)="variablesService.onContextMenuOnlyCopy($event, seedPhrase)"
                                class="content mb-1"
                                fxLayout="row wrap"
                                [attr.aria-label]="'WALLET_DETAILS.LABEL_SEED_PHRASE' | translate"
                            >
                                <ng-container *ngFor="let word of seedPhrase.split(' '); let index = index">
                                    <div
                                        class="item p-1 mr-1 mb-1 border-radius-0_8-rem"
                                        fxLayout="row nowrap"
                                        fxLayoutAlign="start center"
                                        tabindex="0"
                                        [attr.aria-label]="word"
                                    >
                                        <div class="number p-1 mr-1" fxLayout="row" fxLayoutAlign="center center">
                                            {{ index + 1 }}
                                        </div>
                                        <span class="word" fxLayout="row">{{ word }}</span>
                                    </div>
                                </ng-container>
                            </div>

                            <div class="footer max-w-50-rem w-100" fxLayout="column">
                                <button
                                    (click)="copySeedPhrase()"
                                    [attr.aria-label]="'SEED_PHRASE.BUTTON_COPY' | translate"
                                    class="outline big w-100 mb-2"
                                    type="button"
                                >
                                    <ng-container *ngIf="!seedPhraseCopied">
                                        <mat-icon class="mr-1" svgIcon="zano-copy"></mat-icon>
                                        {{ 'SEED_PHRASE.BUTTON_COPY' | translate }}
                                    </ng-container>
                                    <ng-container *ngIf="seedPhraseCopied">
                                        <mat-icon class="mr-1" aria-live="assertive" svgIcon="zano-check"></mat-icon>
                                        {{ 'SEED_PHRASE.BUTTON_COPIED' | translate }}
                                    </ng-container>
                                </button>

                                <p
                                    *ngIf="passwordSeedPhraseForm.controls.password.value.length > 0"
                                    class="text-align-center"
                                    aria-live="assertive"
                                >
                                    <mat-icon class="mr-1" svgIcon="zano-info"></mat-icon>
                                    <span class="color-primary">{{ 'WALLET_DETAILS.REMEMBER_YOU_WILL_REQUIRE' | translate }}</span>
                                </p>
                            </div>
                        </div>
                    </ng-template>
                </ng-container>
            </div>
        </div>
    </section>
</main>
