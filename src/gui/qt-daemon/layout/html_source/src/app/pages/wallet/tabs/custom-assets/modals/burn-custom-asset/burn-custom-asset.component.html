<form role="form" (ngSubmit)="submit()" [formGroup]="form">
    <h3 mat-dialog-title>
        {{ 'BURN_CUSTOM_ASSET.LABELS.LABEL1' | translate }}
    </h3>

    <mat-dialog-content>
        <fieldset class="form__field mb-0">
            <label for="amount">
                {{ 'BURN_CUSTOM_ASSET.LABELS.LABEL2' | translate }}
                <span class="color-red">*</span>
            </label>

            <input
                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                [class.invalid]="form.controls.amount.touched && form.controls.amount.invalid"
                [decimalPoint]="data.asset_info.decimal_point"
                [attr.aria-describedby]="'amount-error'"
                [placeholder]="'1000000'"
                appInputValidate="money"
                autofocus
                appAutofocus
                class="form__field--input"
                formControlName="amount"
                id="amount"
                name="amount"
                type="text"
            />

            <div
                *ngIf="form.controls.amount.invalid && (form.controls.amount.touched || form.controls.amount.dirty)"
                aria-live="assertive"
                id="amount-error"
                class="error"
            >
                <div *ngIf="form.controls.amount.hasError('required')">
                    {{ 'ERRORS.REQUIRED' | translate }}
                </div>
                <div *ngIf="form.controls.amount.hasError('insufficientFunds')">
                    {{ form.controls.amount.errors['insufficientFunds'].errorText | translate }}
                </div>
                <div *ngIf="form.controls.amount.hasError('asset_not_found')">
                    {{ 'ERRORS.ASSET_NOT_FOUND' | translate }}
                </div>
                <div *ngIf="form.controls.amount.hasError('greater_max')">
                    {{ 'ERRORS.MAX' | translate : { max: form.controls.amount.errors['greater_max'].max } }}
                </div>
            </div>
        </fieldset>
    </mat-dialog-content>

    <mat-dialog-actions>
        <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
            <button mat-dialog-close class="outline big w-100" type="button">
                {{ 'BURN_CUSTOM_ASSET.BUTTONS.BUTTON1' | translate }}
            </button>
            <button [disabled]="form.invalid" class="primary big w-100" type="submit">
                {{ 'BURN_CUSTOM_ASSET.BUTTONS.BUTTON2' | translate }}
            </button>
        </div>
    </mat-dialog-actions>
</form>
