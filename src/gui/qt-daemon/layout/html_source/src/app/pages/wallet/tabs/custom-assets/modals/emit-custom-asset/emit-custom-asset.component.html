<form role="form" (ngSubmit)="submit()" [formGroup]="form">
    <h3 mat-dialog-title>
        {{ 'EMIT_CUSTOM_ASSET.LABELS.LABEL1' | translate }}
    </h3>

    <mat-dialog-content>
        <fieldset class="form__field mb-0">
            <label for="amount">
                {{ 'EMIT_CUSTOM_ASSET.LABELS.LABEL2' | translate }}
                <span class="color-red">*</span>
            </label>
            <input
                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                [class.invalid]="form.controls.amount.touched && form.controls.amount.invalid"
                [decimalPoint]="data.asset_info.decimal_point"
                [placeholder]="'1000000'"
                aria-describedby="money-error"
                appInputValidate="money"
                autofocus
                appAutofocus
                class="form__field--input"
                formControlName="amount"
                id="amount"
                name="amount"
                type="text"
            />
            <div *ngIf="form.controls.amount.touched && form.controls.amount.invalid" aria-live="assertive" id="money-error" class="error">
                <div *ngIf="form.controls.amount.hasError('required')">
                    {{ 'ERRORS.REQUIRED' | translate }}
                </div>
                <div *ngIf="form.controls.amount.hasError('greater_than_total_max_supply')">
                    {{ 'ERRORS.GREATER_THAN_TOTAL_MAX_SUPPLY' | translate }}
                </div>
            </div>
        </fieldset>
    </mat-dialog-content>

    <mat-dialog-actions>
        <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
            <button mat-dialog-close class="outline big w-100" type="button">
                {{ 'EMIT_CUSTOM_ASSET.BUTTONS.BUTTON1' | translate }}
            </button>
            <button [disabled]="form.invalid" class="primary big w-100" type="submit">
                {{ 'EMIT_CUSTOM_ASSET.BUTTONS.BUTTON2' | translate }}
            </button>
        </div>
    </mat-dialog-actions>
</form>
