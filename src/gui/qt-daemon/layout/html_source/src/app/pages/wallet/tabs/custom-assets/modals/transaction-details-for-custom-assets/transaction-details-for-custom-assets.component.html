<h3 mat-dialog-title>
    {{ 'SEND_DETAILS_MODAL.TITLE1' | translate }}
</h3>

<mat-dialog-content fxLayout="column">
    <div class="status mb-2" fxFlex="0 0 auto" fxLayout="column" fxLayoutAlign=" center" role="status" aria-live="polite">
        <div *ngIf="status === 'success'" class="image">
            <img alt="success" src="assets/icons/aqua/transaction_success.svg" />
        </div>

        <div *ngIf="status === 'error'" class="image">
            <img alt="failed" class="image" src="assets/icons/red/transaction_failed.svg" />
        </div>

        <div *ngIf="status === 'loading'" [attr.aria-label]="'COMMON.LOADING' | translate" class="loader"></div>

        <p class="color-primary mt-2">
            {{
                {
                    success: 'TOR_LIB_STATE.STATE_SENT_SUCCESS',
                    error: 'TOR_LIB_STATE.STATE_SEND_FAILED',
                    loading: 'COMMON.LOADING'
                }[status] | translate
            }}
        </p>
    </div>

    <div *ngIf="details || error" class="details border-radius-0_8-rem overflow-hidden" fxFlex="0 0 auto" fxLayout="column">
        <div
            (click)="toggleDetails()"
            class="header overflow-hidden py-1 px-2 w-100 cursor-pointer"
            tabindex="0"
            [attr.aria-expanded]="isShowDetailsState"
            [attr.aria-label]="'SEND_DETAILS_MODAL.TITLE2' | translate"
            role="button"
            fxLayout="row"
            fxLayoutAlign="space-between center"
        >
            <p class="title text-ellipsis mr-2">
                {{ 'SEND_DETAILS_MODAL.TITLE2' | translate }}
            </p>
            <button fxLayout="row" fxLayoutAlign="center center">
                <mat-icon
                    [ngClass]="{
                        'rotate-180': isShowDetailsState
                    }"
                    class="ml-1"
                    svgIcon="zano-dropdown-arrow-down"
                ></mat-icon>
            </button>
        </div>
        <div
            [class.px-2]="isShowDetailsState"
            [class.py-1]="isShowDetailsState"
            [fxHide]="!isShowDetailsState"
            class="details-wrapper"
            fxFlex="1 1 auto"
            fxLayout="row"
        >
            <ul #elDetailsList class="details-list scrolled-content">
                <ng-container *ngIf="details">
                    <ng-container *ngIf="details.new_asset_id as new_asset_id">
                        <li class="item mb-1 color-primary" fxLayout="row nowrap" fxLayoutAlign="start center">
                            <span class="text word-break-break-all"> New asset id: {{ new_asset_id || '---' }} </span>
                            <app-copy-button [value]="new_asset_id" class="ml-1"></app-copy-button>
                        </li>
                    </ng-container>
                    <ng-container *ngIf="details.result_tx as result_tx">
                        <li class="item mb-1 color-primary" fxLayout="row nowrap" fxLayoutAlign="start center">
                            <span (click)="openInBrowser(result_tx)" class="text word-break-break-all cursor-pointer">
                                Result tx: {{ result_tx }}
                            </span>
                            <app-copy-button [value]="result_tx" class="ml-1"></app-copy-button>
                        </li>
                    </ng-container>
                </ng-container>
                <ng-container *ngIf="error">
                    <li class="item mb-1 color-red" fxLayout="row nowrap" fxLayoutAlign="start center">
                        <span class="text word-break-break-all">
                            Error: {{ error.message ? ('ERRORS' + '.' + error.message | translate) : ('ERRORS.OPS_UNKNOWN' | translate) }}
                        </span>
                    </li>
                </ng-container>
            </ul>
        </div>
    </div>
</mat-dialog-content>

<mat-dialog-actions>
    <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
        <button [mat-dialog-close]="status === 'success'" class="outline big w-100" type="button">
            {{ 'MODALS.OK' | translate }}
        </button>
    </div>
</mat-dialog-actions>
