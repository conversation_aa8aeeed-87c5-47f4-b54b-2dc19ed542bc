<form role="form" (ngSubmit)="submit()" [formGroup]="form">
    <h3 mat-dialog-title>
        {{ 'UPDATE_CUSTOM_ASSET.LABELS.LABEL1' | translate }}
    </h3>
    <mat-dialog-content>
        <div class="form__field mb-0">
            <label for="owner">
                {{ 'UPDATE_CUSTOM_ASSET.LABELS.LABEL2' | translate }}
                <span class="color-red">*</span>
            </label>
            <input
                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                [class.invalid]="form.controls.owner.touched && form.controls.owner.invalid"
                [placeholder]="'UPDATE_CUSTOM_ASSET.LABELS.LABEL2' | translate"
                autofocus
                class="form__field--input"
                formControlName="owner"
                id="owner"
                name="owner"
                type="text"
            />
            <div *ngIf="form.controls.owner.invalid && (form.controls.owner.touched || form.controls.owner.dirty)" class="error">
                <div *ngIf="form.controls.owner.hasError('required')">
                    {{ 'ERRORS.REQUIRED' | translate }}
                </div>
                <div *ngIf="form.controls.owner.hasError('hex_not_valid')">
                    {{ 'ERRORS.HEX_NOT_VALID' | translate }}
                </div>
                <div *ngIf="form.controls.owner.hasError('address_not_valid')">
                    {{ 'ERRORS.ADDRESS_NOT_VALID' | translate }}
                </div>
            </div>
        </div>
    </mat-dialog-content>

    <mat-dialog-actions>
        <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
            <button mat-dialog-close class="outline big w-100" type="button">
                {{ 'UPDATE_CUSTOM_ASSET.BUTTONS.BUTTON1' | translate }}
            </button>
            <button [disabled]="form.invalid" class="primary big w-100" type="submit">
                {{ 'UPDATE_CUSTOM_ASSET.BUTTONS.BUTTON2' | translate }}
            </button>
        </div>
    </mat-dialog-actions>
</form>
