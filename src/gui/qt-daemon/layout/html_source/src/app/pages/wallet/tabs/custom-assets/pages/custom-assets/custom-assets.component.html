<div [ngClass]="{ center: !assetInfoItems.length }" class="container">
    <ng-container *ngIf="assetInfoItems.length; else emptyTemplate">
        <div class="scrolled-content mb-2" fxFlex="1 1 auto">
            <table class="zano-table">
                <thead>
                    <tr>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL1' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL2' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL3' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL4' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL5' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">
                                {{ 'CUSTOM_ASSETS.TABLE.LABELS.LABEL6' | translate }}
                            </div>
                        </th>
                        <th scope="col">
                            <div class="bg title">&nbsp;</div>
                        </th>
                    </tr>
                    <div class="row-divider" aria-hidden="true"></div>
                </thead>
                <tbody>
                    <ng-container *ngFor="let assetInfo of assetInfoItems | paginate : paginateArgs; trackBy: trackByAssets">
                        <tr>
                            <td (contextmenu)="variablesService.onContextMenuOnlyCopy($event, assetInfo.asset_id)">
                                {{ assetInfo.asset_id | zanoShortString }}
                            </td>
                            <td>{{ assetInfo.ticker }}</td>
                            <td>{{ assetInfo.full_name }}</td>
                            <td>{{ assetInfo.total_max_supply | intToMoney : assetInfo.decimal_point }}</td>
                            <td>{{ assetInfo.current_supply | intToMoney : assetInfo.decimal_point }}</td>
                            <td>{{ assetInfo.decimal_point }}</td>
                            <td>
                                <div fxLayout="row" fxLayoutAlign="end center">
                                    <button
                                        (click)="$event.stopPropagation()"
                                        (keydown.enter)="$event.stopPropagation()"
                                        (keydown.space)="$event.stopPropagation()"
                                        [matMenuTriggerFor]="menu"
                                        aria-haspopup="menu"
                                        class="btn-icon circle row-options small"
                                        type="button"
                                    >
                                        <mat-icon class="small" svgIcon="zano-row-options"></mat-icon>
                                    </button>

                                    <mat-menu #menu="matMenu" class="zano-mat-menu">
                                        <button (click)="openDialog('assetDetails', assetInfo)" mat-menu-item type="button">
                                            <mat-icon svgIcon="zano-info-v2"></mat-icon>
                                            <span>{{ 'CUSTOM_ASSETS.DROP_MENU.LABELS.LABEL1' | translate }}</span>
                                        </button>
                                        <button (click)="openDialog('emit', assetInfo)" mat-menu-item type="button">
                                            <mat-icon svgIcon="zano-emit"></mat-icon>
                                            <span>{{ 'CUSTOM_ASSETS.DROP_MENU.LABELS.LABEL2' | translate }}</span>
                                        </button>
                                        <button [disabled]="true" (click)="openDialog('update', assetInfo)" mat-menu-item type="button">
                                            <mat-icon svgIcon="zano-update"></mat-icon>
                                            <span>{{ 'CUSTOM_ASSETS.DROP_MENU.LABELS.LABEL3' | translate }}</span>
                                        </button>
                                        <button (click)="openDialog('burn', assetInfo)" mat-menu-item type="button">
                                            <mat-icon svgIcon="zano-burn"></mat-icon>
                                            <span>{{ 'CUSTOM_ASSETS.DROP_MENU.LABELS.LABEL4' | translate }}</span>
                                        </button>
                                    </mat-menu>
                                </div>
                            </td>
                        </tr>
                        <tr class="row-divider" aria-hidden="true"></tr>
                    </ng-container>
                </tbody>
            </table>

            <a class="btn create-new-asset primary big" routerLink="/wallet/create-new-asset">{{
                'CUSTOM_ASSETS.BUTTONS.BUTTON1' | translate
            }}</a>
        </div>

        <pagination-template
            #p="paginationApi"
            (pageChange)="paginateArgs.currentPage = $event"
            *ngIf="isShowPagination"
            [id]="paginationId"
            class="ngx-pagination custom-pagination"
            tabindex="0"
            [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL1' | translate"
        >
            <button
                tabindex="0"
                (click)="p.previous()"
                [disabled]="p.isFirstPage()"
                [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL2' | translate"
                class="pagination-previous btn-icon circle small mr-0_5"
            >
                <mat-icon svgIcon="zano-arrow-left"></mat-icon>
            </button>

            <div *ngFor="let page of p.pages; trackBy: trackByPages" [class.current]="p.getCurrent() === page.value" class="mr-0_5">
                <a tabindex="0" (click)="p.setCurrent(page.value)" *ngIf="p.getCurrent() !== page.value">
                    <span>{{ page.label }}</span>
                </a>
                <div tabindex="0" *ngIf="p.getCurrent() === page.value">
                    <span>{{ page.label }}</span>
                </div>
            </div>

            <button
                [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL3' | translate"
                tabindex="0"
                (click)="p.next()"
                [disabled]="p.isLastPage()"
                class="pagination-next btn-icon circle small"
            >
                <mat-icon svgIcon="zano-arrow-right"></mat-icon>
            </button>
        </pagination-template>
    </ng-container>

    <ng-template #emptyTemplate>
        <p class="mb-2">{{ 'CUSTOM_ASSETS.TEXT.EMPTY' | translate }}</p>
        <a class="btn create-new-asset primary big" routerLink="/wallet/create-new-asset">{{
            'CUSTOM_ASSETS.BUTTONS.BUTTON1' | translate
        }}</a>
    </ng-template>
</div>
