<div class="history-wrap" fxFlexFill fxLayout="column">
    <div class="wrap-table scrolled-content mb-2" fxFlex="1 1 auto">
        <table class="zano-table history-table" [attr.aria-label]="'ACCESSIBILITY.HISTORY.LABELS.LABEL1' | translate">
            <thead>
                <tr>
                    <th scope="col">
                        <div class="bg title">{{ 'HISTORY.STATUS' | translate }}</div>
                    </th>
                    <th scope="col">
                        <div class="bg title">{{ 'HISTORY.AMOUNT' | translate }}</div>
                    </th>
                    <th scope="col">
                        <div class="bg title">{{ 'HISTORY.FEE' | translate }}</div>
                    </th>
                    <th scope="col">
                        <div class="bg title">{{ 'HISTORY.ADDRESS' | translate }}</div>
                    </th>
                    <th scope="col">
                        <div class="bg title">{{ 'HISTORY.DATE' | translate }}</div>
                    </th>
                </tr>
                <div class="row-divider"></div>
            </thead>
            <tbody>
                <ng-container *ngFor="let transaction of variablesService.current_wallet.history">
                    <tr
                        (click)="toggleTransactionDetails($event, transaction.tx_hash)"
                        (keydown.enter)="toggleTransactionDetails($event, transaction.tx_hash)"
                        (keydown.space)="toggleTransactionDetails($event, transaction.tx_hash)"
                        [class.locked-transaction]="!transaction.is_mining && transaction.unlock_time > 0"
                        tabindex="0"
                    >
                        <!-- Status -->
                        <td>
                            <app-transaction-status [transaction]="transaction"></app-transaction-status>
                        </td>

                        <!-- Amount -->
                        <td>
                            <ng-container *ngFor="let item of transaction | getAmountItems : currentWallet">
                                <div *appVisibilityBalance class="cell-amount" [attr.aria-label]="item.amount + ' ' + item.ticker">
                                    <div class="amount">{{ item.amount }}</div>
                                    <span class="ticker">{{ item.ticker }}</span>
                                </div>
                            </ng-container>
                        </td>

                        <!-- Fee -->
                        <td>
                            <div *ngIf="transaction | isVisibleFee" class="cell-fee">
                                <ng-container *ngIf="transaction?.subtransfers?.length">
                                    <ng-container *ngIf="transaction.fee; else noFeeTemplate">
                                        <mat-icon class="mr-1" style="opacity: 50" svgIcon="zano-fire"></mat-icon>

                                        {{ transaction.fee | intToMoney }}
                                        {{ variablesService.defaultTicker }}
                                    </ng-container>

                                    <ng-template #noFeeTemplate>
                                        <span>{{ 'HISTORY.NO_FEE' | translate }}</span>
                                    </ng-template>
                                </ng-container>
                            </div>
                        </td>

                        <!-- Address -->
                        <td class="remote-address">
                            <ng-container [ngSwitch]="true">
                                <!-- Message -->
                                <ng-container *ngSwitchCase="!(transaction.tx_type === 0)">
                                    <div class="text-ellipsis">
                                        {{ transaction | historyTypeMessages }}
                                    </div>
                                </ng-container>

                                <ng-container *ngSwitchCase="transaction.tx_type === 0">
                                    <!-- Address -->
                                    <ng-container
                                        *ngIf="transaction.remote_addresses && transaction.remote_addresses[0] &&
                                        !transaction.remote_aliases?.[0]?.trim()?.length"
                                    >
                                        <div class="text-ellipsis">
                                            <span
                                                (contextmenu)="
                                                    variablesService.onContextMenuOnlyCopy($event, transaction.remote_addresses[0])
                                                "
                                            >
                                                {{ transaction.remote_addresses[0] | zanoShortString }}
                                            </span>
                                        </div>
                                    </ng-container>

                                    <!-- Alias -->
                                    <ng-container *ngIf="transaction.remote_aliases?.[0]?.trim()?.length">
                                        <div fxLayout="row nowrap" fxLayoutAlign="start center">
                                            <ng-container *ngIf="transaction.remote_aliases as remote_aliases">
                                                <ng-container *ngIf="remote_aliases[0] as firstAlias">
                                                    <div
                                                        [class.available]="firstAlias.length >= 1 && firstAlias.length <= 5"
                                                        class="alias-container"
                                                    >
                                                        <!-- First Alias -->
                                                        <div
                                                            (contextmenu)="variablesService.onContextMenuOnlyCopy($event, '@' + firstAlias)"
                                                            class="alias"
                                                        >
                                                            {{ '@' + firstAlias }}
                                                        </div>
                                                    </div>

                                                    <!-- Other Aliases -->
                                                    <ng-container *ngIf="remote_aliases.length > 1">
                                                        <div
                                                            [delay]="150"
                                                            [placement]="'bottom'"
                                                            [timeout]="0"
                                                            [tooltipClass]="'table-tooltip'"
                                                            [tooltip]="remoteAliasListTemplate"
                                                            class="alias-history-count"
                                                        >
                                                            +{{ remote_aliases.length - 1 }}
                                                        </div>
                                                    </ng-container>

                                                    <ng-template #remoteAliasListTemplate>
                                                        <div class="alias-history-list">
                                                            <ng-container *ngFor="let alias of remote_aliases; let first = first">
                                                                <!-- Do not display the first alias as it is displayed in the banner -->
                                                                <ng-container *ngIf="!first">
                                                                    <p>{{ '@' + alias }}</p>
                                                                </ng-container>
                                                            </ng-container>
                                                        </div>
                                                    </ng-template>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                    </ng-container>
                                </ng-container>

                                <!-- Hidden -->
                                <ng-container
                                    *ngSwitchCase="
                                        !(transaction.remote_addresses?.length || transaction.remote_aliases?.length) &&
                                        transaction.tx_type === 0
                                    "
                                >
                                    {{ 'HISTORY.HIDDEN' | translate }}
                                </ng-container>
                            </ng-container>
                        </td>

                        <!-- Date -->
                        <td>
                            <div class="text-ellipsis">
                                {{ transaction.timestamp * 1000 | date : 'dd-MM-yyyy HH:mm' }}
                            </div>
                        </td>
                    </tr>

                    <div class="row-divider" aria-hidden="true"></div>

                    <tr>
                        <td [ngStyle]="{ padding: '0', 'border-radius': '0.8rem' }" colspan="5">
                            <app-transaction-details
                                *ngIf="transaction.tx_hash === opened_transaction_details"
                                @collapseOnLeave
                                @expandOnEnter
                                [transaction]="transaction"
                            ></app-transaction-details>
                        </td>
                    </tr>

                    <tr
                        *ngIf="transaction.tx_hash === opened_transaction_details as state"
                        [@collapseOnLeave]="{
                            value: state,
                            params: { duration: 400 }
                        }"
                        [@expandOnEnter]="{ value: state, params: { duration: 150 } }"
                        class="row-divider"
                        aria-hidden="true"
                    ></tr>
                </ng-container>
            </tbody>
        </table>
    </div>

    <div class="pagination-wrapper">
        <div class="pagination" fxLayout="row" fxLayoutAlign="space-between center">
            <div
                class="left"
                fxLayout="row"
                fxLayoutAlign=" center"
                [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL1' | translate"
                tabindex="0"
            >
                <button
                    [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL2' | translate"
                    tabindex="0"
                    (click)="setPage(variablesService.current_wallet.currentPage - 1)"
                    [disabled]="
                        variablesService.current_wallet.currentPage === 1 ||
                        variablesService.isCurrentWalletSync ||
                        !variablesService.isCurrentWalletLoaded
                    "
                    class="btn-icon circle small mr-1"
                    type="button"
                >
                    <mat-icon svgIcon="zano-arrow-left"></mat-icon>
                </button>

                <ng-container *ngIf="!mining">
                    <button
                        (click)="setPage(page)"
                        *ngFor="let page of variablesService.current_wallet.pages"
                        [class.color-primary]="variablesService.current_wallet.currentPage === page"
                        [disabled]="variablesService.isCurrentWalletSync || !variablesService.isCurrentWalletLoaded"
                        class="mr-0_5"
                        tabindex="0"
                        type="button"
                    >
                        {{ page }}
                    </button>
                </ng-container>

                <ng-container *ngIf="mining">
                    <button
                        (click)="setPage(variablesService.current_wallet.currentPage)"
                        [disabled]="stop_paginate || variablesService.isCurrentWalletSync || !variablesService.isCurrentWalletLoaded"
                        [ngClass]="{
                            'color-primary': variablesService.current_wallet.currentPage,
                            disabled: variablesService.isCurrentWalletSync || !variablesService.isCurrentWalletLoaded
                        }"
                        class="mr-0_5"
                        tabindex="0"
                        type="button"
                    >
                        {{ variablesService.current_wallet.currentPage }}
                    </button>
                </ng-container>

                <button
                    (click)="setPage(variablesService.current_wallet.currentPage + 1)"
                    [disabled]="stop_paginate || variablesService.isCurrentWalletSync || !variablesService.isCurrentWalletLoaded"
                    class="btn-icon circle small ml-0_5"
                    tabindex="0"
                    [attr.aria-label]="'ACCESSIBILITY.PAGINATION.LABELS.LABEL3' | translate"
                    type="button"
                >
                    <mat-icon svgIcon="zano-arrow-right"></mat-icon>
                </button>
            </div>
            <div class="right" fxLayout="row" fxLayoutAlign=" center">
                <span class="switch-text mr-2">Hide mining transactions</span>
                <app-switch
                    (emitChange)="toggleMiningTransactions()"
                    [disabled]="variablesService.isCurrentWalletSync || !variablesService.isCurrentWalletLoaded"
                    [value]="mining"
                ></app-switch>
            </div>
        </div>
    </div>
</div>
