<section class="container overflow-auto" fxFlexFill fxLayout="column" fxLayoutAlign="center center">
    <div class="wrap-qr mb-2">
        <img alt="qr-code" [src]="qrImageSrc" />
    </div>

    <div
        class="address border-radius-0_8-rem overflow-hidden pl-1 pr-0_5 pt-0_5 pb-0_5"
        fxFlex="0 0 auto"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        [attr.aria-label]="variablesService.current_wallet.address"
    >
        <span
            [matTooltip]="variablesService.current_wallet.address"
            matTooltipShowDelay="800"
            matTooltipClass="mat-tooltip-address"
            class="text-ellipsis mr-1"
            >{{ variablesService.current_wallet.address | zanoShortString : 9 : 9 }}</span
        >
        <app-copy-button [value]="variablesService.current_wallet.address"></app-copy-button>
    </div>
</section>
