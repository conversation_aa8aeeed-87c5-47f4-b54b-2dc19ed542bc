<ng-container
    *ngIf="{
        items: address_items$ | async,
        loading: loading_address_items$ | async,
        lowerCaseDisabled: lower_case_disabled$ | async
    } as vm"
>
    <fieldset class="form__field">
        <label for="address">
            {{ 'SEND.ADDRESS' | translate }}
            <span class="color-red">*</span>
        </label>

        <input
            (blur)="updateAddressErrorMessage()"
            (contextmenu)="variables_service.onContextMenu($event)"
            (paste)="pasteListenAddressField($event)"
            [formControl]="control_ref.controls.address"
            [lowerCaseDisabled]="vm.lowerCaseDisabled"
            [matAutocomplete]="auto"
            [placeholder]="'PLACEHOLDERS.ADDRESS_PLACEHOLDER' | translate"
            aria-describedby="address-error wrap-service-is-inactive"
            class="form__field--input"
            id="address"
            lowerCase
            matAutocompletePosition="auto"
            type="text"
        />

        <mat-autocomplete (opened)="openAutocomplete()" #auto="matAutocomplete" class="zano-autocomplete-panel">
            <ng-container *ngIf="!variables_service.is_remote_node">
                <mat-option *ngIf="vm.loading" class="loading" disabled>
                    <zano-loader class="mx-auto" style="display: block; width: fit-content"></zano-loader>
                </mat-option>

                <mat-option *ngIf="control_ref.controls.address.value.startsWith('@') && vm.items?.length === 0 && !vm.loading" disabled>
                    <span class="pl-1"> Not found aliases </span>
                </mat-option>
            </ng-container>

            <ng-container *ngIf="!vm.loading">
                <cdk-virtual-scroll-viewport [style.height.px]="5 * 40" itemSize="40">
                    <mat-option *cdkVirtualFor="let item of vm.items; trackBy: trackByFn" [value]="item">
                        <!-- Alias -->
                        <ng-container *ngIf="item.startsWith('@'); else itemAddressTemplate">
                            <div
                                [ngClass]="{
                                    available: item.length >= 1 && item.length <= 6,
                                    'pl-1': item.length > 6
                                }"
                                class="alias-container"
                            >
                                <div class="alias">
                                    {{ item }}
                                </div>
                            </div>
                        </ng-container>

                        <!-- Address -->
                        <ng-template #itemAddressTemplate>
                            <span class="pl-1">{{ item | zanoShortString }}</span>
                        </ng-template>
                    </mat-option>
                </cdk-virtual-scroll-viewport>
            </ng-container>
        </mat-autocomplete>

        <div *ngIf="control_ref.controls.address | isVisibleControlError" aria-live="assertive" id="address-error" class="error">
            {{ error_messages['address'] | translate }}
        </div>

        <ng-container *ngIf="control_ref.controls.is_visible_wrap_info.value">
            <ng-container *ngIf="variables_service.is_wrap_info_service_inactive$ | async">
                <div class="error" aria-live="assertive" id="wrap-service-is-inactive">
                    {{ 'ERRORS.WRAP_SERVICE_IS_INACTIVE' | translate }}
                </div>
            </ng-container>
        </ng-container>

        <div *ngIf="control_ref.controls.alias_address.value" class="info text-ellipsis">
            <span>{{ control_ref.controls.alias_address.value | zanoShortString }}</span>
        </div>
    </fieldset>
</ng-container>
