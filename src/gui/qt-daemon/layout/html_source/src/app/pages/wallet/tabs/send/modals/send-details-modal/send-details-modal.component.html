<div role="dialog" aria-modal="true" aria-labelledby="send-details-title" class="send-details-modal-wrapper" fxFlex="0 1 54rem">
    <div class="wrapper w-100" fxFlex fxLayout="column">
        <h3 class="title mb-2" fxFlex="0 0 auto" id="send-details-title">
            {{ 'SEND_DETAILS_MODAL.TITLE1' | translate }}
        </h3>

        <div class="content mb-2 overflow-x-hidden overflow-y-auto" fxFlex="1 1 auto" fxLayout="column">
            <div class="status mb-2" fxFlex="0 0 auto" fxLayout="column" fxLayoutAlign=" center">
                <div *ngIf="isSentSuccess" class="image">
                    <img alt="success" src="assets/icons/aqua/transaction_success.svg" />
                </div>

                <div *ngIf="isSentFailed" class="image">
                    <img alt="failed" class="image" src="assets/icons/red/transaction_failed.svg" />
                </div>

                <div *ngIf="!isSentSuccess && !isSentFailed" class="loader"></div>

                <p class="color-primary mt-2 mb-0_5" *ngIf="currentActionState$ | async as currentActionState">
                    {{
                        (currentActionState ? 'TOR_LIB_STATE' + '.' + currentActionState.status : 'TOR_LIB_STATE.STATE_INITIALIZING')
                            | translate
                    }}
                    {{ !isSentSuccess && !isSentFailed ? '...' : '' }}
                </p>

                <ng-container *ngIf="responseData$ | async as data">
                    <ng-container
                        *ngTemplateOutlet="
                            errorCodesTemplate;
                            context: {
                                error_code: data.error_code
                            }
                        "
                    ></ng-container>
                </ng-container>
            </div>

            <div class="details border-radius-0_8-rem overflow-hidden" fxFlex="0 0 auto" fxLayout="column">
                <div
                    (click)="isDetailsNotEmpty && toggleDetails()"
                    class="header overflow-hidden py-1 px-2 w-100 cursor-pointer"
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                    [attr.aria-expanded]="stateDetails$ | async"
                    [attr.aria-controls]="'details-list'"
                >
                    <p class="title text-ellipsis mr-2">
                        {{ 'SEND_DETAILS_MODAL.TITLE2' | translate }}
                    </p>
                    <button *ngIf="isDetailsNotEmpty" fxLayout="row" fxLayoutAlign="center center">
                        <mat-icon
                            svgIcon="zano-dropdown-arrow-down"
                            aria-hidden="true"
                            [ngClass]="{
                                'rotate-180': stateDetails$ | async
                            }"
                        ></mat-icon>
                    </button>
                </div>
                <div
                    [class.px-2]="stateDetails$ | async"
                    [class.py-1]="stateDetails$ | async"
                    [fxHide]="!(stateDetails$ | async)"
                    class="details-wrapper"
                    fxFlex="1 1 auto"
                    fxLayout="row"
                >
                    <ul role="list" #elDetailsList id="details-list" class="details-list scrolled-content">
                        <li
                            *ngFor="let action of currentActionStates$ | async; let last = last; trackBy: trackBy"
                            class="item mb-1 color-primary"
                            fxLayout="row nowrap"
                            fxLayoutAlign=" center"
                        >
                            <span class="text text-ellipsis mr-1"
                                >{{ 'TOR_LIB_STATE' + '.' + action?.status | translate
                                }}{{ last && !isSentSuccess && !isSentFailed ? '...' : '' }}</span
                            >
                            <ng-container *ngIf="!last">
                                <img *ngIf="isSuccess(action)" alt="success" class="image" src="assets/icons/blue/check_with_blue_bg.svg" />

                                <img *ngIf="isFailed(action)" alt="failed" class="image" src="assets/icons/red/transaction_failed.svg" />
                            </ng-container>

                            <ng-container *ngIf="last">
                                <img
                                    *ngIf="last && isSentSuccess"
                                    alt="success"
                                    class="image"
                                    src="assets/icons/blue/check_with_blue_bg.svg"
                                />

                                <img
                                    *ngIf="last && isSentFailed"
                                    alt="failed"
                                    class="image"
                                    src="assets/icons/red/transaction_failed.svg"
                                />
                            </ng-container>
                        </li>

                        <ng-container *ngIf="responseData$ | async as data">
                            <li class="item mb-1 color-primary" fxLayout="row nowrap" fxLayoutAlign=" center">
                                <span class="word-break-break-all cursor-pointer" (click)="openInBrowser(data.response_data.tx_hash)">
                                    tx id: {{ data.response_data.tx_hash || '---' }}
                                </span>
                                <app-copy-button *ngIf="data.response_data.tx_hash" [value]="data.response_data.tx_hash" class="ml-1">
                                </app-copy-button>
                            </li>
                            <li class="item mb-1 color-primary" fxLayout="row nowrap" fxLayoutAlign=" center">
                                <div class="word-break-break-all">
                                    tx size:
                                    {{ data.response_data.tx_blob_size }}
                                    bytes
                                </div>
                            </li>
                            <li *ngIf="data.error_code !== 'OK'" class="item">
                                <ng-container
                                    *ngTemplateOutlet="
                                        errorCodesTemplate;
                                        context: {
                                            prefix: 'Error:',
                                            error_code: data.error_code
                                        }
                                    "
                                ></ng-container>
                            </li>
                        </ng-container>
                    </ul>
                </div>
            </div>
        </div>
        <div class="controls" fxFlex="0 0 auto">
            <button (click)="event_close.emit(success)" [disabled]="!isSentSuccess && !isSentFailed" class="primary big w-100">
                {{ 'Ok' | translate }}
            </button>
        </div>
    </div>
</div>

<ng-template #errorCodesTemplate let-prefix="prefix" let-error_code="error_code">
    <ng-container [ngSwitch]="error_code">
        <ng-container *ngSwitchCase="'NOT_ENOUGH_MONEY'">
            <p class="color-red">{{ prefix }} {{ 'SEND.ERROR_CODES' + '.' + error_code | translate }}</p>
        </ng-container>
        <ng-container *ngSwitchCase="'OK'"></ng-container>
        <ng-container *ngSwitchDefault>
            <p class="color-red">{{ prefix }} {{ 'ERRORS' + '.' + error_code | translate }}</p>
        </ng-container>
    </ng-container>
</ng-template>
