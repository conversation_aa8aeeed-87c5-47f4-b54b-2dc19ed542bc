<div class="send-modal-wrapper" fxLayout="row" role="dialog" aria-modal="true" aria-labelledby="confirm-title">
    <div class="wrapper">
        <form role="form" (ngSubmit)="beforeSubmit()" [formGroup]="form" class="overflow-hidden" fxFlexFill fxLayout="column">
            <h3 class="title mb-2" fxFlex="0 0 auto" id="confirm-title">
                {{ 'CONFIRM.TITLE' | translate }}
            </h3>

            <div class="content w-100 overflow-x-hidden overflow-y-auto" fxFlex="1 1 auto">
                <div class="table-info mb-2">
                    <ng-container *ngFor="let destination of transfer_params.destinations; let first = first">
                        <ng-container *ngIf="!first">
                            <hr class="separator" />
                        </ng-container>

                        <div class="row">
                            <div class="label max-w-19-rem w-100">
                                {{ 'CONFIRM.MESSAGE.SEND' | translate }}
                            </div>
                            <div class="text">
                                {{ destination.amount }}
                                {{ (destination.asset_id | getAssetInfo)?.ticker || '***' }}
                            </div>
                        </div>

                        <hr class="separator" />

                        <div class="row">
                            <div class="label max-w-19-rem w-100">
                                {{ 'CONFIRM.MESSAGE.FROM' | translate }}
                            </div>
                            <div class="text">
                                {{ variablesService.current_wallet.address }}
                            </div>
                        </div>

                        <hr class="separator" />

                        <div class="row">
                            <div class="label max-w-19-rem w-100">
                                {{ 'CONFIRM.MESSAGE.TO' | translate }}
                            </div>
                            <div class="text">{{ destination.address }}</div>
                        </div>
                    </ng-container>

                    <ng-container *ngIf="!!transfer_params.comment">
                        <hr class="separator" />

                        <div class="row">
                            <div class="label max-w-19-rem w-100">
                                {{ 'CONFIRM.MESSAGE.COMMENT' | translate }}
                            </div>
                            <div class="text">{{ transfer_params.comment }}</div>
                        </div>
                    </ng-container>
                </div>

                <div *ngIf="variablesService.appPass" class="form__field fixed">
                    <label for="password">
                        {{ 'LOGIN.MASTER_PASS' | translate }}
                        <span class="color-red">*</span>
                    </label>
                    <input
                        (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                        [class.invalid]="form.touched && form.invalid"
                        [placeholder]="'PLACEHOLDERS.MASTER_PASS_PLACEHOLDER' | translate"
                        autofocus
                        appAutofocus
                        class="form__field--input"
                        formControlName="password"
                        aria-describedby="password-error"
                        id="password"
                        name="password"
                        type="password"
                    />
                    <div *ngIf="form.touched && form.invalid" aria-live="assertive" id="password-error" class="error">
                        <div *ngIf="form.hasError('passwordNotMatch') && !form.controls.password.hasError('required')">
                            {{ 'LOGIN.FORM_ERRORS.WRONG_PASSWORD' | translate }}
                        </div>
                        <div *ngIf="form.controls.password.hasError('required')">
                            {{ 'LOGIN.FORM_ERRORS.PASS_REQUIRED' | translate }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="controls w-100" fxFlex="0 0 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
                <button (click)="onClose()" class="outline big w-100" type="button">
                    {{ 'CONFIRM.BUTTON_CANCEL' | translate }}
                </button>
                <button class="primary big w-100" type="submit">
                    {{ 'CONFIRM.BUTTON_CONFIRM' | translate }}
                </button>
            </div>
        </form>
    </div>
</div>
