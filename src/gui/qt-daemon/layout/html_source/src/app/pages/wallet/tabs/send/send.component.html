<div class="container scrolled-content" fxFlex="1 1 auto">
    <form
        role="form"
        (ngSubmit)="beforeSubmit()"
        [formGroup]="form"
        class="form"
        fxFlex="0 1 50rem"
        fxLayout="column"
        fxLayoutAlign="start stretch"
    >
        <!-- Asset -->
        <zano-asset-field
            [balances]="variables_service.current_wallet.balances$ | async"
            [control_ref]="form.controls.asset_id"
            [is_visible_wrap_info]="is_visible_wrap_info"
        >
        </zano-asset-field>

        <!-- Destinations -->
        <ng-container *ngFor="let destinationControl of form.controls.destinations.controls; let index = index; let first = first">
            <!-- Address and Amount -->
            <div [formGroup]="destinationControl" class="form__field--row destination">
                <!-- Address -->
                <div>
                    <zano-address-field [control_ref]="destinationControl"></zano-address-field>
                </div>

                <!-- Amount -->
                <div>
                    <zano-amount-field [control_ref]="destinationControl" [price_info]="price_info$ | async"></zano-amount-field>

                    <ng-container *ngIf="!first">
                        <mat-icon
                            tabindex="0"
                            role="button"
                            (click)="removeDestination($event, index)"
                            (keydown.enter)="removeDestination($event, index)"
                            (keydown.space)="removeDestination($event, index)"
                            [attr.aria-label]="'ACCESSIBILITY.SEND.LABELS.LABEL1' | translate"
                            class="remove"
                            svgIcon="zano-close"
                        ></mat-icon>
                    </ng-container>
                </div>
            </div>

            <!-- Wrap information -->
            <ng-container *ngIf="destinationControl.controls.is_visible_wrap_info.value">
                <ng-container *ngIf="variables_service.wrap_info$ | async">
                    <div class="mb-2">
                        <ng-container *ngIf="destinationControl.hasError('wrap_info_invalid'); else zanoWrapInformationTemplate">
                            <div class="error">
                                {{ 'ERRORS.CANT_ESTIMATE_WRAP_OPERATION_COST' | translate }}
                            </div>
                        </ng-container>

                        <ng-template #zanoWrapInformationTemplate>
                            <zano-wrap-information
                                [amount]="destinationControl.controls.amount.value"
                                [asset_id]="destinationControl.controls.asset_id.value"
                                [hide_received_value]="destinationControl.controls.amount.invalid"
                                [is_currency_input_mode]="destinationControl.controls.is_currency_input_mode.value"
                                [price_info]="price_info$ | async"
                                [wrap_info]="variables_service.wrap_info$ | async"
                            ></zano-wrap-information>
                        </ng-template>
                    </div>
                </ng-container>
            </ng-container>
        </ng-container>

        <zano-add-another-destination-button (click)="addDestination()" class="mb-2"></zano-add-another-destination-button>

        <!-- Comment -->
        <zano-comment-field *ngIf="!form.controls.comment.disabled" [control_ref]="form.controls.comment"></zano-comment-field>

        <!-- Details -->
        <div class="details mb-2">
            <button
                (click)="is_visible_additional_options_state = !is_visible_additional_options_state"
                [class.border-radius-all]="!is_visible_additional_options_state"
                class="header"
                type="button"
            >
                <span>{{ 'SEND.DETAILS' | translate }}</span>
                <mat-icon
                    [ngClass]="{
                        'rotate-180': is_visible_additional_options_state
                    }"
                    class="ml-1"
                    svgIcon="zano-dropdown-arrow-down"
                ></mat-icon>
            </button>

            <div *ngIf="is_visible_additional_options_state" class="content">
                <!--TODO: Do not delete, may return later-->
                <!-- MIXIN -->
                <!--<zano-mixin-field [control_ref]="form.controls.mixin"></zano-mixin-field>-->

                <!-- FEE -->
                <zano-fee-field [control_ref]="form.controls.fee"></zano-fee-field>
                <!--</div>-->

                <!--TODO: Do not delete, may return later-->
                <!-- PUSH -->
                <!--<app-checkbox [label]="'SEND.INCLUDE_SENDER_ADDRESS' | translate" class="mt-1" formControlName="push_payer"></app-checkbox>-->

                <!--TODO: Do not delete, may return later-->
                <!-- HIDE -->
                <!--<app-checkbox [label]="'SEND.HIDE' | translate" class="mt-1" formControlName="hide_receiver"></app-checkbox>-->
            </div>
        </div>

        <button [disabled]="is_submit_disabled" class="primary big max-w-19-rem w-100" type="submit">
            {{ 'SEND.BUTTON' | translate }}
        </button>
    </form>
</div>

<app-send-modal
    (confirmed)="handleConfirmed($event)"
    *ngIf="is_send_modal_state"
    [price_info]="price_info$ | async"
    [transfer_params]="getTransferParams()"
></app-send-modal>

<app-send-details-modal
    (event_close)="handeCloseSendDetailsModal($event)"
    *ngIf="is_send_details_modal_state"
    [job_id]="job_id"
></app-send-details-modal>
