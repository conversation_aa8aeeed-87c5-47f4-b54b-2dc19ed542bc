<form role="form" aria-labelledby="confirm-dialog-title" [formGroup]="confirmForm">
    <h3 mat-dialog-title id="confirm-dialog-title">
        {{ 'CONFIRM.TITLE' | translate }}
    </h3>

    <mat-dialog-content>
        <div class="table-info mb-2">
            <div class="row">
                <div class="label max-w-19-rem w-100">
                    {{ 'CONFIRM_SWAP.FORM.TABLE.LABELS.LABEL1' | translate }}
                </div>
                <div class="text">
                    <ng-container *ngFor="let item of data.proposalDetails.to_initiator">
                        {{ item.amount | intToMoney : (item.asset_id | getAssetInfo)?.decimal_point }}
                        {{ (item.asset_id | getAssetInfo)?.ticker || '***' }}
                        <br />
                    </ng-container>
                </div>
            </div>

            <hr class="separator" />

            <div class="row">
                <div class="label max-w-19-rem w-100">
                    {{ 'CONFIRM_SWAP.FORM.TABLE.LABELS.LABEL2' | translate }}
                </div>
                <div class="text">
                    <ng-container *ngFor="let item of data.proposalDetails.to_finalizer">
                        {{ item.amount | intToMoney }}
                        {{ (item.asset_id | getAssetInfo)?.ticker || '***' }}
                        <br />
                    </ng-container>
                </div>
            </div>
        </div>

        <fieldset *ngIf="variablesService.appPass" class="form__field mb-0">
            <label for="password">
                {{ 'LOGIN.MASTER_PASS' | translate }}
                <span class="color-red">*</span>
            </label>
            <input
                (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                [class.invalid]="confirmForm.touched && confirmForm.invalid"
                [placeholder]="'PLACEHOLDERS.MASTER_PASS_PLACEHOLDER' | translate"
                aria-describedby="password-error"
                autofocus
                appAutofocus
                class="form__field--input"
                formControlName="password"
                id="password"
                name="password"
                type="password"
            />
            <div *ngIf="confirmForm.touched && confirmForm.invalid" aria-live="assertive" id="password-error" class="error">
                <div *ngIf="confirmForm.hasError('passwordNotMatch')">
                    {{ 'LOGIN.FORM_ERRORS.WRONG_PASSWORD' | translate }}
                </div>
                <div *ngIf="confirmForm.controls.password.hasError('required')">
                    {{ 'LOGIN.FORM_ERRORS.PASS_REQUIRED' | translate }}
                </div>
            </div>
        </fieldset>
    </mat-dialog-content>

    <mat-dialog-actions>
        <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
            <button [mat-dialog-close]="false" class="outline big w-100" type="button">
                {{ 'CONFIRM.BUTTON_CANCEL' | translate }}
            </button>
            <button [mat-dialog-close]="true" [disabled]="confirmForm.invalid" class="primary big w-100" type="submit">
                {{ 'CONFIRM.BUTTON_CONFIRM' | translate }}
            </button>
        </div>
    </mat-dialog-actions>
</form>
