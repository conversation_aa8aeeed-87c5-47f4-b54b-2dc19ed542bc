<div class="container scrolled-content">
    <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

    <form role="form" [formGroup]="form" class="form">
        <fieldset class="form__field textarea mb-2">
            <label for="swap-proposal-hex">
                {{ 'CONFIRM_SWAP.FORM.LABELS.LABEL1' | translate }}
            </label>
            <textarea
                [placeholder]="'CONFIRM_SWAP.FORM.PLACEHOLDERS.PLACEHOLDER1' | translate"
                aria-required="true"
                formControlName="hex_raw_proposal"
                id="swap-proposal-hex"
            ></textarea>
        </fieldset>

        <div *ngIf="proposalDetails" class="form__field mb-2">
            <label for="swap-proposal-hex">
                {{ 'CONFIRM_SWAP.FORM.LABELS.LABEL2' | translate }}
            </label>
            <div class="table-info">
                <div class="row">
                    <div class="label max-w-19-rem w-100">
                        {{ 'CONFIRM_SWAP.FORM.TABLE.LABELS.LABEL1' | translate }}
                    </div>

                    <div class="text">
                        <p *ngFor="let item of proposalDetails.to_initiator">
                            {{ item.amount | intToMoney : (item.asset_id | getAssetInfo)?.decimal_point }}
                            {{ (item.asset_id | getAssetInfo)?.ticker || '***' }}
                        </p>
                    </div>
                </div>

                <hr aria-hidden="true" class="separator" />

                <div class="row">
                    <div class="label max-w-19-rem w-100">
                        {{ 'CONFIRM_SWAP.FORM.TABLE.LABELS.LABEL2' | translate }}
                    </div>
                    <div class="text">
                        <p *ngFor="let item of proposalDetails.to_finalizer">
                            {{ item.amount | intToMoney : (item.asset_id | getAssetInfo)?.decimal_point }}
                            {{ (item.asset_id | getAssetInfo)?.ticker || '***' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="actions">
            <button (click)="swapConfirmMasterPasswordDialog()" [disabled]="!proposalDetails" class="btn primary big w-100" type="button">
                {{ 'CONFIRM_SWAP.FORM.BUTTONS.BUTTON1' | translate }}
            </button>

            <a class="btn outline big w-100" role="link" routerLink="/wallet/swap">
                {{ 'CONFIRM_SWAP.FORM.BUTTONS.BUTTON2' | translate }}
            </a>
        </div>

        <div *ngIf="errorRpc" aria-live="assertive" class="error mt-2">
            {{ 'ERRORS.INVALID_PROPOSAL' | translate }}
        </div>
    </form>
</div>
