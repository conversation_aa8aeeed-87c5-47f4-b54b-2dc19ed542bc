<div class="container scrolled-content">
    <app-breadcrumbs [items]="breadcrumbItems" class="mb-2"></app-breadcrumbs>

    <form role="form" (ngSubmit)="beforeSubmit()" [formGroup]="form" class="form">
        <!-- Sending -->
        <div class="form__row" formGroupName="sending">
            <fieldset class="form__field fixed">
                <label for="sending-amount">
                    {{ 'CREATE_SWAP.FORM.LABELS.LABEL1' | translate }}
                </label>
                <input
                    (contextmenu)="variablesService.onContextMenu($event)"
                    [decimalPoint]="sendingDecimalPoint$ | async"
                    [placeholder]="'CREATE_SWAP.FORM.PLACEHOLDERS.PLACEHOLDER1' | translate"
                    appInputValidate="money"
                    class="form__field--input"
                    formControlName="amount"
                    id="sending-amount"
                    aria-describedby="sending-amount-error"
                    type="text"
                />
                <div
                    *ngIf="isVisibleErrorByControl(form.controls.sending.controls.amount) || isVisibleErrorByForm(form.controls.sending)"
                    aria-live="assertive"
                    id="sending-amount-error"
                    class="error"
                >
                    <ng-container [ngSwitch]="true">
                        <ng-container *ngSwitchCase="form.controls.sending.controls.amount.hasError('zero')">
                            {{ 'SEND.FORM_ERRORS.AMOUNT_ZERO' | translate }}
                        </ng-container>
                        <ng-container
                            *ngSwitchCase="
                                form.controls.sending.controls.amount.hasError('required') &&
                                !form.controls.sending.hasError('assetHasNotBeenAddedToWallet')
                            "
                        >
                            {{ 'ERRORS.REQUIRED' | translate }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.sending.hasError('greater_max')">
                            {{ 'ERRORS.MAX' | translate : { max: form.controls.sending.errors['greater_max'].max } }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.sending.hasError('insufficientFunds')">
                            {{ form.controls.sending.errors['insufficientFunds'].errorText | translate }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.sending.hasError('assetHasNotBeenAddedToWallet')">
                            {{ form.controls.sending.errors['assetHasNotBeenAddedToWallet'].errorText | translate }}
                        </ng-container>
                    </ng-container>
                </div>
            </fieldset>

            <fieldset class="form__field fixed">
                <label for="select-sending-asset-id" [attr.aria-label]="'ACCESSIBILITY.CREATE_SWAP.LABELS.LABEL1' | translate">
                    &nbsp;
                </label>
                <ng-select
                    (change)="form.controls.sending.controls.amount.updateValueAndValidity()"
                    [bindValue]="'asset_id'"
                    [clearable]="false"
                    [items]="sendingAssetsInfo$ | async"
                    [searchable]="false"
                    class="custom-select with-circle"
                    formControlName="asset_id"
                    id="select-sending-asset-id"
                >
                    <ng-template let-item="item" ng-label-tmp ng-option-tmp>
                        <div [innerHTML]="item | getLogoByAssetInfo" class="token-logo"></div>
                        {{ item.full_name || '---' }}
                    </ng-template>
                </ng-select>
            </fieldset>
        </div>
        <!-- /Sending -->

        <div class="wrapper-reverse">
            <button
                [attr.aria-label]="'ACCESSIBILITY.CREATE_SWAP.LABELS.LABEL2' | translate"
                (click)="reverse()"
                [disabled]="currentWallet.balances.length === 1"
                class="revers"
                type="button"
            >
                <mat-icon class="rotate-90" svgIcon="zano-swap"></mat-icon>
            </button>
        </div>

        <!-- Receiving -->
        <div class="form__row" formGroupName="receiving">
            <fieldset class="form__field fixed">
                <label for="receiving-amount">
                    {{ 'CREATE_SWAP.FORM.LABELS.LABEL2' | translate }}
                </label>
                <input
                    [decimalPoint]="receivingDecimalPoint$ | async"
                    [placeholder]="'CREATE_SWAP.FORM.PLACEHOLDERS.PLACEHOLDER1' | translate"
                    appInputValidate="money"
                    class="form__field--input"
                    formControlName="amount"
                    id="receiving-amount"
                    type="text"
                    aria-describedby="receiving-amount-error"
                />
                <div
                    *ngIf="
                        isVisibleErrorByControl(form.controls.receiving.controls.amount) || isVisibleErrorByForm(form.controls.receiving)
                    "
                    aria-live="assertive"
                    id="receiving-amount-error"
                    class="error"
                >
                    <ng-container [ngSwitch]="true">
                        <ng-container
                            *ngSwitchCase="
                                form.controls.receiving.controls.amount.hasError('required') &&
                                !form.controls.receiving.hasError('assetHasNotBeenAddedToWallet')
                            "
                        >
                            {{ 'ERRORS.REQUIRED' | translate }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.receiving.controls.amount.hasError('zero')">
                            {{ 'SEND.FORM_ERRORS.AMOUNT_ZERO' | translate }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.receiving.hasError('greater_max')">
                            {{ 'ERRORS.MAX' | translate : { max: form.controls.receiving.errors['greater_max'].max } }}
                        </ng-container>
                        <ng-container *ngSwitchCase="form.controls.receiving.hasError('assetHasNotBeenAddedToWallet')">
                            {{ form.controls.receiving.errors['assetHasNotBeenAddedToWallet'].errorText | translate }}
                        </ng-container>
                    </ng-container>
                </div>
            </fieldset>

            <fieldset class="form__field fixed">
                <label for="select-receive-asset-id" [attr.aria-label]="'ACCESSIBILITY.CREATE_SWAP.LABELS.LABEL1' | translate">
                    &nbsp;
                </label>
                <ng-select
                    [bindValue]="'asset_id'"
                    [clearable]="false"
                    [items]="receivingAssetsInfo$ | async"
                    [searchable]="false"
                    class="custom-select with-circle"
                    formControlName="asset_id"
                    id="select-receive-asset-id"
                    aria-describedby="select-receive-asset-id-error"
                >
                    <ng-template let-item="item" ng-label-tmp ng-option-tmp>
                        <div [innerHTML]="item | getLogoByAssetInfo" class="token-logo"></div>
                        {{ item.full_name || '---' }}
                    </ng-template>
                </ng-select>
                <div
                    *ngIf="
                        form.controls.receiving.controls.asset_id.invalid &&
                        (form.controls.receiving.controls.asset_id.dirty ||
                            form.controls.receiving.controls.asset_id.touched ||
                            form.touched)
                    "
                    class="error"
                    aria-live="assertive"
                    id="select-receive-asset-id-error"
                >
                    <ng-container *ngIf="form.controls.receiving.controls.asset_id.hasError('sameAssetsId')">
                        {{ 'CREATE_SWAP.FORM.ERRORS.ERROR1' | translate }}
                    </ng-container>
                </div>
            </fieldset>
        </div>
        <!-- /Receiving -->

        <!-- Receiving Address -->
        <div
            *ngIf="{
                items: addressItems$ | async,
                loading: loadingAddressItems$ | async,
                lowerCaseDisabled: lowerCaseDisabled$ | async
            } as vm"
            class="form__field fixed"
        >
            <label for="address">
                {{ 'CREATE_SWAP.FORM.LABELS.LABEL3' | translate }}
                <span class="color-red">*</span>
            </label>

            <input
                (blur)="updateReceiverAddressErrorMessage()"
                (contextmenu)="variablesService.onContextMenu($event)"
                (paste)="pasteListenReceiverAddressField($event)"
                [lowerCaseDisabled]="vm.lowerCaseDisabled"
                [matAutocomplete]="auto"
                [placeholder]="'PLACEHOLDERS.ADDRESS_PLACEHOLDER' | translate"
                class="form__field--input"
                formControlName="receiverAddress"
                id="address"
                aria-describedby="address-error"
                lowerCase
                matAutocompletePosition="auto"
                type="text"
            />

            <mat-autocomplete (opened)="openAutocomplete()" #auto="matAutocomplete" class="zano-autocomplete-panel">
                <ng-container *ngIf="!variablesService.is_remote_node">
                    <mat-option *ngIf="vm.loading" class="loading" disabled>
                        <zano-loader class="mx-auto" style="display: block; width: fit-content"></zano-loader>
                    </mat-option>

                    <mat-option *ngIf="form.controls.receiverAddress.value[0] === '@' && vm.items?.length === 0 && !vm.loading" disabled>
                        <span class="pl-1"> Not found aliases </span>
                    </mat-option>
                </ng-container>

                <ng-container *ngIf="!vm.loading">
                    <cdk-virtual-scroll-viewport [style.height.px]="5 * 40" itemSize="40">
                        <mat-option *cdkVirtualFor="let item of vm.items; trackBy: trackByFn" [value]="item">
                            <!-- Alias -->
                            <ng-container *ngIf="item.startsWith('@'); else itemAddressTemplate">
                                <div
                                    [ngClass]="{
                                        available: item.length >= 1 && item.length <= 6,
                                        'pl-1': item.length > 6
                                    }"
                                    class="alias-container"
                                >
                                    <div class="alias">
                                        {{ item }}
                                    </div>
                                </div>
                            </ng-container>

                            <!-- Address -->
                            <ng-template #itemAddressTemplate>
                                <span class="pl-1">{{ item | zanoShortString }}</span>
                            </ng-template>
                        </mat-option>
                    </cdk-virtual-scroll-viewport>
                </ng-container>
            </mat-autocomplete>

            <div *ngIf="isVisibleErrorByControl(form.controls.receiverAddress)" aria-live="assertive" id="address-error" class="error">
                {{ errorMessages['receiverAddress'] | translate }}
            </div>

            <div *ngIf="aliasAddress" class="info text-ellipsis">
                <span>{{ aliasAddress | zanoShortString }}</span>
            </div>
        </div>
        <!-- /Address -->

        <div class="actions">
            <button [disabled]="form.invalid || (loading$ | async)" class="btn primary big w-100" type="submit">
                <ng-container *ngIf="!(loading$ | async)">{{ 'CREATE_SWAP.FORM.BUTTONS.BUTTON1' | translate }} </ng-container>
                <zano-loader *ngIf="loading$ | async" [type]="'circle'"></zano-loader>
            </button>
            <a class="btn outline big w-100" routerLink="/wallet/swap">{{ 'CREATE_SWAP.FORM.BUTTONS.BUTTON2' | translate }}</a>
        </div>

        <div *ngIf="errorRpc" aria-live="assertive" class="error mt-2">
            {{ 'ERRORS.INVALID_PROPOSAL' | translate }}
        </div>
    </form>
</div>
