<form role="form">
    <h4 aria-live="assertive" mat-dialog-title>
        {{ 'EXPORT_HISTORY.TITLE' | translate }}
    </h4>

    <mat-dialog-content style="overflow: visible">
        <fieldset class="form__field">
            <label for="select-format">
                {{ 'EXPORT_HISTORY.FORMAT' | translate }}
            </label>
            <ng-select
                [(ngModel)]="currentFormat"
                [clearable]="false"
                [items]="exportFormats"
                [searchable]="false"
                bindLabel="formatName"
                bindValue="format"
                name="format"
                autofocus="true"
                appAutofocus
                id="select-format"
            >
                <ng-template let-item="item" ng-label-tmp>
                    {{ item.formatName }}
                </ng-template>
                <ng-template let-index="index" let-item="item" ng-option-tmp>
                    {{ item.formatName }}
                </ng-template>
            </ng-select>
        </fieldset>

        <div fxLayout="row" fxLayoutAlign="start center">
            <span class="color-primary mr-1">{{ 'EXPORT_HISTORY.FILTER' | translate }}</span>
            <app-switch (emitChange)="posFilterIsOn = !posFilterIsOn" [value]="posFilterIsOn"></app-switch>
        </div>
    </mat-dialog-content>

    <mat-dialog-actions>
        <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
            <button (click)="confirmExport()" class="primary big w-100" type="button">
                {{ 'EXPORT_HISTORY.EXPORT' | translate }}
            </button>
            <button class="outline big w-100" mat-dialog-close type="button" appAutofocus cdkFocusInitial>
                {{ 'EXPORT_HISTORY.CANCEL' | translate }}
            </button>
        </div>
    </mat-dialog-actions>
</form>
