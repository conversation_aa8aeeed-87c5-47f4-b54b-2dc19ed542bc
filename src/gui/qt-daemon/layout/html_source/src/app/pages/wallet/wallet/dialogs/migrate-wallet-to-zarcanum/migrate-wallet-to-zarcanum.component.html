<h3 aria-live="assertive" mat-dialog-title>
    {{ 'MIGRATE_WALLET_TO_ZARCANUM.TITLE' | translate }}
</h3>

<mat-dialog-content>
    <div class="details">
        <p class="mb-2 mt-2">
            {{
                'MIGRATE_WALLET_TO_ZARCANUM.TEXT1'
                    | translate
                        : {
                              total_bare_outs: data.total_bare_outs,
                              total_amount: data.total_amount | intToMoney
                          }
            }}
        </p>
        <p
            (click)="openZarcanumMigration($event)"
            (keydown.enter)="openZarcanumMigration($event)"
            (keydown.space)="openZarcanumMigration($event)"
            class="text-align-center cursor-pointer mb-2"
            fxLayout="row"
            fxLayoutAlign="start center"
            role="link"
            tabindex="0"
            [attr.aria-label]="'MIGRATE_WALLET_TO_ZARCANUM.LINK1' | translate"
        >
            <mat-icon class="mr-0_5" svgIcon="zano-question"></mat-icon>
            <span class="color-primary">{{ 'MIGRATE_WALLET_TO_ZARCANUM.LINK1' | translate }}</span>
        </p>
        <hr aria-hidden="true" class="mb-2" />
        <p class="mb-2">
            {{
                'MIGRATE_WALLET_TO_ZARCANUM.TEXT2'
                    | translate
                        : {
                              txs_count: data.txs_count,
                              expected_total_fee: data.expected_total_fee | intToMoney
                          }
            }}
        </p>
    </div>
</mat-dialog-content>

<mat-dialog-actions>
    <div fxFlex="1 1 auto" fxLayout="row nowrap" fxLayoutGap="1rem">
        <button (click)="migrate()" class="primary big w-100" type="button">
            {{ 'MIGRATE_WALLET_TO_ZARCANUM.BUTTON1' | translate }}
        </button>
    </div>
</mat-dialog-actions>
