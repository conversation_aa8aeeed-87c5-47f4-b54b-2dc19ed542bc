<h3 aria-live="assertive" mat-dialog-title>
    {{ 'MY_ALIASES_DIALOG.TITLE' | translate }}
</h3>

<mat-dialog-content>
    <div class="my-aliases-list">
        <ng-container *ngFor="let alias_info of variablesService.current_wallet.alias_info_list">
            <div class="my-aliases-list-item">
                <div class="alias" [attr.aria-label]="'@' + alias_info.alias">
                    <div class="name">@{{ alias_info.alias }}</div>
                    <ng-container *ngIf="alias_info.alias.length >= 1 && alias_info.alias.length <= 5">
                        <mat-icon svgIcon="zano-crown"></mat-icon>
                    </ng-container>
                </div>

                <ng-container *ngIf="isShowAliasButtons">
                    <div class="controls">
                        <button
                            [routerLink]="['/edit-alias']"
                            [state]="{ alias_info: alias_info }"
                            role="link"
                            class="btn-icon circle small mr-1"
                            matDialogClose
                            [matTooltip]="'WALLET.TOOLTIPS.EDIT_ALIAS' | translate"
                            matTooltipShowDelay="800"
                            type="button"
                            [attr.aria-label]="'ACCESSIBILITY.MY_ALIASES_DIALOG.LABELS.LABEL1' | translate"
                        >
                            <mat-icon svgIcon="zano-edit"></mat-icon>
                        </button>

                        <button
                            [routerLink]="['/transfer-alias']"
                            [state]="{ alias_info: alias_info }"
                            class="btn-icon circle small"
                            matDialogClose
                            role="link"
                            [matTooltip]="'WALLET.TOOLTIPS.TRANSFER_ALIAS' | translate"
                            matTooltipShowDelay="800"
                            type="button"
                            [attr.aria-label]="'ACCESSIBILITY.MY_ALIASES_DIALOG.LABELS.LABEL2' | translate"
                        >
                            <mat-icon svgIcon="zano-send"></mat-icon>
                        </button>
                    </div>
                </ng-container>
            </div>
        </ng-container>
    </div>
</mat-dialog-content>

<mat-dialog-actions>
    <button class="outline big w-100" matDialogClose cdkFocusInitial appAutofocus>
        {{ 'MY_ALIASES_DIALOG.BUTTON1' | translate }}
    </button>
</mat-dialog-actions>
