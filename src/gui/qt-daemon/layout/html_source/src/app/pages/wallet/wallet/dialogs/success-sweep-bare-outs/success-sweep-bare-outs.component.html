<h3 aria-live="assertive" mat-dialog-title>
    {{ 'SEND_DETAILS_MODAL.TITLE1' | translate }}
</h3>

<mat-dialog-content fxLayout="column">
    <div class="status mb-2" fxFlex="0 0 auto" fxLayout="column" fxLayoutAlign="center center">
        <div class="image">
            <img alt="Transaction Success" src="assets/icons/aqua/transaction_success.svg" />
        </div>

        <p aria-live="polite" class="color-primary mt-2">
            {{ 'TOR_LIB_STATE.STATE_SENT_SUCCESS' | translate }}
        </p>
    </div>

    <div class="details border-radius-0_8-rem overflow-hidden" fxFlex="0 0 auto" fxLayout="column">
        <div
            (click)="toggleDetails($event)"
            (keydown.enter)="toggleDetails($event)"
            (keydown.space)="toggleDetails($event)"
            [attr.aria-controls]="'details-content'"
            [attr.aria-expanded]="stateDetails"
            [ngStyle]="{ 'border-radius': stateDetails ? '0.8rem 0.8rem 0 0' : '0.8rem' }"
            class="header overflow-hidden py-1 px-2 w-100 cursor-pointer"
            fxLayout="row"
            fxLayoutAlign="space-between center"
            role="button"
            tabindex="0"
        >
            <p class="title text-ellipsis mr-2">
                {{ 'SEND_DETAILS_MODAL.TITLE2' | translate }}
            </p>
            <button fxLayout="row" fxLayoutAlign="center center">
                <mat-icon
                    [ngClass]="{
                        'rotate-180': stateDetails
                    }"
                    svgIcon="zano-dropdown-arrow-down"
                ></mat-icon>
            </button>
        </div>
        <div
            [class.px-2]="stateDetails"
            [class.py-1]="stateDetails"
            [fxHide]="!stateDetails"
            [ngStyle]="{ 'border-radius': stateDetails ? '0 0 0.8rem 0.8rem ' : '0' }"
            class="details-wrapper"
            fxFlex="1 1 auto"
            fxLayout="row"
            id="details-content"
        >
            <ul class="details-list scrolled-content">
                <li aria-live="polite" class="item mb-1" fxLayout="row nowrap" fxLayoutAlign=" center">
                    <p>
                        {{
                            'SUCCESS_SWEEP_BARE_OUTS.DETAILS'
                                | translate
                                    : {
                                          txs_sent: data.txs_sent,
                                          bare_outs_swept: data.bare_outs_swept,
                                          amount_swept: data.amount_swept | intToMoney,
                                          fee_spent: data.fee_spent | intToMoney
                                      }
                        }}
                    </p>
                </li>
            </ul>
        </div>
    </div>
</mat-dialog-content>

<mat-dialog-actions>
    <div fxFlex="1 1 auto">
        <button class="primary big w-100" mat-dialog-close>
            {{ 'Ok' | translate }}
        </button>
    </div>
</mat-dialog-actions>
