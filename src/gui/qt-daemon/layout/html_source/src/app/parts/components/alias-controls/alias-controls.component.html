<div class="controls" fxFlex="0 0 auto" fxLayout="row" fxLayoutAlign="start center">
    <ng-container *ngIf="isShowAssignAlias">
        <button [routerLink]="['/assign-alias']" class="px-1 py-0_8 btn-light-background" role="link" type="button">
            {{ 'WALLET.REGISTER_ALIAS' | translate }}
        </button>
    </ng-container>

    <ng-container *ngIf="isShowAlias">
        <button
            (click)="openMyAliasesDialog()"
            [class.available]="variablesService.current_wallet.alias_info | isAvailableAliasName"
            class="control-alias-wallet-container"
            type="button"
            aria-haspopup="true"
            [attr.aria-label]="'@' + variablesService.current_wallet.alias_info?.alias"
        >
            <div class="alias">
                @{{ variablesService.current_wallet.alias_info?.alias }}
                <mat-icon svgIcon="zano-circle-arrow-right"></mat-icon>
            </div>

            <ng-container *ngIf="variablesService.current_wallet.alias_info_list.length > 1">
                <div
                    [delay]="150"
                    [placement]="'bottom'"
                    [timeout]="0"
                    [tooltipClass]="'table-tooltip'"
                    [tooltip]="aliasHistoryTemplate"
                    class="alias-history-count"
                >
                    +{{ variablesService.current_wallet.alias_info_list.length - 1 }}
                </div>

                <ng-template #aliasHistoryTemplate>
                    <div class="alias-history-list">
                        <ng-container *ngFor="let alias_info of variablesService.current_wallet.alias_info_list; let last = last">
                            <ng-container *ngIf="!last">
                                <p>
                                    {{ alias_info.alias }}
                                </p>
                            </ng-container>
                        </ng-container>
                    </div>
                </ng-template>
            </ng-container>
        </button>
    </ng-container>
</div>
