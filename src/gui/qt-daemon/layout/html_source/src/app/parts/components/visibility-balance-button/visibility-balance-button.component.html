<button
    (click)="variablesService.visibilityBalance$.next(!variablesService.visibilityBalance$.value)"
    [matTooltipShowDelay]="800"
    [matTooltip]="
        ((variablesService.visibilityBalance$ | async) ? 'WALLET.TOOLTIPS.HIDE_BALANCE' : 'WALLET.TOOLTIPS.SHOW_BALANCE') | translate
    "
    class="btn-icon circle big"
    type="button"
>
    <mat-icon aria-hidden="true" [svgIcon]="(variablesService.visibilityBalance$ | async) ? 'zano-hide-balance' : 'zano-show-balance'">
    </mat-icon>
</button>
