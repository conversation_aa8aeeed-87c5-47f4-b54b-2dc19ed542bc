<h2 aria-live="assertive" mat-dialog-title>{{ data?.title ?? 'ASSETS.MODALS.ASSET_DETAILS.TITLE' | translate }}</h2>

<mat-dialog-content>
    <ng-container *ngIf="data.asset_info as asset_info; else templateEmpty">
        <table class="rounded-corners">
            <tbody>
                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.NAME' | translate }}</td>
                    <td>{{ asset_info.full_name }}</td>
                </tr>
                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.TICKER' | translate }}</td>
                    <td>{{ asset_info.ticker }}</td>
                </tr>
                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.OWNER' | translate }}</td>
                    <td (contextmenu)="variablesService.onContextMenuOnlyCopy($event, asset_info.owner)">
                        {{ asset_info.owner }}
                    </td>
                </tr>
                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.ID' | translate }}</td>
                    <td (contextmenu)="variablesService.onContextMenuOnlyCopy($event, asset_info.asset_id)">
                        {{ asset_info.asset_id }}
                    </td>
                </tr>
                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.CURRENT_SUPPLY' | translate }}</td>
                    <td>
                        <ng-container *ngIf="asset_info.asset_id !== zanoAssetInfo.asset_id; else zanoCurrentSupply">
                            {{ asset_info.current_supply | intToMoney : asset_info.decimal_point }}
                        </ng-container>

                        <ng-template #zanoCurrentSupply>
                            <ng-container *ngIf="variablesService.zano_current_supply !== undefined; else emptyCurrentSupply">
                                {{ variablesService.zano_current_supply | intToMoney : asset_info.decimal_point }}
                            </ng-container>
                        </ng-template>

                        <ng-template #emptyCurrentSupply> Unknown </ng-template>
                    </td>
                </tr>

                <tr>
                    <td>{{ 'ASSETS.MODALS.ASSET_DETAILS.LABELS.MAX_SUPPLE' | translate }}</td>
                    <td>
                        {{
                            asset_info.asset_id === zanoAssetInfo.asset_id
                                ? 'Uncapped'
                                : (asset_info.total_max_supply | intToMoney : asset_info.decimal_point)
                        }}
                    </td>
                </tr>
            </tbody>
        </table>
    </ng-container>

    <ng-template #templateEmpty>No data</ng-template>
</mat-dialog-content>

<mat-dialog-actions>
    <button class="outline big w-100" mat-dialog-close type="button" appAutofocus cdkFocusInitial>
        {{ 'MODALS.OK' | translate }}
    </button>
</mat-dialog-actions>
