.asset-details-wrapper {
    padding: 2rem;
    border-radius: 0.8rem;
    max-height: 90vh;

    background: var(--dialog-background);
}

table {
    td {
        padding: 2rem;
    }

    tr {
        td:nth-child(1) {
            width: 17rem;
            color: var(--azure-500);
        }

        td:nth-child(2) {
            word-break: break-all;
        }
    }
}

.light {
    :host {
        table {
            tr {
                td:nth-child(1) {
                    background: #f0f6fb;
                }
            }
        }
    }
}

.dark {
    :host {
        table {
            tr {
                td:nth-child(1) {
                    background: var(--blue-500);
                }
            }
        }
    }
}
