<div class="modal open-wallet-modal-wrapper scrolled-content">
    <div class="wrapper w-100">
        <h3 class="mb-2">{{ 'OPEN_WALLET.MODAL.TITLE' | translate }}</h3>

        <div class="word-break-break-all mb-2">{{ wallet.name }}</div>
        <div class="word-break-break-all mb-2">{{ wallet.path }}</div>

        <form role="form" (ngSubmit)="openWallet()" class="form" fxLayout="column">
            <div *ngIf="!wallet.notFound" class="form__field">
                <label for="password">{{ 'OPEN_WALLET.MODAL.LABEL' | translate }}</label>
                <input
                    [(ngModel)]="wallet.pass"
                    [class.invalid]="isWrongPassword$ | async"
                    (contextmenu)="variablesService.onContextMenuPasteSelect($event)"
                    (focusin)="resetPasswordError()"
                    (keydown)="resetPasswordError()"
                    class="form__field--input"
                    id="password"
                    name="password"
                    type="password"
                />
                <div *ngIf="wallet.notFound" class="error">
                    {{ 'OPEN_WALLET.MODAL.NOT_FOUND' | translate }}
                </div>
                <div *ngIf="isWrongPassword$ | async" class="error">
                    {{ 'ERRORS.WRONG_PASSWORD' | translate }}
                </div>
            </div>

            <div fxLayout="row nowrap" fxLayoutGap="1rem">
                <button [disabled]="isFormInvalid()" class="primary big w-100" type="submit">
                    {{ 'OPEN_WALLET.MODAL.OPEN' | translate }}
                </button>
                <button (click)="skipWallet()" class="outline big w-100" type="button">
                    {{ 'OPEN_WALLET.MODAL.SKIP' | translate }}
                </button>
            </div>
        </form>
    </div>
</div>
