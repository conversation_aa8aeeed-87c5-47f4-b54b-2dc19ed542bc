import { Pipe, PipeTransform } from '@angular/core';
import { AliasInfo } from '@api/models/alias.model';

@Pipe({
    name: 'isAvailableAliasName',
})
export class IsAvailableAliasNamePipe implements PipeTransform {
    transform(alias: AliasInfo | null | undefined): boolean {
        return (<PERSON><PERSON><PERSON>(alias) && <PERSON><PERSON><PERSON>(alias.alias) && alias.alias.length >= 1 && alias.alias.length <= 5) || false;
    }
}
