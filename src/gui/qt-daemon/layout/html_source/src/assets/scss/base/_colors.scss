:root {
    // red
    --red-600: rgba(255, 103, 103, 0.75);
    --red-500: #ff6767;
    --red-100: #ffcbcb;

    // blue
    --blue-900: #0c0c3a;
    --blue-800: #0c1243;
    --blue-700: #0f2055;
    --blue-500: #11316b;
    --blue-450: #144182;
    --blue-400: rgba(31, 143, 235, 0.3);
    --blue-300: rgba(31, 143, 235, 0.15);

    // orange
    --orange-500: #ff6f00;

    // gray
    --gray-900: rgba(255, 255, 255, 0.1);
    --gray-800: rgba(255, 255, 255, 0.2);
    --gray-700: rgba(255, 255, 255, 0.3);
    --gray-600: rgba(255, 255, 255, 0.4);
    --gray-500: rgba(255, 255, 255, 0.5);
    --gray-400: rgba(255, 255, 255, 0.75);

    // aqua
    --aqua-500: #16d1d6;

    // azure
    --azure-600: #1c72b9;
    --azure-500: #1f8feb;

    // white
    --white-500: #ffffff;

    // black
    --black-300: rgba(0, 0, 0, 0.6);

    // amethyst
    --amethyst-500: #9a69f7;

    // silver
    --silver-500: #8898b5;

    // gradient
    --gradietAquaToBlue: radial-gradient(100% 188.88% at 0% 0%, #16d1d6 0%, #274cff 100%);

    --gradietLightAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, rgba(163, 102, 255, 0.5) 0%, rgba(96, 31, 255, 0.5) 100%);
    --gradietAmethystToPurpurle: radial-gradient(100% 246.57% at 0% 0%, #a366ff 0%, #601fff 100%);

    // shadow
    --shadow-gray: 0px 2.11765px 5.64706px rgba(0, 0, 0, 0.15), 0px 2.11765px 0.705882px rgba(0, 0, 0, 0.06);
    --shadow-black-300: 0 0 1rem var(--black-300);

    // chart
    --chartOptionsBackgroundColor: #2b3644;
    --chartOptionsHoverColor: #556576;
}
