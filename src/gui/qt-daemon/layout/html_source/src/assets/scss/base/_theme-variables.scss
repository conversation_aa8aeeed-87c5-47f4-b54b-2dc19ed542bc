:root {
    --border-radius: 0.8rem;
}

.light {
    --main-background: #f0f6fb;
    --page-content-background: #fcfcfc;
    --sidebar-background: #fcfcfc;
    --tab-content-background: #fcfcfc;
    --tab-preloader-background: #fcfcfc;
    --tab-preloader-text: var(--blue-900);
    --tab-header-background: #d9ebfa;
    --tab-header-active-background: #fcfcfc;
    --main-text: var(--blue-900);
    --button-color: var(--blue-900);
    --synchronization-status-color: var(--blue-900);
    --synchronization-progress-bar-container-background: rgba(0, 0, 0, 0.1);
    --dialog-background: #ffffff;
    --block-sync: #505274;
    --control-alias-wallet-background: #1f8feb1a;
    --control-alias-wallet-text: var(--main-text);
    --alias-history-count-text: #1f8feb;

    // sidebar
    --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);
    --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(252, 252, 252, 0) 0%, #fcfcfc 100%);

    // auth
    --auth-card-background: #fcfcfc;
    --auth-card-form-background: #ffffff;
    --auth-card-form-border: 1px solid #1f8feb33;

    // form
    --form-card-background: #f0f6fb;

    // ng-select
    --ng-select-bg: #dbecfa;
    --ng-select-border: #1f8feb40;
    --ng-select-highlight: #1f8feb10;
    --ng-select-circle-border: 1px solid #1f8feb50;
    --ng-select-circle-background: #1f8feb;

    // field
    --input-background: #ffffff;
    --input-color: var(--blue-900);
    --input-placeholder: #0c0c3a4d;
    --border: 1px solid #1f8feb66;
    --border-not-empty: 1px solid #1f8feb99;
    --border-disabled: 1px solid #1f8feb66;
    --border-error: 1px solid var(--red-600);
    --hint-text: #0c0c3a99;

    // field amount
    --amount-ticker-text: #0c0c3a99;
    --amount-btn-revers-background: #1f8feb1a;

    // checkbox
    --checkbox-border: 1px solid #1f8feb66;
    --checkbox-hover-border: 1px solid #1f8feb99;
    --checkbox-active-border: 1px solid #1f8feb99;
    --checkbox-checked-background: #1f8feb;

    // switch
    --switch-on-background: var(--aqua-500);
    --switch-off-background: #0c0c3a1a;
    --switch-circle-background: var(--white-500);

    //wallet
    --wallet-background: #1f8feb26;
    --wallet-active-background: var(--gradietAquaToBlue);
    --wallet-border: 1px solid #1f8feb00;
    --wallet-border-color-hover: #1f8feb;
    --wallet-auditable-watch-only-background: radial-gradient(
        100% 246.57% at 0% 0%,
        rgba(163, 102, 255, 0.75) 0%,
        rgba(96, 31, 255, 0.75) 100%
    );
    --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);
    --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);
    --wallet-watch-only-after-background: #dbecf9;
    --wallet-watch-only-text: var(--blue-900);
    --wallet-active-text: #ffffff;
    --wallet-text: var(--blue-900);

    // table
    --table-thead-bg: #1f8feb4d;
    --table-row-bg: #dbecf9;
    --table-rounded-corners-border: 1px solid #d9dfe8;

    --table-info-border: 1px solid #d9dfe8;
    --table-info-label-background: #f0f6fb;

    // buttons
    --btn-icon-background: #1f8feb1a;
    --btn-icon-hover-background: #1f8feb4d;

    // list
    --list-background: #f9fcff;
    --list-border: 1px solid #1f8feb80;
    --list-item-hover-background: #1f8feb1a;

    // tooltip
    --tooltip-background: #8dbee8;

    // details
    --details-background: #dbecfa;
}
.dark {
    --main-background: var(--blue-900);
    --page-content-background: var(--blue-700);
    --sidebar-background: var(--blue-700);
    --tab-content-background: var(--blue-700);
    --tab-preloader-background: var(--blue-700);
    --tab-preloader-text: #ffffff;
    --tab-header-background: var(--blue-800);
    --tab-header-active-background: var(--blue-700);
    --main-text: var(--white-500);
    --button-color: var(--white-500);
    --synchronization-status-color: var(--white-500);
    --synchronization-progress-bar-container-background: var(--gray-900);
    --dialog-background: var(--blue-700);
    --block-sync: #a8abb5;
    --control-alias-wallet-background: #ffffff1a;
    --control-alias-wallet-text: var(--main-text);
    --alias-history-count-text: #1f8feb;

    // sidebar
    --gradient-sidebar-content-wallet-list-top: linear-gradient(0deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);
    --gradient-sidebar-content-wallet-list-bottom: linear-gradient(180deg, rgba(15, 32, 85, 0) 0%, #0f2055 100%);

    // auth
    --auth-card-background: var(--blue-700);
    --auth-card-form-background: var(--blue-500);
    --auth-card-form-border: 1px solid transparent;

    //form
    --form-card-background: var(--blue-500);

    //ng-select
    --ng-select-bg: var(--blue-500);
    --ng-select-border: var(--gray-500);
    --ng-select-highlight: var(--gray-900);
    --ng-select-circle-border: 1px solid white;
    --ng-select-circle-background: var(--white-500);

    // field
    --input-background: transparent;
    --input-color: var(--white-500);
    --input-placeholder: var(--gray-800);
    --border: 1px solid var(--gray-800);
    --border-not-empty: 1px solid var(--gray-500);
    --border-disabled: 1px solid var(--gray-800);
    --border-error: 1px solid var(--red-600);
    --hint-text: #ffffff60;

    // field amount
    --amount-ticker-text: #ffffff60;
    --amount-btn-revers-background: #ffffff1a;

    // checkbox
    --checkbox-border: 1px solid #ffffff33;
    --checkbox-hover-border: 1px solid #ffffff66;
    --checkbox-active-border: 1px solid #ffffff66;
    --checkbox-checked-background: #ffffff;

    // switch
    --switch-on-background: var(--aqua-500);
    --switch-off-background: var(--gray-800);
    --switch-circle-background: var(--white-500);

    //wallet
    --wallet-background: var(--blue-500);
    --wallet-active-background: var(--gradietAquaToBlue);
    --wallet-border: 1px solid var(--blue-500);
    --wallet-border-color-hover: var(--gray-800);
    --wallet-auditable-watch-only-background: var(--gradietLightAmethystToPurpurle);
    --wallet-auditable-active-background: var(--gradietAmethystToPurpurle);
    --wallet-watch-only-active-background: var(--gradietAmethystToPurpurle);
    --wallet-watch-only-after-background: var(--blue-500);
    --wallet-watch-only-text: #ffffff;
    --wallet-active-text: #ffffff;
    --wallet-text: #ffffff;

    // table
    --table-thead-bg: var(--blue-400);
    --table-row-bg: var(--blue-300);
    --table-rounded-corners-border: 1px solid #33426e;

    --table-info-border: 1px solid #33426e;
    --table-info-label-background: var(--blue-500);

    // buttons
    --btn-icon-background: var(--gray-900);
    --btn-icon-hover-background: var(--gray-700);

    // list
    --list-background: var(--blue-500);
    --list-border: var(--border);
    --list-item-hover-background: var(--gray-900);

    // tooltip
    --tooltip-background: var(--blue-450);

    // details
    --details-background: var(--blue-500);
}
