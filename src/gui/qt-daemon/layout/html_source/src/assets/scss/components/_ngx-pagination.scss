.ngx-pagination {
    a {
        min-width: 29px;
        cursor: pointer;
        color: var(--main-text) !important;
        &:hover {
            background: transparent !important;
        }
    }

    .current {
        background: transparent !important;
        color: var(--azure-500) !important;
    }

    .pagination-next,
    .pagination-previous {
        background-color: var(--btn-icon-background);
        transition: background-color 0.2s ease-in-out;
        border-radius: 999px;

        &:hover {
            background-color: var(--btn-icon-hover-background);
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}

.custom-pagination {
    display: flex;
    align-items: center;
    min-height: 2.8rem;
}
