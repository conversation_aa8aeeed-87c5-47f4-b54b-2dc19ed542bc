.color-red {
    color: var(--red-500);
}

.color-primary {
    color: var(--azure-500);
}

.color-aqua {
    color: var(--aqua-500);
}

.border-radius-0_8-rem {
    border-radius: 0.8rem;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-default {
    cursor: default !important;
}

.text-ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.text-align-center {
    text-align: center;
}

.text-align-end {
    text-align: end;
}

.word-break-break-all {
    word-break: break-all;
}

.word-break-break-word {
    word-break: break-word;
}

// background

.bg-light-gray {
    background-color: var(--gray-900);
}

.bg-light-blue {
    background-color: var(--blue-700);
}

.bg-light-blue-details {
    background-color: var(--blue-500);
}

.background-none {
    background: none;
}

// overflow
.overflow-hidden {
    overflow: hidden;
}

.overflow-auto {
    overflow: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.overflow-x-auto {
    overflow-x: auto;
}

.overflow-y-auto {
    overflow-y: auto;
}

.no-scroll {
    overflow: hidden;
}

.rotate-90 {
    transform: rotate(90deg);
}

.rotate-180 {
    transform: rotate(180deg);
}

.rotate-270 {
    transform: rotate(270deg);
}

.rotate-360 {
    transform: rotate(360deg);
}

.opacity-0 {
    opacity: 0;
}

.opacity-1 {
    opacity: 1;
}
