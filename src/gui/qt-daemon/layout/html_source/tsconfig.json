{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "paths": {"@store/*": ["src/app/store/*"], "@parts/*": ["src/app/parts/*"], "@api/*": ["src/app/api/*"]}, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "experimentalDecorators": true, "target": "es2018", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "resolveJsonModule": true, "esModuleInterop": true}}