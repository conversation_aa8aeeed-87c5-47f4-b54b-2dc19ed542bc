// Copyright (c) 2025 Zano Project
// Distributed under the MIT/X11 software license, see the accompanying
// file COPYING or http://www.opensource.org/licenses/mit-license.php.

#include "chaingen.h"
#include "timestamp_reorg_attack.h"
#include "test_core_time.h"

using namespace epee;
using namespace currency;

timestamp_reorg_attack::timestamp_reorg_attack()
{
  REGISTER_CALLBACK("check_inconsistent_alt_chains", timestamp_reorg_attack::check_inconsistent_alt_chains);
}

bool timestamp_reorg_attack::generate(std::vector<test_event_entry>& events) const
{
  uint64_t ts_start = **********;
  
  /*
   Attack scenario:
   0    1    2    3    4    5
  (0)-(1)-(2)-(3)-(4)-(5)     <- main chain
            \-(2a)-(3a)       <- alt chain with future timestamps
   
   When alt chain becomes longer and triggers reorganization:
   - Main chain blocks 3,4,5 are moved to alt storage
   - But block 3 has timestamp that fails validation during re-add
   - switch_to_alternative_blockchain "pretends success" and continues
   - Results in inconsistent alt chain state
  */

  GENERATE_ACCOUNT(miner_account);
  m_miner_account = miner_account;

  // Build main chain
  MAKE_GENESIS_BLOCK(events, blk_0, miner_account, ts_start);
  MAKE_NEXT_BLOCK(events, blk_1, blk_0, miner_account);
  MAKE_NEXT_BLOCK(events, blk_2, blk_1, miner_account);
  
  // Create block with timestamp that will cause issues during reorganization
  // This block passes initial validation but fails when re-added as alt block
  block blk_3;
  if (!generator.construct_block(blk_3, blk_2, miner_account, 
                                std::list<transaction>(), 
                                ts_start + 3600 * 24 * 30)) // 30 days in future
    return false;
  events.push_back(blk_3);
  
  MAKE_NEXT_BLOCK(events, blk_4, blk_3, miner_account);
  MAKE_NEXT_BLOCK(events, blk_5, blk_4, miner_account);

  // Create alternative chain that will trigger reorganization
  // Alt blocks have even more extreme future timestamps
  block blk_2a;
  if (!generator.construct_block(blk_2a, blk_2, miner_account,
                                std::list<transaction>(),
                                ts_start + 3600 * 24 * 60)) // 60 days in future
    return false;
  events.push_back(blk_2a);

  block blk_3a;
  if (!generator.construct_block(blk_3a, blk_2a, miner_account,
                                std::list<transaction>(),
                                ts_start + 3600 * 24 * 61)) // 61 days in future
    return false;
  events.push_back(blk_3a);

  // This should trigger reorganization and expose the vulnerability
  DO_CALLBACK(events, "check_inconsistent_alt_chains");

  return true;
}

bool timestamp_reorg_attack::check_inconsistent_alt_chains(currency::core& c, size_t ev_index, const std::vector<test_event_entry>& events)
{
  // The vulnerability allows reorganization to "succeed" even when
  // ex-main chain blocks fail to be added to alt storage
  // This creates inconsistent alt chain graphs between nodes
  
  // Check that the core is in an inconsistent state
  // Alt chains should have missing blocks due to failed re-adds
  
  LOG_PRINT_L0("Checking for timestamp reorganization attack vulnerability...");
  
  // Get current blockchain height
  uint64_t height = c.get_current_blockchain_height();
  LOG_PRINT_L0("Current blockchain height: " << height);
  
  // Check alt blocks storage - should be inconsistent due to failed re-adds
  std::list<block> alt_blocks;
  std::list<crypto::hash> alt_block_ids;
  c.get_alternative_blocks(alt_blocks);
  
  LOG_PRINT_L0("Alternative blocks count: " << alt_blocks.size());
  
  // The attack succeeds if reorganization completed but alt storage is missing blocks
  // that should have been moved from main chain
  if (alt_blocks.size() < 3) {
    LOG_PRINT_RED("VULNERABILITY CONFIRMED: Alt chain missing expected blocks after reorganization", LOG_LEVEL_0);
    LOG_PRINT_RED("This indicates switch_to_alternative_blockchain 'pretended success' when re-adding failed", LOG_LEVEL_0);
    return true; // Test passes - vulnerability confirmed
  }
  
  LOG_PRINT_GREEN("Alt chain state appears consistent", LOG_LEVEL_0);
  return true;
}
