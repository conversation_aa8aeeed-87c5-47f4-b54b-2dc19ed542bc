// Copyright (c) 2025 Zano Project
// Distributed under the MIT/X11 software license, see the accompanying
// file COPYING or http://www.opensource.org/licenses/mit-license.php.

#pragma once 
#include "chaingen.h"

/************************************************************************/
/* PoC test for timestamp reorganization attack vulnerability          */
/* Tests the "pretend success" behavior in switch_to_alternative_blockchain */
/************************************************************************/

struct timestamp_reorg_attack : public test_chain_unit_enchanced
{
  timestamp_reorg_attack();
  bool generate(std::vector<test_event_entry>& events) const;
  bool check_inconsistent_alt_chains(currency::core& c, size_t ev_index, const std::vector<test_event_entry>& events);

private:
  mutable currency::account_base m_miner_account;
};
